10:38:25.739 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 14761 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by xt in /home/<USER>/nl-mes/iot-all)
10:38:25.743 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:38:26.834 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
10:38:26.834 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:38:26.835 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:38:26.879 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:38:28.922 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:38:29.014 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:38:29.384 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:38:29.385 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:38:29.773 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:38:29.815 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:38:29.817 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:38:29.942 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:38:29.950 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:38:29.954 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:38:29.955 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:38:29.955 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:38:29.956 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:38:29.956 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:38:29.956 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:38:29.957 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:38:29.960 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:38:30.057 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
10:38:30.110 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.032 seconds (JVM running for 4.537)
10:40:48.977 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:43:42.467 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
10:43:42.468 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
10:43:42.472 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
10:43:42.472 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
10:47:15.826 [http-nio-9335-exec-9] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 18
10:54:26.068 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
10:54:26.073 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
10:54:30.407 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 19022 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by xt in /home/<USER>/nl-mes/iot-all)
10:54:30.409 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:54:31.272 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
10:54:31.273 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:54:31.273 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:54:31.307 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:54:32.311 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:54:32.408 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:54:32.738 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:54:32.739 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:54:33.079 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:54:33.109 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:54:33.111 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:54:33.191 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:54:33.196 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:54:33.199 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:54:33.199 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:54:33.200 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:54:33.200 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:54:33.200 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:54:33.200 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:54:33.201 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:54:33.202 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:54:33.252 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
10:54:33.289 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.229 seconds (JVM running for 3.674)
10:54:37.713 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:54:37.855 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 18
10:54:37.864 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,116] - 查询到检验记录数据 0 条
10:54:38.272 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 18，文档大小: 2386 bytes
10:54:38.274 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756781678273.docx, 大小: 2386 bytes
10:56:19.612 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 18
10:56:19.620 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,116] - 查询到检验记录数据 0 条
10:56:19.638 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 18，文档大小: 2386 bytes
10:56:19.639 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756781779639.docx, 大小: 2386 bytes
10:56:35.399 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
10:56:35.404 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
10:56:39.636 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 20593 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by xt in /home/<USER>/nl-mes/iot-all)
10:56:39.640 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:56:41.044 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
10:56:41.045 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:56:41.045 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:56:41.099 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:56:42.680 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:56:42.846 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:56:43.317 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:56:43.319 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:56:43.920 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:56:43.987 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:56:43.989 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:56:44.097 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:56:44.103 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:56:44.107 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:56:44.108 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:56:44.108 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:56:44.109 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:56:44.109 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:56:44.110 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:56:44.111 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:56:44.113 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:56:44.203 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
10:56:44.235 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 5.259 seconds (JVM running for 6.496)
10:56:47.124 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:56:47.181 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 18
10:57:25.088 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,116] - 查询到检验记录数据 0 条
10:57:25.669 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 18，文档大小: 2385 bytes
10:57:25.672 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756781845669.docx, 大小: 2385 bytes
10:58:13.226 [http-nio-9335-exec-3] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 18
10:58:17.700 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,116] - 查询到检验记录数据 3 条
10:58:27.247 [http-nio-9335-exec-3] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 18，文档大小: 2684 bytes
10:58:27.247 [http-nio-9335-exec-3] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756781907247.docx, 大小: 2684 bytes
11:55:36.683 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
11:55:36.691 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
12:08:53.291 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 40854 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
12:08:53.293 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
12:08:54.509 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
12:08:54.509 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:08:54.510 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
12:08:54.563 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:08:55.834 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
12:08:55.951 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
12:08:56.306 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
12:08:56.307 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
12:08:56.677 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
12:08:56.722 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
12:08:56.724 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
12:08:56.833 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
12:08:56.840 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
12:08:56.844 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
12:08:56.844 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
12:08:56.845 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
12:08:56.845 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
12:08:56.845 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
12:08:56.845 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
12:08:56.846 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
12:08:56.848 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
12:08:56.922 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
12:08:56.953 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.171 seconds (JVM running for 5.786)
12:09:21.559 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:09:56.193 [http-nio-9335-exec-10] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 18
12:09:56.205 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,121] - 查询到检验记录数据 3 条
12:09:56.208 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 18, 检验记录数量: 3
12:09:56.213 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [parseExistingTableData,639] - 解析现有表格数据成功，共 1 行
12:09:56.214 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,578] - 根据绑定关系替换表格数据，行模板绑定数量: 3, 检验记录数量: 3
12:09:56.215 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,627] - 构建数据行完成，共 3 行
12:09:56.217 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
12:09:56.494 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
12:09:56.495 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
12:09:56.570 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
12:09:56.570 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
12:09:56.646 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
12:09:56.647 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
12:09:56.650 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
12:09:56.654 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
12:09:56.656 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
12:09:56.658 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
12:09:56.665 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
12:09:56.667 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
12:09:56.668 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
12:09:56.669 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
12:09:56.671 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
12:09:56.672 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
12:09:56.672 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
12:09:56.674 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
12:09:56.793 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2944 bytes
12:09:56.799 [http-nio-9335-exec-10] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 18，文档大小: 2944 bytes
12:09:56.802 [http-nio-9335-exec-10] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756786196799.docx, 大小: 2944 bytes
12:10:22.457 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
12:10:22.457 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
12:10:22.461 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
12:10:22.461 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
12:12:36.880 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
12:12:36.884 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
12:12:39.680 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 42959 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
12:12:39.682 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
12:12:40.961 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
12:12:40.962 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:12:40.963 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
12:12:41.019 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:12:42.391 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
12:12:42.497 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
12:12:42.903 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
12:12:42.905 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
12:12:43.333 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
12:12:43.366 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
12:12:43.367 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
12:12:43.477 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
12:12:43.484 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
12:12:43.487 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
12:12:43.488 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
12:12:43.488 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
12:12:43.488 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
12:12:43.489 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
12:12:43.489 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
12:12:43.490 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
12:12:43.491 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
12:12:43.545 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
12:12:43.567 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.478 seconds (JVM running for 5.37)
12:12:47.035 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:12:47.573 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
12:12:47.574 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
12:12:47.579 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
12:12:47.579 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
12:25:49.732 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
12:25:49.737 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
12:25:54.366 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 46080 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
12:25:54.367 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
12:25:55.391 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
12:25:55.392 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:25:55.392 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
12:25:55.437 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:25:56.562 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
12:25:56.653 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
12:25:56.974 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
12:25:56.975 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
12:25:57.383 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
12:25:57.417 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
12:25:57.419 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
12:25:57.506 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
12:25:57.513 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
12:25:57.516 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
12:25:57.517 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
12:25:57.517 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
12:25:57.517 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
12:25:57.517 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
12:25:57.517 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
12:25:57.518 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
12:25:57.520 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
12:25:57.576 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
12:25:57.595 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.671 seconds (JVM running for 4.329)
12:26:20.113 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:26:20.148 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 18
12:26:20.161 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,121] - 查询到检验记录数据 3 条
12:26:20.245 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 18, 检验记录数量: 3
12:26:20.254 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseExistingTableData,675] - 解析现有表格数据成功，共 1 行
12:26:20.256 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,578] - 根据绑定关系替换表格数据，行模板绑定数量: 3, 检验记录数量: 3
12:26:20.257 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,663] - 构建数据行完成，共 3 行
12:26:20.259 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
12:26:20.510 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
12:26:20.510 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
12:26:20.564 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
12:26:20.564 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
12:26:20.623 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
12:26:20.624 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
12:26:20.628 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
12:26:20.631 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
12:26:20.632 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
12:26:20.634 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
12:26:20.638 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
12:26:20.640 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
12:26:20.640 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
12:26:20.641 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
12:26:20.642 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
12:26:20.643 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
12:26:20.644 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
12:26:20.646 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
12:26:20.732 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2943 bytes
12:26:20.737 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 18，文档大小: 2943 bytes
12:26:20.739 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756787180737.docx, 大小: 2943 bytes
12:31:18.526 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
12:31:18.531 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
12:31:23.145 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 47670 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
12:31:23.147 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
12:31:24.161 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
12:31:24.161 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:31:24.162 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
12:31:24.203 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:31:25.273 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
12:31:25.368 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
12:31:25.710 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
12:31:25.712 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
12:31:26.090 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
12:31:26.146 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
12:31:26.148 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
12:31:26.230 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
12:31:26.235 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
12:31:26.238 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
12:31:26.239 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
12:31:26.239 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
12:31:26.239 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
12:31:26.239 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
12:31:26.239 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
12:31:26.240 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
12:31:26.242 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
12:31:26.289 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
12:31:26.308 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.628 seconds (JVM running for 4.317)
12:31:31.070 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:31:31.108 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 18
12:31:31.121 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,121] - 查询到检验记录数据 3 条
12:31:31.206 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 18, 检验记录数量: 3
12:31:31.212 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseExistingTableData,695] - 解析现有表格数据成功，共 1 行
12:31:31.213 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,578] - 根据绑定关系替换表格数据，行模板绑定数量: 3, 检验记录数量: 3
12:31:31.214 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,683] - 构建数据行完成，共 3 行
12:31:31.216 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
12:31:31.445 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
12:31:31.446 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
12:31:31.512 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
12:31:31.512 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
12:31:31.613 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
12:31:31.614 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
12:31:31.617 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
12:31:31.621 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
12:31:31.622 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
12:31:31.625 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
12:31:31.632 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
12:31:31.633 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
12:31:31.635 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
12:31:31.635 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
12:31:31.637 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
12:31:31.639 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
12:31:31.640 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
12:31:31.643 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
12:31:31.789 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2943 bytes
12:31:31.795 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 18，文档大小: 2943 bytes
12:31:31.798 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756787491795.docx, 大小: 2943 bytes
12:34:43.648 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 18
12:34:43.655 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,121] - 查询到检验记录数据 3 条
12:34:43.655 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 18, 检验记录数量: 3
12:34:43.656 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [parseExistingTableData,695] - 解析现有表格数据成功，共 1 行
12:37:18.322 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,578] - 根据绑定关系替换表格数据，行模板绑定数量: 3, 检验记录数量: 3
12:37:18.325 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,683] - 构建数据行完成，共 3 行
12:37:18.326 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
12:37:18.328 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
12:37:18.329 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
12:37:18.331 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
12:37:18.331 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
12:37:18.337 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
12:37:18.338 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
12:37:18.339 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
12:37:18.340 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
12:37:18.341 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
12:37:18.341 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
12:37:18.342 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
12:37:18.344 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
12:37:18.345 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
12:37:18.345 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
12:37:18.346 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
12:37:18.347 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
12:37:18.348 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
12:37:18.350 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
12:37:18.361 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2944 bytes
12:37:18.366 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 18，文档大小: 2944 bytes
12:37:18.366 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756787838366.docx, 大小: 2944 bytes
12:37:18.575 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
12:37:18.580 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
13:46:22.886 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 63852 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
13:46:22.888 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
13:46:24.164 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
13:46:24.165 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:46:24.165 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
13:46:24.210 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:46:25.348 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
13:46:25.462 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
13:46:25.840 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
13:46:25.841 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
13:46:26.320 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
13:46:26.359 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
13:46:26.361 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
13:46:26.458 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
13:46:26.465 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
13:46:26.468 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
13:46:26.469 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
13:46:26.469 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
13:46:26.469 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
13:46:26.470 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
13:46:26.470 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
13:46:26.471 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
13:46:26.473 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
13:46:26.530 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
13:46:26.556 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.153 seconds (JVM running for 5.427)
13:46:44.327 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:46:56.482 [http-nio-9335-exec-5] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 18
13:46:56.496 [http-nio-9335-exec-5] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,123] - 查询到检验记录数据 3 条
13:46:56.498 [http-nio-9335-exec-5] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 18, 检验记录数量: 3
13:46:56.501 [http-nio-9335-exec-5] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,712] - 构建数据行完成，共 3 行
13:46:56.503 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
13:46:56.766 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
13:46:56.767 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
13:46:56.828 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
13:46:56.828 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
13:46:56.895 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
13:46:56.895 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
13:46:56.899 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
13:46:56.901 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
13:46:56.902 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
13:46:56.904 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
13:46:56.909 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
13:46:56.910 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
13:46:56.910 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
13:46:56.910 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
13:46:56.912 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
13:46:56.912 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
13:46:56.913 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
13:46:56.915 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
13:46:57.002 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2970 bytes
13:46:57.006 [http-nio-9335-exec-5] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 18，文档大小: 2970 bytes
13:46:57.008 [http-nio-9335-exec-5] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756792017007.docx, 大小: 2970 bytes
13:48:24.148 [http-nio-9335-exec-9] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 18
13:48:24.154 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,123] - 查询到检验记录数据 3 条
13:48:24.155 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 18, 检验记录数量: 3
13:49:50.786 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,712] - 构建数据行完成，共 3 行
13:49:50.787 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
13:49:50.791 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
13:49:50.791 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
13:49:50.793 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
13:49:50.793 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
13:49:50.799 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
13:49:50.800 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
13:49:50.801 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
13:49:50.802 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
13:49:50.803 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
13:49:50.805 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
13:49:50.806 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
13:49:50.807 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
13:49:50.808 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
13:49:50.808 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
13:49:50.808 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
13:49:50.809 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
13:49:50.809 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
13:49:50.810 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
13:49:50.816 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2970 bytes
13:49:50.820 [http-nio-9335-exec-9] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 18，文档大小: 2970 bytes
13:49:50.821 [http-nio-9335-exec-9] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756792190821.docx, 大小: 2970 bytes
14:15:12.838 [http-nio-9335-exec-5] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 18
14:15:12.846 [http-nio-9335-exec-5] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,123] - 查询到检验记录数据 3 条
14:15:12.847 [http-nio-9335-exec-5] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 18, 检验记录数量: 3
14:15:12.848 [http-nio-9335-exec-5] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,712] - 构建数据行完成，共 3 行
14:15:12.848 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
14:15:12.850 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
14:15:12.850 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
14:15:12.852 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
14:15:12.853 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
14:15:12.860 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
14:15:12.860 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
14:15:12.861 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
14:15:12.863 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
14:15:12.864 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
14:15:12.865 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:15:12.866 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:15:12.867 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:15:12.868 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:15:12.868 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
14:15:12.868 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
14:15:12.869 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:15:12.869 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:15:12.870 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:15:12.877 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2969 bytes
14:15:12.880 [http-nio-9335-exec-5] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 18，文档大小: 2969 bytes
14:15:12.881 [http-nio-9335-exec-5] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756793712881.docx, 大小: 2969 bytes
14:15:33.985 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
14:15:33.989 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
14:15:39.241 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 71090 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
14:15:39.243 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:15:40.280 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
14:15:40.281 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:15:40.281 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:15:40.320 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:15:41.381 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:15:41.482 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:15:41.802 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
14:15:41.803 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
14:15:42.165 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
14:15:42.191 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
14:15:42.192 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
14:15:42.269 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
14:15:42.273 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
14:15:42.277 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
14:15:42.277 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
14:15:42.278 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
14:15:42.278 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
14:15:42.278 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
14:15:42.278 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
14:15:42.279 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
14:15:42.281 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
14:15:42.326 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
14:15:42.344 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.569 seconds (JVM running for 4.275)
14:15:50.666 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:15:50.700 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 18
14:15:50.713 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,123] - 查询到检验记录数据 3 条
14:15:50.795 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 18, 检验记录数量: 3
14:15:50.803 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,730] - 构建数据行完成，共 3 行
14:15:50.806 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
14:15:51.037 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
14:15:51.037 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
14:15:51.096 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
14:15:51.097 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
14:15:51.161 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
14:15:51.162 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
14:15:51.166 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
14:15:51.168 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
14:15:51.170 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
14:15:51.171 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:15:51.175 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:15:51.177 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:15:51.178 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:15:51.178 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
14:15:51.180 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
14:15:51.181 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:15:51.181 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:15:51.182 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:15:51.267 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3014 bytes
14:15:51.272 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 18，文档大小: 3014 bytes
14:15:51.275 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756793751273.docx, 大小: 3014 bytes
14:17:40.910 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 18
14:17:40.917 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,123] - 查询到检验记录数据 3 条
14:17:40.918 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 18, 检验记录数量: 3
14:17:40.918 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,730] - 构建数据行完成，共 3 行
14:17:40.919 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
14:17:40.921 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
14:17:40.921 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
14:17:40.923 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
14:17:40.923 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
14:17:40.929 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
14:17:40.930 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
14:17:40.931 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
14:17:40.932 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
14:17:40.933 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
14:17:40.934 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:17:40.936 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:17:40.937 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:17:40.938 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:17:40.939 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
14:17:40.939 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
14:17:40.940 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:17:40.941 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:17:40.942 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:17:40.949 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3002 bytes
14:17:40.952 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 18，文档大小: 3002 bytes
14:17:40.952 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756793860952.docx, 大小: 3002 bytes
14:18:53.570 [http-nio-9335-exec-7] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 18
14:18:53.576 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,123] - 查询到检验记录数据 3 条
14:18:53.576 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 18, 检验记录数量: 3
14:18:53.577 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,730] - 构建数据行完成，共 3 行
14:18:53.577 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
14:18:53.578 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
14:18:53.578 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
14:18:53.580 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
14:18:53.581 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
14:18:53.587 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
14:18:53.587 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
14:18:53.588 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
14:18:53.588 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
14:18:53.589 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
14:18:53.590 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:18:53.591 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:18:53.592 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:18:53.593 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:18:53.593 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
14:18:53.594 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
14:18:53.594 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:18:53.595 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:18:53.595 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:18:53.601 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3019 bytes
14:18:53.604 [http-nio-9335-exec-7] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 18，文档大小: 3019 bytes
14:18:53.605 [http-nio-9335-exec-7] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756793933604.docx, 大小: 3019 bytes
15:48:48.591 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 92628 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:48:48.593 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:48:49.552 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:48:49.552 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:48:49.553 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:48:49.588 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:48:50.558 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:48:50.643 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:48:50.924 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:48:50.925 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:48:51.250 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:48:51.297 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:48:51.297 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:48:51.361 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:48:51.367 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:48:51.369 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:48:51.370 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:48:51.370 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:48:51.370 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:48:51.370 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:48:51.371 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:48:51.371 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:48:51.372 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:48:51.418 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:48:51.535 [main] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:48:51.540 [main] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:48:51.543 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9335"]
15:48:51.543 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
15:48:51.546 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9335"]
15:48:51.547 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9335"]
15:49:06.986 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 92957 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:49:06.989 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:49:07.809 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:49:07.809 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:49:07.810 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:49:07.841 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:49:08.828 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:49:08.910 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:49:09.165 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:49:09.166 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:49:09.414 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:49:09.435 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:49:09.436 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:49:09.494 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:49:09.498 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:49:09.500 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:49:09.501 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:49:09.501 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:49:09.501 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:49:09.501 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:49:09.501 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:49:09.501 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:49:09.503 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:49:09.543 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:49:09.556 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 2.995 seconds (JVM running for 3.382)
15:49:45.984 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:50:44.378 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
15:50:44.380 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
15:50:44.385 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
15:50:44.386 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
15:51:56.840 [http-nio-9335-exec-10] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 17
15:51:56.841 [http-nio-9335-exec-10] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
15:51:56.841 [http-nio-9335-exec-10] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 17
15:51:56.845 [http-nio-9335-exec-10] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
15:51:56.846 [http-nio-9335-exec-10] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
15:51:56.857 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
15:51:57.159 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
15:51:57.160 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 6, 总列数: 8
15:51:57.245 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
15:51:57.246 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
15:51:57.313 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
15:51:57.313 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
15:51:57.317 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
15:51:57.319 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
15:51:57.321 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
15:51:57.323 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
15:51:57.324 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:51:57.328 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:51:57.329 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:51:57.329 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:51:57.329 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
15:51:57.331 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
15:51:57.332 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:51:57.332 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:51:57.333 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:51:57.419 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3036 bytes
15:51:57.426 [http-nio-9335-exec-10] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC17%E9%A1%B5_20250902_155157.docx, 大小: 3036 bytes
15:52:50.361 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 19
15:52:50.371 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,123] - 查询到检验记录数据 1 条
15:52:50.372 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 19, 检验记录数量: 1
15:52:50.374 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
15:52:50.375 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
15:52:50.377 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
15:52:50.377 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:52:50.378 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
15:52:50.378 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
15:52:50.383 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
15:52:50.383 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 450px (6750twips)
15:52:50.384 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
15:52:50.384 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:52:50.385 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:52:50.386 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:52:50.386 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:52:50.387 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
15:52:50.387 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
15:52:50.387 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:52:50.387 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:52:50.388 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:52:50.394 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2888 bytes
15:52:50.397 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 19，文档大小: 2888 bytes
15:52:50.398 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756799570398.docx, 大小: 2888 bytes
15:53:46.236 [http-nio-9335-exec-6] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 20
15:53:46.244 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,123] - 查询到检验记录数据 1 条
15:53:46.245 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 20, 检验记录数量: 1
15:53:46.246 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
15:53:46.246 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
15:53:46.247 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
15:53:46.247 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:53:46.248 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
15:53:46.249 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
15:53:46.252 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
15:53:46.253 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 480px (7200twips)
15:53:46.255 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
15:53:46.256 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:53:46.257 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:53:46.257 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:53:46.258 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:53:46.258 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
15:53:46.259 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
15:53:46.259 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:53:46.260 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:53:46.261 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:53:46.267 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2889 bytes
15:53:46.272 [http-nio-9335-exec-6] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 20，文档大小: 2889 bytes
15:53:46.272 [http-nio-9335-exec-6] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756799626272.docx, 大小: 2889 bytes
15:54:30.576 [http-nio-9335-exec-9] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 20
15:54:30.581 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,123] - 查询到检验记录数据 1 条
15:54:30.582 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 20, 检验记录数量: 1
15:54:30.583 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
15:54:30.583 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
15:54:30.584 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
15:54:30.584 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:54:30.585 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
15:54:30.585 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
15:54:30.588 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
15:54:30.588 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 480px (7200twips)
15:54:30.589 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
15:54:30.589 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:54:30.590 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:54:30.590 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:54:30.591 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:54:30.591 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
15:54:30.591 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
15:54:30.592 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:54:30.592 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:54:30.593 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:54:30.597 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2889 bytes
15:54:30.600 [http-nio-9335-exec-9] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 20，文档大小: 2889 bytes
15:54:30.600 [http-nio-9335-exec-9] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756799670600.docx, 大小: 2889 bytes
15:55:15.322 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
15:55:15.323 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
15:55:15.324 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
15:55:15.325 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
15:56:03.394 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 19
15:56:03.400 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,123] - 查询到检验记录数据 1 条
15:56:03.401 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 19, 检验记录数量: 1
15:56:03.401 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
15:56:03.402 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
15:56:03.402 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
15:56:03.403 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:56:03.404 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
15:56:03.404 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
15:56:03.406 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
15:56:03.407 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 520px (7800twips)
15:56:03.407 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
15:56:03.408 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:56:03.408 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:56:03.408 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:56:03.409 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:56:03.409 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
15:56:03.409 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
15:56:03.409 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:56:03.409 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:56:03.410 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:56:03.414 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2890 bytes
15:56:03.417 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 19，文档大小: 2890 bytes
15:56:03.417 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756799763417.docx, 大小: 2890 bytes
15:56:40.661 [http-nio-9335-exec-7] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 19
15:56:40.667 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,123] - 查询到检验记录数据 1 条
15:56:40.668 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 19, 检验记录数量: 1
15:56:40.669 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
15:56:40.669 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
15:56:40.670 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
15:56:40.671 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:56:40.671 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
15:56:40.672 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
15:56:40.673 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
15:56:40.674 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
15:56:40.674 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
15:56:40.674 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:56:40.675 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:56:40.675 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:56:40.675 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:56:40.676 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
15:56:40.676 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
15:56:40.676 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:56:40.676 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:56:40.677 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:56:40.681 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2889 bytes
15:56:40.684 [http-nio-9335-exec-7] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 19，文档大小: 2889 bytes
15:56:40.684 [http-nio-9335-exec-7] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756799800684.docx, 大小: 2889 bytes
19:21:16.979 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
19:21:16.983 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
19:23:07.909 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 141092 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
19:23:07.911 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
19:23:08.739 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
19:23:08.739 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:23:08.740 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
19:23:08.773 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:23:09.674 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
19:23:09.758 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
19:23:10.036 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
19:23:10.037 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
19:23:10.360 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
19:23:10.379 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
19:23:10.380 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
19:23:10.451 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
19:23:10.455 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
19:23:10.458 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
19:23:10.458 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
19:23:10.458 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
19:23:10.458 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
19:23:10.458 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
19:23:10.458 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
19:23:10.459 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
19:23:10.461 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
19:23:10.504 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
19:23:10.518 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 2.928 seconds (JVM running for 3.277)
19:24:26.914 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:25:49.369 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
19:25:49.373 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
19:25:52.457 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 142204 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
19:25:52.459 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
19:25:53.369 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
19:25:53.370 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:25:53.370 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
19:25:53.403 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:25:54.264 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
19:25:54.336 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
19:25:54.630 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
19:25:54.631 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
19:25:54.923 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
19:25:54.943 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
19:25:54.944 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
19:25:55.004 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
19:25:55.008 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
19:25:55.011 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
19:25:55.011 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
19:25:55.011 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
19:25:55.011 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
19:25:55.011 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
19:25:55.012 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
19:25:55.012 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
19:25:55.013 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
19:25:55.055 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
19:25:55.068 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 2.979 seconds (JVM running for 3.316)
19:25:59.545 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:37:46.270 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
19:37:46.274 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
19:37:49.384 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 144977 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
19:37:49.387 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
19:37:50.226 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
19:37:50.226 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:37:50.226 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
19:37:50.259 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:37:51.147 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
19:37:51.226 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
19:37:51.492 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
19:37:51.493 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
19:37:51.751 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
19:37:51.774 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
19:37:51.775 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
19:37:51.838 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
19:37:51.842 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
19:37:51.845 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
19:37:51.846 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
19:37:51.846 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
19:37:51.846 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
19:37:51.846 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
19:37:51.846 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
19:37:51.847 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
19:37:51.848 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
19:37:51.891 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
19:37:51.906 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 2.905 seconds (JVM running for 3.228)
19:37:57.213 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:38:48.682 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 21
19:38:48.686 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 21, 检验记录数量: 0
19:38:48.755 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
19:38:48.757 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
19:38:48.906 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
19:38:48.907 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
19:38:48.948 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
19:38:48.984 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
19:38:48.989 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
19:38:48.992 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2042] - 应用JSON格式数据行合并单元格，数量: 7，表头偏移: 2
19:38:48.993 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonDataMerge,2111] - 数据行合并使用相对索引，加上表头偏移: 0 -> 2
19:38:48.994 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonDataMerge,2129] - 应用数据行合并: 行[2-2], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
19:38:48.994 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行2, 列3-4, 跨度2列
19:38:48.995 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行2, 列3-4
19:38:49.052 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2852 bytes
19:38:49.056 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 21，文档大小: 2852 bytes
19:38:49.057 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756813129056.docx, 大小: 2852 bytes
19:39:39.786 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 18
19:39:39.795 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,123] - 查询到检验记录数据 3 条
19:39:39.797 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 18, 检验记录数量: 3
19:39:39.799 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 3 行
19:39:39.800 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
19:39:39.801 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
19:39:39.801 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
19:39:39.807 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
19:39:39.807 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
19:39:39.816 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
19:39:39.816 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
19:39:39.817 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
19:39:39.819 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
19:39:39.820 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
19:39:39.821 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
19:39:39.823 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
19:39:39.823 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
19:39:39.823 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
19:39:39.824 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
19:39:39.824 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
19:39:39.824 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
19:39:39.825 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
19:39:39.825 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
19:39:39.833 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3018 bytes
19:39:39.836 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 18，文档大小: 3018 bytes
19:39:39.836 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756813179836.docx, 大小: 3018 bytes
19:44:12.522 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
19:44:12.526 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
19:44:15.765 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 146683 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
19:44:15.767 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
19:44:16.579 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
19:44:16.580 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:44:16.580 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
19:44:16.615 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:44:17.470 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
19:44:17.542 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
19:44:17.807 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
19:44:17.808 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
19:44:18.085 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
19:44:18.105 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
19:44:18.106 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
19:44:18.166 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
19:44:18.169 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
19:44:18.171 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
19:44:18.172 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
19:44:18.172 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
19:44:18.172 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
19:44:18.172 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
19:44:18.172 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
19:44:18.173 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
19:44:18.174 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
19:44:18.214 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
19:44:18.227 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 2.781 seconds (JVM running for 3.093)
19:44:26.900 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:44:50.047 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 22
19:44:50.052 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 22, 检验记录数量: 0
19:44:50.111 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
19:44:50.112 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
19:44:50.257 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
19:44:50.258 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
19:44:50.298 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
19:44:50.334 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
19:44:50.338 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
19:44:50.341 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
19:44:50.342 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
19:44:50.345 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
19:44:50.346 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
19:44:50.347 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
19:44:50.347 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
19:44:50.348 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
19:44:50.349 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
19:44:50.349 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
19:44:50.351 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
19:44:50.401 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2858 bytes
19:44:50.405 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 22，文档大小: 2858 bytes
19:44:50.406 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756813490405.docx, 大小: 2858 bytes
19:52:42.402 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
19:52:42.405 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
19:52:45.634 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 148743 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
19:52:45.636 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
19:52:46.499 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
19:52:46.499 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:52:46.500 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
19:52:46.532 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:52:47.433 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
19:52:47.503 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
19:52:47.778 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
19:52:47.778 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
19:52:48.039 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
19:52:48.060 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
19:52:48.061 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
19:52:48.127 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
19:52:48.131 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
19:52:48.133 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
19:52:48.134 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
19:52:48.134 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
19:52:48.134 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
19:52:48.134 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
19:52:48.134 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
19:52:48.135 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
19:52:48.136 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
19:52:48.180 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
19:52:48.196 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 2.882 seconds (JVM running for 3.202)
19:52:56.998 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:53:06.489 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 23
19:53:06.499 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 23, 检验记录数量: 0
19:53:06.629 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
19:53:06.633 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
19:53:06.844 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
19:53:06.844 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
19:53:06.891 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
19:53:06.933 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
19:53:06.939 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
19:53:06.944 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
19:53:06.945 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
19:53:06.951 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
19:53:06.952 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
19:53:06.953 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
19:53:06.954 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
19:53:06.955 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
19:53:06.957 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
19:53:06.957 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
19:53:06.959 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
19:53:07.020 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2857 bytes
19:53:07.024 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 23，文档大小: 2857 bytes
19:53:07.026 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756813987024.docx, 大小: 2857 bytes
19:55:30.589 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
19:55:30.592 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
19:55:33.787 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 149590 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
19:55:33.790 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
19:55:34.601 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
19:55:34.602 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:55:34.602 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
19:55:34.634 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:55:35.516 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
19:55:35.592 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
19:55:35.857 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
19:55:35.858 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
19:55:36.162 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
19:55:36.188 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
19:55:36.189 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
19:55:36.284 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
19:55:36.289 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
19:55:36.292 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
19:55:36.293 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
19:55:36.293 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
19:55:36.293 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
19:55:36.294 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
19:55:36.294 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
19:55:36.295 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
19:55:36.297 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
19:55:36.346 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
19:55:36.371 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 2.904 seconds (JVM running for 3.196)
19:56:24.578 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:56:33.039 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 24
19:56:33.042 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 24, 检验记录数量: 0
19:56:33.102 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
19:56:33.103 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
19:56:33.259 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
19:56:33.259 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
19:56:33.303 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
19:56:33.343 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
19:56:33.348 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
19:56:33.351 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
19:56:33.352 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
19:56:33.355 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
19:56:33.356 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
19:56:33.356 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
19:56:33.356 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
19:56:33.357 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
19:56:33.358 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
19:56:33.359 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
19:56:33.360 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
19:56:33.427 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2857 bytes
19:56:33.432 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 24，文档大小: 2857 bytes
19:56:33.433 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756814193432.docx, 大小: 2857 bytes
19:59:04.678 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
19:59:04.682 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
19:59:08.187 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 150593 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
19:59:08.189 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
19:59:09.023 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
19:59:09.024 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:59:09.024 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
19:59:09.057 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:59:09.934 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
19:59:10.013 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
19:59:10.300 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
19:59:10.301 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
19:59:10.602 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
19:59:10.624 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
19:59:10.625 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
19:59:10.691 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
19:59:10.695 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
19:59:10.698 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
19:59:10.698 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
19:59:10.698 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
19:59:10.698 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
19:59:10.698 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
19:59:10.699 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
19:59:10.699 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
19:59:10.701 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
19:59:10.746 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
19:59:10.761 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 2.922 seconds (JVM running for 3.249)
19:59:14.152 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:59:21.836 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 25
19:59:21.840 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 25, 检验记录数量: 0
20:01:49.856 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
20:01:49.860 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
20:01:53.705 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 151512 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
20:01:53.707 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
20:01:54.578 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
20:01:54.579 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:01:54.579 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
20:01:54.612 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:01:55.552 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
20:01:55.625 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
20:01:55.899 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
20:01:55.900 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
20:01:56.162 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
20:01:56.187 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
20:01:56.188 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
20:01:56.252 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
20:01:56.257 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
20:01:56.259 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
20:01:56.259 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
20:01:56.260 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
20:01:56.260 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
20:01:56.260 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
20:01:56.260 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
20:01:56.261 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
20:01:56.262 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
20:01:56.302 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
20:01:56.316 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 2.975 seconds (JVM running for 3.318)
20:02:02.535 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:02:06.849 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 26
20:02:06.853 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 26, 检验记录数量: 0
20:02:06.915 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
20:02:06.916 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
20:02:07.067 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
20:02:07.067 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
20:02:07.110 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
20:02:07.110 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
20:02:07.154 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
20:02:07.154 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
20:02:07.156 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
20:02:07.157 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
20:02:07.160 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
20:02:07.160 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
20:02:07.161 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
20:02:07.161 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
20:02:07.162 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
20:02:07.163 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
20:02:07.163 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
20:02:07.164 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
20:02:07.218 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2881 bytes
20:02:07.223 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 26，文档大小: 2881 bytes
20:02:07.224 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756814527223.docx, 大小: 2881 bytes
20:26:14.712 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
20:26:14.717 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
20:26:16.804 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 156747 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
20:26:16.806 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
20:26:17.849 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
20:26:17.850 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:26:17.850 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
20:26:17.892 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:26:18.886 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
20:26:18.979 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
20:26:19.283 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
20:26:19.284 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
20:26:19.619 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
20:26:19.650 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
20:26:19.651 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
20:26:19.748 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
20:26:19.754 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
20:26:19.757 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
20:26:19.757 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
20:26:19.757 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
20:26:19.757 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
20:26:19.757 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
20:26:19.758 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
20:26:19.758 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
20:26:19.760 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
20:26:19.834 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
20:26:19.851 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.42 seconds (JVM running for 4.035)
20:26:33.116 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:29:51.870 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
20:29:51.874 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
20:29:54.857 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 157732 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
20:29:54.859 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
20:29:56.053 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
20:29:56.055 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:29:56.056 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
20:29:56.110 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:29:57.326 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
20:29:57.420 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
20:29:57.781 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
20:29:57.782 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
20:29:58.149 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
20:29:58.182 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
20:29:58.183 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
20:29:58.304 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
20:29:58.310 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
20:29:58.313 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
20:29:58.314 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
20:29:58.314 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
20:29:58.315 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
20:29:58.315 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
20:29:58.315 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
20:29:58.316 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
20:29:58.318 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
20:29:58.380 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
20:29:58.410 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.064 seconds (JVM running for 5.306)
20:30:02.385 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:31:56.343 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
20:31:56.347 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
20:31:58.427 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 158397 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
20:31:58.428 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
20:31:59.448 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
20:31:59.448 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:31:59.448 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
20:31:59.487 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:32:00.540 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
20:32:00.629 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
20:32:00.977 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
20:32:00.979 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
20:32:01.385 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
20:32:01.423 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
20:32:01.424 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
20:32:01.513 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
20:32:01.519 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
20:32:01.521 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
20:32:01.522 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
20:32:01.522 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
20:32:01.523 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
20:32:01.523 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
20:32:01.523 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
20:32:01.524 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
20:32:01.525 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
20:32:01.572 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
20:32:01.589 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.604 seconds (JVM running for 4.252)
20:32:19.145 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:33:01.218 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 27
20:33:01.222 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 27, 检验记录数量: 0
20:33:07.921 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 2 行
20:33:07.927 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
20:33:08.164 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
20:33:08.165 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 4, 总列数: 8
20:33:08.226 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
20:33:08.226 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
20:33:08.292 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
20:33:08.293 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
20:33:08.295 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
20:33:08.299 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
20:33:08.300 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
20:33:08.303 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
20:33:08.305 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
20:33:08.305 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
20:33:08.305 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
20:33:08.307 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
20:33:08.308 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
20:33:08.308 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
20:33:08.310 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
20:33:08.407 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3434 bytes
20:33:08.412 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 27，文档大小: 3434 bytes
20:33:08.415 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756816388413.docx, 大小: 3434 bytes
23:02:10.409 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 178098 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
23:02:10.411 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:02:11.354 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
23:02:11.354 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:02:11.354 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:02:11.388 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:02:12.380 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:02:12.466 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:02:12.767 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
23:02:12.768 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
23:02:13.117 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
23:02:13.141 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
23:02:13.141 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
23:02:13.207 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
23:02:13.211 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
23:02:13.214 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
23:02:13.214 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
23:02:13.214 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
23:02:13.214 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
23:02:13.214 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
23:02:13.214 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
23:02:13.215 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
23:02:13.216 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
23:02:13.256 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
23:02:13.270 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.246 seconds (JVM running for 3.598)
23:02:33.864 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:19:13.509 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:19:13.513 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:19:16.696 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 184191 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
23:19:16.698 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:19:17.493 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
23:19:17.494 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:19:17.494 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:19:17.528 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:19:18.400 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:19:18.486 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:19:18.767 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
23:19:18.768 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
23:19:19.035 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
23:19:19.058 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
23:19:19.059 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
23:19:19.123 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
23:19:19.127 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
23:19:19.131 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
23:19:19.132 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
23:19:19.132 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
23:19:19.133 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
23:19:19.133 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
23:19:19.133 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
23:19:19.133 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
23:19:19.135 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
23:19:19.175 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
23:19:19.189 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 2.8 seconds (JVM running for 3.093)
23:21:20.111 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:24:27.169 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:24:27.173 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:24:30.532 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 185614 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
23:24:30.535 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:24:31.409 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
23:24:31.409 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:24:31.410 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:24:31.442 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:24:32.314 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:24:32.381 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:24:32.643 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
23:24:32.644 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
23:24:32.913 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
23:24:32.933 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
23:24:32.933 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
23:24:32.997 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
23:24:33.001 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
23:24:33.004 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
23:24:33.005 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
23:24:33.005 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
23:24:33.005 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
23:24:33.005 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
23:24:33.005 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
23:24:33.006 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
23:24:33.008 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
23:24:33.048 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
23:24:33.063 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 2.875 seconds (JVM running for 3.216)
23:24:38.046 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:25:22.343 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 28
23:25:22.346 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 28, 检验记录数量: 0
23:25:24.042 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,624] - 构建数据行完成，共 2 行
23:25:24.042 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [processTableHeightAndSplit,822] - 开始处理表格高度计算和拆分，原始数据行数: 2
23:25:24.043 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [processTableHeightAndSplit,837] - 第二列宽度: 460px, 表格最大高度: 510px, 计算得到每页最大行数: 5
23:25:24.043 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [processTableHeightAndSplit,842] - 数据行数2不超过每页最大行数5，无需拆分
23:25:24.044 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
23:25:24.233 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
23:25:24.234 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 4, 总列数: 8
23:25:24.284 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
23:25:24.285 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
23:25:24.333 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
23:25:24.333 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
23:25:24.334 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
23:25:24.335 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
23:25:24.336 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:25:24.339 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:25:24.341 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:25:24.342 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:25:24.342 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
23:25:24.344 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
23:25:24.345 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:25:24.346 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:25:24.347 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:25:24.411 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3855 bytes
23:25:24.414 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 28，文档大小: 3855 bytes
23:25:24.415 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756826724414.docx, 大小: 3855 bytes
23:25:28.660 [http-nio-9335-exec-3] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 29
23:25:28.664 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 29, 检验记录数量: 0
23:25:28.665 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,624] - 构建数据行完成，共 1 行
23:25:28.665 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [processTableHeightAndSplit,822] - 开始处理表格高度计算和拆分，原始数据行数: 1
23:25:28.665 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [processTableHeightAndSplit,837] - 第二列宽度: 460px, 表格最大高度: 510px, 计算得到每页最大行数: 21
23:25:28.666 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [processTableHeightAndSplit,842] - 数据行数1不超过每页最大行数21，无需拆分
23:25:28.666 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
23:25:28.667 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
23:25:28.668 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
23:25:28.669 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
23:25:28.669 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
23:25:28.674 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
23:25:28.674 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
23:25:28.674 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
23:25:28.675 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:25:28.675 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:25:28.676 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:25:28.676 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:25:28.677 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
23:25:28.677 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
23:25:28.677 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:25:28.678 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:25:28.678 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:25:28.684 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2966 bytes
23:25:28.687 [http-nio-9335-exec-3] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 29，文档大小: 2966 bytes
23:25:28.687 [http-nio-9335-exec-3] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756826728687.docx, 大小: 2966 bytes
23:27:59.515 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:27:59.519 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:28:03.232 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 186637 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
23:28:03.234 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:28:04.005 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
23:28:04.005 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:28:04.006 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:28:04.039 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:28:04.899 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:28:04.964 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:28:05.209 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
23:28:05.210 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
23:28:05.486 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
23:28:05.508 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
23:28:05.508 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
23:28:05.585 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
23:28:05.589 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
23:28:05.591 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
23:28:05.591 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
23:28:05.592 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
23:28:05.592 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
23:28:05.592 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
23:28:05.592 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
23:28:05.593 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
23:28:05.594 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
23:28:05.633 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
23:28:05.648 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 2.724 seconds (JVM running for 3.037)
23:38:46.594 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:38:46.597 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:38:49.950 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 189997 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
23:38:49.952 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:38:50.747 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
23:38:50.747 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:38:50.748 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:38:50.781 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:38:51.648 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:38:51.715 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:38:51.961 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
23:38:51.962 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
23:38:52.220 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
23:38:52.240 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
23:38:52.240 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
23:38:52.297 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
23:38:52.301 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
23:38:52.303 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
23:38:52.303 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
23:38:52.303 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
23:38:52.304 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
23:38:52.304 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
23:38:52.304 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
23:38:52.304 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
23:38:52.305 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
23:38:52.344 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
23:38:52.358 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 2.71 seconds (JVM running for 3.002)
23:39:02.663 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:39:38.786 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 30
23:39:38.790 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 30, 检验记录数量: 0
23:39:38.852 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 3 行
23:39:38.853 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
23:39:38.994 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
23:39:38.995 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
23:39:39.038 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
23:39:39.038 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
23:39:39.080 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
23:39:39.080 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
23:39:39.081 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
23:39:39.082 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
23:39:39.083 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
23:39:39.084 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:39:39.087 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:39:39.087 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:39:39.088 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:39:39.088 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
23:39:39.089 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
23:39:39.090 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:39:39.090 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:39:39.091 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:39:39.141 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3936 bytes
23:39:39.144 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 30，文档大小: 3936 bytes
23:39:39.145 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756827579145.docx, 大小: 3936 bytes
23:40:12.901 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 18
23:40:12.913 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,123] - 查询到检验记录数据 3 条
23:40:12.915 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 18, 检验记录数量: 3
23:40:12.916 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 3 行
23:40:12.917 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
23:40:12.918 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
23:40:12.918 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
23:40:12.920 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
23:40:12.920 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
23:40:12.927 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
23:40:12.928 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
23:40:12.929 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
23:40:12.930 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
23:40:12.931 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
23:40:12.931 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:40:12.932 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:40:12.933 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:40:12.933 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:40:12.934 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
23:40:12.934 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
23:40:12.934 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:40:12.935 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:40:12.935 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:40:12.944 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3018 bytes
23:40:12.946 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 18，文档大小: 3018 bytes
23:40:12.947 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756827612947.docx, 大小: 3018 bytes
23:40:31.758 [http-nio-9335-exec-5] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 25
23:40:31.761 [http-nio-9335-exec-5] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 25, 检验记录数量: 0
23:40:56.684 [http-nio-9335-exec-6] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 26
23:40:56.687 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 26, 检验记录数量: 0
23:40:56.688 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
23:40:56.688 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
23:40:56.689 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
23:40:56.690 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
23:40:56.691 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
23:40:56.692 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
23:40:56.694 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
23:40:56.695 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
23:40:56.695 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
23:40:56.696 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:40:56.696 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:40:56.697 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:40:56.697 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:40:56.697 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
23:40:56.697 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
23:40:56.698 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:40:56.698 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:40:56.699 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:40:56.704 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2882 bytes
23:40:56.707 [http-nio-9335-exec-6] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 26，文档大小: 2882 bytes
23:40:56.707 [http-nio-9335-exec-6] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756827656707.docx, 大小: 2882 bytes
23:42:25.757 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:42:25.762 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:42:29.128 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 191021 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
23:42:29.130 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:42:29.918 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
23:42:29.918 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:42:29.919 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:42:29.957 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:42:30.843 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:42:30.911 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:42:31.163 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
23:42:31.164 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
23:42:31.449 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
23:42:31.468 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
23:42:31.469 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
23:42:31.527 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
23:42:31.532 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
23:42:31.534 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
23:42:31.535 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
23:42:31.535 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
23:42:31.535 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
23:42:31.535 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
23:42:31.535 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
23:42:31.536 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
23:42:31.537 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
23:42:31.578 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
23:42:31.595 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 2.78 seconds (JVM running for 3.082)
23:42:46.177 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:42:56.070 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 31
23:42:56.074 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 31, 检验记录数量: 0
23:42:56.131 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 3 行
23:42:56.132 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
23:42:56.274 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
23:42:56.274 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
23:42:56.317 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
23:42:56.317 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
23:42:56.359 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
23:42:56.360 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
23:42:56.361 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
23:42:56.362 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
23:42:56.362 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
23:42:56.364 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:42:56.366 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:42:56.367 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:42:56.368 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:42:56.368 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
23:42:56.369 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
23:42:56.369 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:42:56.370 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:42:56.371 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:42:56.419 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3936 bytes
23:42:56.423 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 31，文档大小: 3936 bytes
23:42:56.424 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756827776423.docx, 大小: 3936 bytes
23:50:11.627 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:50:11.630 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:50:15.869 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 192950 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
23:50:15.870 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:50:16.895 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
23:50:16.895 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:50:16.896 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:50:16.933 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:50:18.003 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:50:18.091 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:50:18.438 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
23:50:18.439 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
23:50:18.780 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
23:50:18.823 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
23:50:18.824 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
23:50:18.896 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
23:50:18.900 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
23:50:18.903 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
23:50:18.903 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
23:50:18.904 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
23:50:18.904 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
23:50:18.904 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
23:50:18.904 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
23:50:18.905 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
23:50:18.906 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
23:50:18.949 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
23:50:18.966 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.498 seconds (JVM running for 4.484)
23:50:22.630 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:56:02.996 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:56:03.001 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:56:08.416 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 194452 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
23:56:08.417 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:56:09.412 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
23:56:09.413 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:56:09.413 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:56:09.456 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:56:10.501 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:56:10.589 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:56:10.928 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
23:56:10.929 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
23:56:11.258 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
23:56:11.285 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
23:56:11.287 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
23:56:11.363 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
23:56:11.368 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
23:56:11.370 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
23:56:11.371 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
23:56:11.371 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
23:56:11.371 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
23:56:11.371 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
23:56:11.371 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
23:56:11.372 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
23:56:11.373 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
23:56:11.416 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
23:56:11.431 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.471 seconds (JVM running for 4.155)
23:56:14.224 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:56:37.966 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 36
23:56:37.970 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 36, 检验记录数量: 0
23:56:38.046 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 3 行
23:56:38.048 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
23:56:38.248 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
23:56:38.248 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
23:56:38.307 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
23:56:38.307 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
23:56:38.366 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
23:56:38.367 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
23:56:38.369 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
23:56:38.375 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
23:56:38.376 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
23:56:38.378 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:56:38.381 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:56:38.382 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:56:38.383 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:56:38.383 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
23:56:38.384 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
23:56:38.384 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:56:38.385 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:56:38.386 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:56:38.472 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 4033 bytes
23:56:38.478 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 36，文档大小: 4033 bytes
23:56:38.480 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756828598478.docx, 大小: 4033 bytes
23:58:32.145 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:58:32.149 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:58:35.906 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 195244 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
23:58:35.907 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:58:36.929 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
23:58:36.930 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:58:36.930 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:58:36.970 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:58:37.983 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:58:38.071 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:58:38.397 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
23:58:38.398 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
23:58:38.743 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
23:58:38.773 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
23:58:38.774 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
23:58:38.850 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
23:58:38.856 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
23:58:38.858 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
23:58:38.859 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
23:58:38.859 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
23:58:38.859 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
23:58:38.859 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
23:58:38.859 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
23:58:38.860 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
23:58:38.861 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
23:58:38.907 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
23:58:38.923 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.433 seconds (JVM running for 4.029)
23:58:41.494 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:58:55.577 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 37
23:58:55.581 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 37, 检验记录数量: 0
23:58:55.707 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 3 行
23:58:55.710 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
23:58:57.188 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
23:58:57.188 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
23:58:57.245 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
23:58:57.245 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
23:58:57.314 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
23:58:57.314 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
23:58:57.319 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
23:58:57.325 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
23:58:57.327 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
23:58:57.329 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:58:57.332 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:58:57.333 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:58:57.333 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:58:57.333 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
23:58:57.335 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
23:58:57.335 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:58:57.336 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:58:57.337 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:58:57.415 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 4032 bytes
23:58:57.421 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 37，文档大小: 4032 bytes
23:58:57.422 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756828737421.docx, 大小: 4032 bytes
