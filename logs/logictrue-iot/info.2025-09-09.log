00:13:32.982 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:13:32.987 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:13:39.734 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 438136 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
00:13:39.736 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:13:40.893 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
00:13:40.894 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:13:40.895 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:13:40.946 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:13:44.231 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:13:44.331 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:13:44.719 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:13:44.720 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:13:45.071 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:13:45.103 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:13:45.104 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:13:45.191 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:13:45.196 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:13:45.199 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:13:45.199 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:13:45.200 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:13:45.200 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:13:45.200 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:13:45.200 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:13:45.201 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:13:45.203 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:13:45.256 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
00:13:45.279 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.901 seconds (JVM running for 4.6)
00:14:00.178 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:14:00.232 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,84] - 接收到新JSON格式的表格导出请求，标题: null
00:14:00.233 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,86] - 表头合并数量: 7, 数据合并数量: 0
00:14:00.233 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 检测到嵌套表格，使用增强导出模式
00:14:00.233 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: null
00:14:00.440 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
00:14:00.445 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
00:14:00.446 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 4, 总列数: 8
00:14:00.499 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
00:14:00.500 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
00:14:00.565 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
00:14:00.566 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 208磅 (4160twips)
00:14:00.567 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1621] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
00:14:00.567 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1656] - 开始处理嵌套表格，父单元格内容: 详细检查结果
00:14:00.568 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1736] - 创建嵌套表格: 3行 x 3列
00:14:00.568 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1742] - 开始创建嵌套表格
00:14:00.600 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1759] - 设置嵌套表格总宽度: 220px (4400twips)
00:14:00.605 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1895] - 使用文本方式表示嵌套表格: 3行 x 3列
00:14:00.611 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1932] - 嵌套表格文本内容添加完成
00:14:00.611 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1714] - 嵌套表格处理完成
00:14:00.613 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 208磅 (4160twips)
00:14:00.614 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1621] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
00:14:00.614 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1656] - 开始处理嵌套表格，父单元格内容: 测试数据
00:14:00.614 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1736] - 创建嵌套表格: 3行 x 3列
00:14:00.614 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1742] - 开始创建嵌套表格
00:14:00.615 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1759] - 设置嵌套表格总宽度: 360px (7200twips)
00:14:00.616 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1895] - 使用文本方式表示嵌套表格: 3行 x 3列
00:14:00.618 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1932] - 嵌套表格文本内容添加完成
00:14:00.618 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1714] - 嵌套表格处理完成
00:14:00.620 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2256] - 应用JSON格式表头合并单元格，数量: 7
00:14:00.620 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2299] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
00:14:00.623 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2299] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
00:14:00.624 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2299] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
00:14:00.624 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2299] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
00:14:00.625 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
00:14:00.626 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
00:14:00.627 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2299] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
00:14:00.627 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2299] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
00:14:00.628 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2299] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
00:14:00.721 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3272 bytes
00:14:00.731 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,109] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250909_001400.docx, 大小: 3272 bytes
00:17:26.984 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:17:26.988 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:17:30.710 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 439191 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
00:17:30.711 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:17:31.659 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
00:17:31.660 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:17:31.660 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:17:31.697 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:17:32.700 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:17:32.780 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:17:33.095 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:17:33.096 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:17:33.383 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:17:33.408 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:17:33.409 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:17:33.479 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:17:33.484 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:17:33.486 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:17:33.487 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:17:33.487 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:17:33.487 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:17:33.487 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:17:33.487 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:17:33.488 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:17:33.489 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:17:33.532 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
00:17:33.547 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.211 seconds (JVM running for 3.805)
00:17:38.250 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:17:43.660 [http-nio-9335-exec-5] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,84] - 接收到新JSON格式的表格导出请求，标题: null
00:17:43.660 [http-nio-9335-exec-5] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,86] - 表头合并数量: 7, 数据合并数量: 0
00:17:43.660 [http-nio-9335-exec-5] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 检测到嵌套表格，使用增强导出模式
00:17:43.660 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: null
00:17:43.831 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
00:17:43.836 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
00:17:43.836 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 4, 总列数: 8
00:17:43.883 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
00:17:43.883 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
00:17:43.953 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
00:17:43.954 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 208磅 (4160twips)
00:17:43.955 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1621] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
00:17:43.955 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1656] - 开始处理嵌套表格，父单元格内容: 详细检查结果
00:17:43.956 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createNestedTable,1736] - 创建嵌套表格: 3行 x 3列
00:17:43.956 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createNestedTable,1742] - 开始创建嵌套表格
00:17:43.988 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createNestedTable,1759] - 设置嵌套表格总宽度: 220px (4400twips)
00:17:43.995 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createNestedTable,1809] - 嵌套表格创建完成，实际行数: 4, 实际列数: 1
00:17:43.996 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1714] - 嵌套表格处理完成
00:17:43.997 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 118磅 (2360twips)
00:17:43.998 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1621] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
00:17:43.998 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1656] - 开始处理嵌套表格，父单元格内容: 测试数据
00:17:43.998 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createNestedTable,1736] - 创建嵌套表格: 3行 x 3列
00:17:43.999 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createNestedTable,1742] - 开始创建嵌套表格
00:17:43.999 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createNestedTable,1759] - 设置嵌套表格总宽度: 60px (1200twips)
00:17:44.002 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createNestedTable,1809] - 嵌套表格创建完成，实际行数: 4, 实际列数: 1
00:17:44.002 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2040] - 开始处理嵌套表格合并单元格，合并数量: 3
00:17:44.002 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2057] - 应用嵌套表格表头合并，数量: 2
00:17:44.009 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2217] - 应用JSON格式表头合并单元格，数量: 7
00:17:44.010 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2260] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
00:17:44.013 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2260] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
00:17:44.013 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2260] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
00:17:44.014 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2260] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
00:17:44.014 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
00:17:44.016 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
00:17:44.017 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2260] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
00:17:44.018 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2260] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
00:17:44.019 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2260] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
00:17:44.083 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3334 bytes
00:17:44.089 [http-nio-9335-exec-5] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,109] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250909_001744.docx, 大小: 3334 bytes
10:29:34.160 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 21804 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
10:29:34.161 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:29:35.411 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
10:29:35.412 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:29:35.413 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:29:35.471 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:29:36.582 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:29:36.692 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:29:37.038 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:29:37.039 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:29:37.394 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:29:37.427 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:29:37.428 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:29:37.526 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:29:37.532 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:29:37.535 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:29:37.536 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:29:37.536 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:29:37.536 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:29:37.536 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:29:37.536 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:29:37.537 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:29:37.538 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:29:37.581 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
10:29:37.600 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.904 seconds (JVM running for 5.186)
10:31:14.170 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:49:12.902 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
10:49:12.910 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
10:49:17.976 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 26373 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
10:49:17.977 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:49:18.932 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
10:49:18.933 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:49:18.933 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:49:18.972 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:49:19.955 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:49:20.046 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:49:20.404 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:49:20.405 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:49:20.717 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:49:20.741 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:49:20.742 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:49:20.812 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:49:20.818 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:49:20.820 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:49:20.821 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:49:20.821 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:49:20.821 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:49:20.821 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:49:20.821 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:49:20.822 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:49:20.823 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:49:20.865 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
10:49:20.880 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.286 seconds (JVM running for 3.85)
10:49:39.843 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:49:39.884 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,84] - 接收到新JSON格式的表格导出请求，标题: null
10:49:39.884 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,86] - 表头合并数量: 7, 数据合并数量: 0
10:49:39.884 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 检测到嵌套表格，使用增强导出模式
10:49:39.884 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: null
10:49:40.088 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
10:49:40.092 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
10:49:40.093 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 4, 总列数: 8
10:49:40.146 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
10:49:40.146 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
10:49:40.207 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
10:49:40.207 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 208磅 (4160twips)
10:49:40.209 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
10:49:40.209 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 详细检查结果
10:49:40.210 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
10:49:40.210 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
10:49:40.242 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1760] - 设置嵌套表格总宽度: 220px (4400twips)
10:49:40.247 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1810] - 嵌套表格创建完成，实际行数: 4, 实际列数: 1
10:49:40.248 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1715] - 嵌套表格处理完成
10:49:40.250 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 118磅 (2360twips)
10:49:40.250 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
10:49:40.251 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 测试数据
10:49:40.251 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
10:49:40.251 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
10:49:40.251 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1760] - 设置嵌套表格总宽度: 60px (1200twips)
10:49:40.254 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1810] - 嵌套表格创建完成，实际行数: 4, 实际列数: 1
10:49:40.254 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2041] - 开始处理嵌套表格合并单元格，合并数量: 3
10:49:40.254 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2047] - 应用嵌套表格数据行合并，数量: 3
10:49:40.263 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2149] - 应用JSON格式表头合并单元格，数量: 7
10:49:40.263 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
10:49:40.266 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
10:49:40.266 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
10:49:40.267 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
10:49:40.267 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
10:49:40.269 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
10:49:40.269 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
10:49:40.270 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
10:49:40.272 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
10:49:40.375 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 3333 bytes
10:49:40.388 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,109] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250909_104940.docx, 大小: 3333 bytes
10:51:29.779 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
10:51:29.783 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
10:51:34.244 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 27085 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
10:51:34.246 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:51:35.144 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
10:51:35.145 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:51:35.145 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:51:35.181 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:51:36.125 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:51:36.207 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:51:36.522 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:51:36.523 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:51:36.822 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:51:36.847 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:51:36.848 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:51:36.918 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:51:36.923 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:51:36.925 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:51:36.926 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:51:36.926 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:51:36.926 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:51:36.926 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:51:36.927 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:51:36.927 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:51:36.929 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:51:36.970 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
10:51:36.985 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.105 seconds (JVM running for 3.673)
10:51:39.843 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:51:39.884 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,84] - 接收到新JSON格式的表格导出请求，标题: null
10:51:39.884 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,86] - 表头合并数量: 7, 数据合并数量: 0
10:51:39.884 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 检测到嵌套表格，使用增强导出模式
10:51:39.884 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: null
10:51:40.049 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
10:51:40.053 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
10:51:40.053 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 4, 总列数: 8
10:51:40.103 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
10:51:40.103 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
10:51:40.160 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
10:51:40.160 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 208磅 (4160twips)
10:51:40.161 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
10:51:40.162 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 详细检查结果
10:51:40.163 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
10:51:40.163 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
10:51:40.199 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1760] - 设置嵌套表格总宽度: 220px (4400twips)
10:51:40.205 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1810] - 嵌套表格创建完成，实际行数: 4, 实际列数: 1
10:51:40.206 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1715] - 嵌套表格处理完成
10:51:40.207 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 118磅 (2360twips)
10:51:40.208 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
10:51:40.208 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 测试数据
10:51:40.209 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
10:51:40.209 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
10:51:40.209 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1760] - 设置嵌套表格总宽度: 60px (1200twips)
10:51:40.211 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1810] - 嵌套表格创建完成，实际行数: 4, 实际列数: 1
10:51:40.211 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2041] - 开始处理嵌套表格合并单元格，合并数量: 3
10:51:40.212 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2047] - 应用嵌套表格数据行合并，数量: 3
10:51:40.212 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2069] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '第一二行第一列合并'
10:51:40.220 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2141] - 应用JSON格式表头合并单元格，数量: 7
10:51:40.221 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
10:51:40.223 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
10:51:40.224 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
10:51:40.224 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
10:51:40.224 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
10:51:40.225 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
10:51:40.226 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
10:51:40.227 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
10:51:40.228 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
10:51:40.305 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 3333 bytes
10:51:40.312 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,109] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250909_105140.docx, 大小: 3333 bytes
10:56:13.723 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,84] - 接收到新JSON格式的表格导出请求，标题: null
10:56:13.724 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,86] - 表头合并数量: 7, 数据合并数量: 0
10:56:13.725 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 检测到嵌套表格，使用增强导出模式
10:56:13.725 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: null
10:56:13.728 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
10:56:13.729 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
10:56:13.729 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 4, 总列数: 8
10:56:13.732 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
10:56:13.732 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
10:56:13.762 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
10:56:13.762 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 208磅 (4160twips)
10:56:13.763 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
10:56:13.763 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 详细检查结果
10:56:13.763 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
10:56:13.763 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
10:56:13.764 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1760] - 设置嵌套表格总宽度: 220px (4400twips)
10:56:59.691 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1810] - 嵌套表格创建完成，实际行数: 4, 实际列数: 1
10:56:59.691 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1715] - 嵌套表格处理完成
10:56:59.693 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 118磅 (2360twips)
10:56:59.693 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
10:56:59.693 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 测试数据
10:56:59.693 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
10:56:59.693 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
10:56:59.694 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1760] - 设置嵌套表格总宽度: 60px (1200twips)
11:06:23.068 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1810] - 嵌套表格创建完成，实际行数: 4, 实际列数: 1
11:06:23.069 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2041] - 开始处理嵌套表格合并单元格，合并数量: 3
11:06:23.070 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2047] - 应用嵌套表格数据行合并，数量: 3
11:06:23.070 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2069] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '第一二行第一列合并'
11:06:23.075 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2141] - 应用JSON格式表头合并单元格，数量: 7
11:06:23.076 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:06:23.078 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:06:23.080 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:06:23.080 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:06:23.081 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
11:06:23.081 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
11:06:23.082 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:06:23.084 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:06:23.085 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:06:23.092 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 3333 bytes
11:06:23.096 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,109] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250909_110623.docx, 大小: 3333 bytes
11:06:23.314 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
11:06:23.318 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
11:06:25.704 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 30072 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
11:06:25.706 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
11:06:27.149 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
11:06:27.150 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:06:27.150 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
11:06:27.186 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:06:28.142 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
11:06:28.225 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
11:06:28.516 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
11:06:28.517 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
11:06:28.821 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
11:06:28.853 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
11:06:28.854 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
11:06:28.930 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
11:06:28.953 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
11:06:28.955 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
11:06:28.956 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
11:06:28.956 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
11:06:28.956 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
11:06:28.956 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
11:06:28.956 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
11:06:28.957 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
11:06:28.958 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
11:06:28.998 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
11:06:29.012 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.037 seconds (JVM running for 4.7)
11:07:29.363 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:07:29.471 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,84] - 接收到新JSON格式的表格导出请求，标题: null
11:07:29.472 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,86] - 表头合并数量: 7, 数据合并数量: 0
11:07:29.472 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 检测到嵌套表格，使用增强导出模式
11:07:29.472 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: null
11:07:29.796 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
11:07:29.800 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
11:07:29.801 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 4, 总列数: 8
11:07:29.863 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
11:07:29.863 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
11:07:29.927 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
11:07:29.928 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 208磅 (4160twips)
11:07:29.930 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
11:07:29.930 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 详细检查结果
11:07:29.931 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
11:07:29.931 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
11:07:29.965 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1760] - 设置嵌套表格总宽度: 220px (4400twips)
11:09:22.247 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1810] - 嵌套表格创建完成，实际行数: 4, 实际列数: 1
11:09:22.248 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1715] - 嵌套表格处理完成
11:09:22.251 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 118磅 (2360twips)
11:09:22.252 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
11:09:22.252 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 测试数据
11:09:22.252 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
11:09:22.253 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
11:09:22.686 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1760] - 设置嵌套表格总宽度: 60px (1200twips)
11:09:23.381 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1810] - 嵌套表格创建完成，实际行数: 4, 实际列数: 1
11:09:23.381 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2041] - 开始处理嵌套表格合并单元格，合并数量: 3
11:09:23.381 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2047] - 应用嵌套表格数据行合并，数量: 3
11:09:23.381 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2069] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '第一二行第一列合并'
11:09:23.390 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2141] - 应用JSON格式表头合并单元格，数量: 7
11:09:23.392 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:09:23.395 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:09:23.396 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:09:23.396 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:09:23.396 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
11:09:23.398 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
11:09:23.399 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:09:23.399 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:09:23.400 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:09:23.457 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 3333 bytes
11:09:23.462 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,109] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250909_110923.docx, 大小: 3333 bytes
11:09:26.353 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,84] - 接收到新JSON格式的表格导出请求，标题: null
11:09:26.354 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,86] - 表头合并数量: 7, 数据合并数量: 0
11:09:26.354 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 检测到嵌套表格，使用增强导出模式
11:09:26.354 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: null
11:09:26.359 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
11:09:26.360 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
11:09:26.361 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 4, 总列数: 8
11:09:26.363 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
11:09:26.363 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
11:09:26.419 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
11:09:26.420 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 208磅 (4160twips)
11:09:26.421 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
11:09:26.422 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 详细检查结果
11:09:26.423 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
11:09:26.423 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
11:10:03.614 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1760] - 设置嵌套表格总宽度: 220px (4400twips)
11:10:05.914 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1810] - 嵌套表格创建完成，实际行数: 4, 实际列数: 1
11:10:05.914 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1715] - 嵌套表格处理完成
11:10:05.916 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 118磅 (2360twips)
11:10:05.916 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
11:10:05.916 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 测试数据
11:10:05.916 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
11:10:05.916 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
11:10:06.402 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1760] - 设置嵌套表格总宽度: 60px (1200twips)
11:10:06.404 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1810] - 嵌套表格创建完成，实际行数: 4, 实际列数: 1
11:10:06.404 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2041] - 开始处理嵌套表格合并单元格，合并数量: 3
11:10:06.404 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2047] - 应用嵌套表格数据行合并，数量: 3
11:10:06.404 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2069] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '第一二行第一列合并'
11:10:06.407 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2141] - 应用JSON格式表头合并单元格，数量: 7
11:10:06.408 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:10:06.409 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:10:06.409 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:10:06.410 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:10:06.410 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
11:10:06.410 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
11:10:06.411 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:10:06.412 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:10:06.412 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2184] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:10:06.417 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 3333 bytes
11:10:06.420 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,109] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250909_111006.docx, 大小: 3333 bytes
11:10:40.876 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
11:10:40.880 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
11:10:45.180 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 31174 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
11:10:45.181 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
11:10:46.069 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
11:10:46.070 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:10:46.070 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
11:10:46.108 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:10:49.999 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
11:10:50.085 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
11:10:50.411 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
11:10:50.412 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
11:10:50.701 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
11:10:50.727 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
11:10:50.728 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
11:10:50.799 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
11:10:50.804 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
11:10:50.807 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
11:10:50.808 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
11:10:50.808 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
11:10:50.808 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
11:10:50.808 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
11:10:50.809 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
11:10:50.809 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
11:10:50.811 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
11:10:50.855 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
11:10:50.874 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.09 seconds (JVM running for 3.648)
11:10:54.786 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:10:54.828 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,84] - 接收到新JSON格式的表格导出请求，标题: null
11:10:54.828 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,86] - 表头合并数量: 7, 数据合并数量: 0
11:10:54.828 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 检测到嵌套表格，使用增强导出模式
11:10:54.828 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: null
11:10:54.995 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
11:10:54.999 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
11:10:55.000 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 4, 总列数: 8
11:10:55.054 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
11:10:55.054 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
11:10:55.110 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
11:10:55.110 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 208磅 (4160twips)
11:10:55.111 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
11:10:55.112 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 详细检查结果
11:10:55.112 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
11:10:55.112 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
11:10:55.145 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1762] - 设置嵌套表格总宽度: 220px (4400twips)
11:10:55.151 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1812] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
11:10:55.151 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1715] - 嵌套表格处理完成
11:10:55.153 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 118磅 (2360twips)
11:10:55.153 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
11:10:55.154 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 测试数据
11:10:55.154 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
11:10:55.154 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
11:10:55.154 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1762] - 设置嵌套表格总宽度: 60px (1200twips)
11:10:55.157 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1812] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
11:10:55.157 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2043] - 开始处理嵌套表格合并单元格，合并数量: 3
11:10:55.157 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2049] - 应用嵌套表格数据行合并，数量: 3
11:10:55.157 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2071] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '第一二行第一列合并'
11:10:55.161 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2071] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
11:10:55.161 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
11:10:55.162 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
11:10:55.162 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2071] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
11:10:55.163 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
11:10:55.163 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
11:10:55.163 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
11:10:55.163 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
11:10:55.163 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2055] - 嵌套表格合并单元格处理完成
11:10:55.163 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1715] - 嵌套表格处理完成
11:10:55.165 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2143] - 应用JSON格式表头合并单元格，数量: 7
11:10:55.166 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:10:55.166 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:10:55.166 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:10:55.167 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:10:55.167 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
11:10:55.168 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
11:10:55.168 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:10:55.169 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:10:55.169 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:10:55.240 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 3306 bytes
11:10:55.246 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,109] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250909_111055.docx, 大小: 3306 bytes
11:13:05.105 [http-nio-9335-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,84] - 接收到新JSON格式的表格导出请求，标题: null
11:13:05.105 [http-nio-9335-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,86] - 表头合并数量: 7, 数据合并数量: 0
11:13:05.106 [http-nio-9335-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 检测到嵌套表格，使用增强导出模式
11:13:05.106 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: null
11:13:05.107 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
11:13:05.108 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
11:13:05.108 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 4, 总列数: 8
11:13:05.109 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
11:13:05.110 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
11:13:05.119 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
11:13:05.120 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 208磅 (4160twips)
11:13:05.120 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
11:13:05.120 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 详细检查结果
11:13:05.121 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
11:13:05.121 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
11:13:05.121 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1762] - 设置嵌套表格总宽度: 220px (4400twips)
11:13:05.124 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1812] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
11:13:05.125 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1715] - 嵌套表格处理完成
11:13:05.126 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 118磅 (2360twips)
11:13:05.126 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
11:13:05.126 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 测试数据
11:13:05.127 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
11:13:05.127 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
11:13:05.127 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1762] - 设置嵌套表格总宽度: 150px (3000twips)
11:13:05.129 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1812] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
11:13:05.129 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2043] - 开始处理嵌套表格合并单元格，合并数量: 3
11:13:05.129 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2049] - 应用嵌套表格数据行合并，数量: 3
11:13:05.129 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2071] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '第一二行第一列合并'
11:13:05.129 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2071] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
11:13:05.129 [http-nio-9335-exec-6] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
11:13:05.129 [http-nio-9335-exec-6] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
11:13:05.130 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2071] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
11:13:05.130 [http-nio-9335-exec-6] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
11:13:05.130 [http-nio-9335-exec-6] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
11:13:05.130 [http-nio-9335-exec-6] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
11:13:05.130 [http-nio-9335-exec-6] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
11:13:05.130 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2055] - 嵌套表格合并单元格处理完成
11:13:05.130 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1715] - 嵌套表格处理完成
11:13:05.132 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2143] - 应用JSON格式表头合并单元格，数量: 7
11:13:05.132 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:13:05.133 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:13:05.134 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:13:05.135 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:13:05.135 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
11:13:05.136 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
11:13:05.136 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:13:05.137 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:13:05.137 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:13:05.143 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 3306 bytes
11:13:05.146 [http-nio-9335-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,109] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250909_111305.docx, 大小: 3306 bytes
12:43:51.713 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
12:43:51.719 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
12:43:54.137 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 42686 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
12:43:54.139 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
12:43:55.237 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
12:43:55.238 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:43:55.238 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
12:43:55.288 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:43:56.461 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
12:43:56.566 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
12:43:56.919 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
12:43:56.921 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
12:43:57.342 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
12:43:57.373 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
12:43:57.374 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
12:43:57.453 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
12:43:57.457 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
12:43:57.460 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
12:43:57.461 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
12:43:57.461 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
12:43:57.461 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
12:43:57.461 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
12:43:57.461 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
12:43:57.462 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
12:43:57.463 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
12:43:57.511 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
12:43:57.533 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.024 seconds (JVM running for 4.858)
14:39:01.368 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 68070 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
14:39:01.369 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:39:02.593 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
14:39:02.594 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:39:02.594 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:39:02.640 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:39:03.826 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:39:03.935 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:39:04.300 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
14:39:04.301 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
14:39:04.669 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
14:39:04.701 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
14:39:04.702 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
14:39:04.787 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
14:39:04.794 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
14:39:04.796 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
14:39:04.797 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
14:39:04.797 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
14:39:04.797 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
14:39:04.797 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
14:39:04.797 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
14:39:04.798 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
14:39:04.800 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
14:39:04.851 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
14:39:04.980 [main] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
14:39:04.986 [main] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
14:39:04.991 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9335"]
14:39:04.991 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:39:04.995 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9335"]
14:39:04.996 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9335"]
14:39:18.620 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 68395 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
14:39:18.622 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:39:19.872 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
14:39:19.873 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:39:19.873 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:39:19.914 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:39:21.279 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:39:21.374 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:39:21.770 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
14:39:21.771 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
14:39:22.112 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
14:39:22.143 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
14:39:22.144 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
14:39:22.228 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
14:39:22.233 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
14:39:22.237 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
14:39:22.237 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
14:39:22.238 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
14:39:22.238 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
14:39:22.238 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
14:39:22.238 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
14:39:22.239 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
14:39:22.241 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
14:39:22.291 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
14:39:22.311 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.439 seconds (JVM running for 5.388)
14:39:52.141 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:39:52.407 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,592] - 批量插入剩余数据成功，数量: 5
14:40:14.020 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
14:40:14.024 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
14:40:14.033 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
14:40:14.132 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
14:40:14.133 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
14:40:14.134 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
14:40:14.142 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
14:40:14.219 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
14:40:14.307 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
14:40:14.309 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
14:40:14.309 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 2, 检验记录数量: 4
14:40:14.323 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
14:40:14.325 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
14:40:14.326 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
14:40:14.327 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
14:40:14.328 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 3, 总列数: 8
14:40:14.368 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
14:40:14.368 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
14:40:14.414 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
14:40:14.415 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 410磅 (8200twips)
14:40:14.416 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
14:40:14.416 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
14:40:14.417 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
14:40:14.417 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
14:40:14.424 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1859] - 使用文本方式表示嵌套表格: 3行 x 3列
14:40:14.429 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1896] - 嵌套表格文本内容添加完成
14:40:14.429 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1715] - 嵌套表格处理完成
14:40:14.431 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2143] - 应用JSON格式表头合并单元格，数量: 7
14:40:14.432 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:40:14.435 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:40:14.437 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:40:14.438 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:40:14.438 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
14:40:14.441 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
14:40:14.442 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:40:14.443 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:40:14.444 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:40:14.453 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 3133 bytes
14:40:14.475 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
14:40:14.477 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
14:40:14.477 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
14:40:14.478 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
14:40:14.479 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
14:40:14.481 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
14:40:14.481 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
14:40:14.547 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
14:40:14.548 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
14:40:14.552 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
14:40:14.560 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
14:40:14.588 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 5 条
14:40:14.589 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 3 行绑定数据
14:40:14.589 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 4, 检验记录数量: 5
14:40:14.590 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 3 行
14:40:14.590 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
14:40:14.591 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
14:40:14.591 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
14:40:14.591 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 5, 总列数: 8
14:40:14.593 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
14:40:14.593 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
14:40:14.610 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
14:40:14.610 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 14磅 (280twips)
14:40:14.613 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 87磅 (1740twips)
14:40:14.615 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 308磅 (6160twips)
14:40:14.616 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2143] - 应用JSON格式表头合并单元格，数量: 7
14:40:14.617 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:40:14.619 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:40:14.621 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:40:14.621 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:40:14.621 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
14:40:14.622 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
14:40:14.622 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:40:14.623 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:40:14.624 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:40:14.631 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 3985 bytes
14:40:14.649 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 0 条
14:40:14.649 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
14:40:14.649 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 5, 检验记录数量: 0
14:40:14.650 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
14:40:14.650 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第5部分
14:40:14.651 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
14:40:14.651 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
14:40:14.651 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 3, 总列数: 8
14:40:14.652 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
14:40:14.652 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
14:40:14.661 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
14:40:14.661 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 391磅 (7820twips)
14:40:14.662 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2143] - 应用JSON格式表头合并单元格，数量: 7
14:40:14.663 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:40:14.664 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:40:14.665 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:40:14.665 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:40:14.665 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
14:40:14.666 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
14:40:14.667 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:40:14.668 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:40:14.668 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:40:14.674 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 4351 bytes
14:40:14.696 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 7944 bytes
14:40:14.699 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757400014697.docx, 大小: 7944 bytes
14:42:16.326 [http-nio-9335-exec-6] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,104] - 开始测试根据设计表ID导出Word文档，设计表ID: 2
14:42:16.333 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
14:42:16.333 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
14:42:16.334 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 2, 检验记录数量: 4
14:42:16.334 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
14:42:16.335 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
14:42:16.336 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
14:42:16.336 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
14:42:16.337 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 3, 总列数: 8
14:42:16.337 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
14:42:16.337 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
14:42:16.345 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
14:42:16.345 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 410磅 (8200twips)
14:42:16.346 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
14:42:16.346 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
14:42:16.346 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
14:42:16.346 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
14:42:16.347 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1859] - 使用文本方式表示嵌套表格: 3行 x 3列
14:42:16.349 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1896] - 嵌套表格文本内容添加完成
14:42:16.349 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1715] - 嵌套表格处理完成
14:42:16.351 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2143] - 应用JSON格式表头合并单元格，数量: 7
14:42:16.352 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:42:16.352 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:42:16.353 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:42:16.354 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:42:16.354 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
14:42:16.354 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
14:42:16.355 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:42:16.355 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:42:16.356 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:42:16.360 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 3132 bytes
14:42:16.363 [http-nio-9335-exec-6] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,109] - Word文档生成成功，设计表ID: 2，文档大小: 3132 bytes
14:42:16.363 [http-nio-9335-exec-6] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,119] - 复杂表格Word文档导出成功，文件名: test1757400136363.docx, 大小: 3132 bytes
14:44:32.081 [http-nio-9335-exec-9] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,104] - 开始测试根据设计表ID导出Word文档，设计表ID: 2
14:44:32.092 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
14:44:32.093 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
14:44:32.093 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 2, 检验记录数量: 4
14:44:32.093 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
14:46:28.067 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
14:46:28.069 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
14:46:28.070 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
14:46:28.071 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 3, 总列数: 8
14:46:28.072 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
14:46:28.072 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
14:46:28.080 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
14:46:28.081 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 410磅 (8200twips)
14:49:35.741 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
14:49:35.741 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
14:49:35.742 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
14:49:35.742 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
14:49:35.743 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1859] - 使用文本方式表示嵌套表格: 3行 x 3列
14:49:35.746 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1896] - 嵌套表格文本内容添加完成
14:49:35.746 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1715] - 嵌套表格处理完成
14:49:38.657 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2143] - 应用JSON格式表头合并单元格，数量: 7
14:49:38.661 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:49:38.663 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:49:38.664 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:49:38.665 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:49:38.665 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
14:49:38.666 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
14:49:38.667 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:49:38.668 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:49:38.674 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:49:38.688 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 3133 bytes
14:49:38.694 [http-nio-9335-exec-9] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,109] - Word文档生成成功，设计表ID: 2，文档大小: 3133 bytes
14:49:38.694 [http-nio-9335-exec-9] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,119] - 复杂表格Word文档导出成功，文件名: test1757400578694.docx, 大小: 3133 bytes
14:51:40.499 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 71234 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
14:51:40.500 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:51:41.532 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
14:51:41.532 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:51:41.532 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:51:41.576 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:51:42.739 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:51:42.832 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:51:43.167 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
14:51:43.168 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
14:51:43.488 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
14:51:43.517 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
14:51:43.518 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
14:51:43.614 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
14:51:43.620 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
14:51:43.623 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
14:51:43.623 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
14:51:43.623 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
14:51:43.623 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
14:51:43.624 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
14:51:43.624 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
14:51:43.624 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
14:51:43.626 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
14:51:43.679 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
14:51:43.801 [main] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
14:51:43.805 [main] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
14:51:43.808 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9335"]
14:51:43.808 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:51:43.811 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9335"]
14:51:43.812 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9335"]
14:52:08.533 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 71669 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
14:52:08.535 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:52:09.588 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
14:52:09.588 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:52:09.589 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:52:09.629 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:52:10.688 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:52:10.784 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:52:11.160 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
14:52:11.161 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
14:52:11.490 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
14:52:11.518 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
14:52:11.519 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
14:52:11.596 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
14:52:11.601 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
14:52:11.605 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
14:52:11.606 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
14:52:11.606 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
14:52:11.606 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
14:52:11.607 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
14:52:11.607 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
14:52:11.607 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
14:52:11.609 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
14:52:11.654 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
14:52:11.773 [main] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
14:52:11.777 [main] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
14:52:11.781 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9335"]
14:52:11.781 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:52:11.784 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9335"]
14:52:11.785 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9335"]
14:52:18.687 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
14:52:18.691 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
14:52:20.925 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 71917 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
14:52:20.926 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:52:21.944 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
14:52:21.944 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:52:21.945 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:52:21.984 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:52:23.027 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:52:23.116 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:52:23.469 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
14:52:23.471 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
14:52:23.784 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
14:52:23.810 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
14:52:23.811 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
14:52:23.887 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
14:52:23.892 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
14:52:23.895 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
14:52:23.896 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
14:52:23.896 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
14:52:23.896 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
14:52:23.896 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
14:52:23.896 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
14:52:23.897 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
14:52:23.899 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
14:52:23.944 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
14:52:23.960 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.604 seconds (JVM running for 4.331)
14:52:33.288 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:52:33.316 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,104] - 开始测试根据设计表ID导出Word文档，设计表ID: 2
14:52:33.341 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
14:52:33.411 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
14:52:33.412 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 2, 检验记录数量: 4
14:52:33.450 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
14:52:37.373 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
14:52:37.614 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
14:52:37.618 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
14:52:37.619 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 3, 总列数: 8
14:52:37.664 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
14:52:37.664 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
14:52:37.722 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
14:52:37.723 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 410磅 (8200twips)
14:52:37.723 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
14:52:37.724 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
14:52:37.724 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
14:52:37.724 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
14:52:37.763 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1859] - 使用文本方式表示嵌套表格: 3行 x 3列
14:52:37.767 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1896] - 嵌套表格文本内容添加完成
14:52:37.767 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1715] - 嵌套表格处理完成
14:52:37.770 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2143] - 应用JSON格式表头合并单元格，数量: 7
14:52:37.771 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:52:37.774 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:52:37.775 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:52:37.776 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:52:37.776 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
14:52:37.778 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
14:52:37.778 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:52:37.779 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:52:37.780 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:52:37.853 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 3133 bytes
14:52:37.857 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,109] - Word文档生成成功，设计表ID: 2，文档大小: 3133 bytes
14:52:37.859 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,119] - 复杂表格Word文档导出成功，文件名: test1757400757858.docx, 大小: 3133 bytes
14:53:01.563 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,104] - 开始测试根据设计表ID导出Word文档，设计表ID: 2
14:53:01.574 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
14:53:01.574 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
14:53:01.574 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 2, 检验记录数量: 4
14:53:01.576 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
14:53:01.576 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
14:53:01.578 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
14:53:01.578 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
14:53:01.578 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 3, 总列数: 8
14:53:01.581 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
14:53:01.581 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
14:53:01.596 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
14:53:01.596 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 410磅 (8200twips)
14:53:01.597 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
14:53:01.597 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
14:53:01.597 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
14:53:01.597 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
14:53:57.394 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1859] - 使用文本方式表示嵌套表格: 3行 x 3列
14:53:57.396 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1896] - 嵌套表格文本内容添加完成
14:53:57.396 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1715] - 嵌套表格处理完成
14:53:57.398 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2143] - 应用JSON格式表头合并单元格，数量: 7
14:53:57.399 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:53:57.400 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:53:57.401 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:53:57.402 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:53:57.403 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
14:53:57.403 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
14:53:57.404 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:53:57.405 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:53:57.405 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:53:57.411 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 3132 bytes
14:53:57.414 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,109] - Word文档生成成功，设计表ID: 2，文档大小: 3132 bytes
14:53:57.414 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,119] - 复杂表格Word文档导出成功，文件名: test1757400837414.docx, 大小: 3132 bytes
14:54:01.221 [http-nio-9335-exec-4] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,104] - 开始测试根据设计表ID导出Word文档，设计表ID: 2
14:54:01.231 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
14:54:01.232 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
14:54:01.232 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 2, 检验记录数量: 4
14:54:01.232 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
14:54:01.232 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
14:54:01.233 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
14:54:01.234 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
14:54:01.234 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 3, 总列数: 8
14:54:01.235 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
14:54:01.235 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
14:54:01.245 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
14:54:01.246 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 410磅 (8200twips)
14:54:01.246 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
14:54:01.246 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
14:54:01.246 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
14:54:01.247 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
14:54:16.512 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1859] - 使用文本方式表示嵌套表格: 3行 x 3列
14:54:16.515 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1896] - 嵌套表格文本内容添加完成
14:54:16.515 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1715] - 嵌套表格处理完成
14:54:16.518 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2143] - 应用JSON格式表头合并单元格，数量: 7
14:54:16.519 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:54:16.520 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:54:16.521 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:54:16.522 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:54:16.522 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
14:54:16.523 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
14:54:16.523 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:54:16.524 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:54:16.524 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:54:16.530 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 3133 bytes
14:54:16.534 [http-nio-9335-exec-4] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,109] - Word文档生成成功，设计表ID: 2，文档大小: 3133 bytes
14:54:16.534 [http-nio-9335-exec-4] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,119] - 复杂表格Word文档导出成功，文件名: test1757400856534.docx, 大小: 3133 bytes
14:54:16.760 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
14:54:16.764 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
14:54:46.737 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 72684 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
14:54:46.738 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:54:47.801 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
14:54:47.801 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:54:47.801 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:54:47.842 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:54:49.043 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:54:49.146 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:54:49.536 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
14:54:49.537 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
14:54:49.870 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
14:54:49.896 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
14:54:49.897 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
14:54:49.974 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
14:54:49.978 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
14:54:49.981 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
14:54:49.982 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
14:54:49.982 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
14:54:49.982 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
14:54:49.982 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
14:54:49.982 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
14:54:49.983 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
14:54:49.985 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
14:54:50.034 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
14:54:50.051 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.728 seconds (JVM running for 4.344)
14:54:55.094 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:54:55.349 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,592] - 批量插入剩余数据成功，数量: 5
14:55:13.244 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,104] - 开始测试根据设计表ID导出Word文档，设计表ID: 2
14:56:09.053 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 73859 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
14:56:09.055 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:56:11.639 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
14:56:11.640 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:56:11.641 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:56:11.731 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:56:13.361 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:56:13.506 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:56:14.072 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
14:56:14.073 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
14:56:14.602 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
14:56:14.647 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
14:56:14.649 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
14:56:14.778 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
14:56:14.785 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
14:56:14.789 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
14:56:14.790 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
14:56:14.791 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
14:56:14.791 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
14:56:14.791 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
14:56:14.791 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
14:56:14.792 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
14:56:14.795 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
14:56:14.894 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
14:56:14.929 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 6.814 seconds (JVM running for 8.476)
14:56:21.178 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:56:21.265 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,104] - 开始测试根据设计表ID导出Word文档，设计表ID: 2
14:56:35.601 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,104] - 开始测试根据设计表ID导出Word文档，设计表ID: 7
14:56:35.621 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
14:56:35.714 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
14:56:35.714 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 7, 检验记录数量: 4
14:56:35.755 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
14:56:35.756 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
14:56:35.966 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
14:56:35.970 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
14:56:35.971 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 3, 总列数: 8
14:56:36.019 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
14:56:36.019 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
14:56:36.077 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
14:56:36.077 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 410磅 (8200twips)
14:56:36.078 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
14:56:36.078 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
14:56:36.079 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1737] - 创建嵌套表格: 3行 x 3列
14:56:36.079 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1743] - 开始创建嵌套表格
14:56:36.114 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1762] - 设置嵌套表格总宽度: 159px (3180twips)
14:56:36.119 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1812] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
14:56:36.119 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2043] - 开始处理嵌套表格合并单元格，合并数量: 3
14:56:36.119 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2049] - 应用嵌套表格数据行合并，数量: 3
14:56:36.120 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2071] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '第一二行第一列合并'
14:56:36.123 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2071] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
14:56:36.123 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
14:56:36.124 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
14:56:36.124 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2071] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
14:56:36.124 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
14:56:36.125 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
14:56:36.125 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
14:56:36.125 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
14:56:36.125 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2055] - 嵌套表格合并单元格处理完成
14:56:36.125 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1715] - 嵌套表格处理完成
14:56:36.129 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2143] - 应用JSON格式表头合并单元格，数量: 7
14:56:36.130 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:56:36.131 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:56:36.131 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:56:36.133 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:56:36.133 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
14:56:36.134 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
14:56:36.134 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:56:36.135 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:56:36.136 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2186] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:56:36.220 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 3113 bytes
14:56:36.226 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,109] - Word文档生成成功，设计表ID: 7，文档大小: 3113 bytes
14:56:36.229 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,119] - 复杂表格Word文档导出成功，文件名: test1757400996227.docx, 大小: 3113 bytes
15:03:40.490 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:03:40.496 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:03:45.636 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 75785 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:03:45.637 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:03:46.679 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:03:46.679 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:03:46.680 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:03:46.717 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:03:47.775 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:03:47.865 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:03:48.204 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:03:48.205 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:03:48.543 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:03:48.569 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:03:48.570 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:03:48.646 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:03:48.651 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:03:48.654 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:03:48.654 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:03:48.654 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:03:48.654 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:03:48.655 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:03:48.655 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:03:48.655 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:03:48.657 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:03:48.701 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:03:48.717 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.499 seconds (JVM running for 4.181)
15:03:55.514 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:03:55.750 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:03:55.754 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
15:03:55.864 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:03:55.947 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:03:55.948 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
15:03:55.948 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:03:55.956 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:03:56.047 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
15:03:56.116 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
15:03:56.117 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
15:03:56.118 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 7, 检验记录数量: 4
15:03:56.130 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
15:03:56.131 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
15:03:56.132 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:03:56.133 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:03:56.133 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:03:56.169 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
15:03:56.170 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
15:03:56.219 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
15:03:56.220 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 410磅 (8200twips)
15:03:56.220 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1622] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
15:03:56.221 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
15:03:56.232 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2147] - 应用JSON格式表头合并单元格，数量: 7
15:03:56.232 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:03:56.236 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:03:56.236 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:03:56.237 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:03:56.237 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:03:56.239 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:03:56.241 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:03:56.243 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:03:56.245 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:03:56.256 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 3029 bytes
15:03:56.282 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:03:56.284 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:03:56.284 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
15:03:56.284 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:03:56.286 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:03:56.287 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
15:03:56.288 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
15:03:56.331 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
15:03:56.331 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:03:56.334 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:03:56.339 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
15:03:56.357 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 5 条
15:03:56.357 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 3 行绑定数据
15:03:56.358 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 9, 检验记录数量: 5
15:03:56.358 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 3 行
15:03:56.358 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
15:03:56.359 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:03:56.359 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:03:56.360 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 5, 总列数: 8
15:03:56.361 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
15:03:56.361 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
15:03:56.373 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
15:03:56.373 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 14磅 (280twips)
15:03:56.376 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 87磅 (1740twips)
15:03:56.379 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 308磅 (6160twips)
15:03:56.380 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2147] - 应用JSON格式表头合并单元格，数量: 7
15:03:56.380 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:03:56.382 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:03:56.383 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:03:56.384 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:03:56.384 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:03:56.385 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:03:56.386 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:03:56.387 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:03:56.387 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:03:56.395 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 3985 bytes
15:03:56.413 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 0 条
15:03:56.413 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
15:03:56.414 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 10, 检验记录数量: 0
15:03:56.414 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
15:03:56.414 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1348] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第5部分
15:03:56.415 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:03:56.416 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:03:56.416 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1435] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:03:56.417 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1527] - 设置表格总宽度: 715px (14300twips)
15:03:56.417 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1459] - 开始处理表头，表头行数: 2
15:03:56.430 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1471] - 表头处理完成，当前行索引: 2
15:03:56.430 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1607] - 设置数据行高度: 391磅 (7820twips)
15:03:56.432 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2147] - 应用JSON格式表头合并单元格，数量: 7
15:03:56.433 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:03:56.434 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:03:56.435 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:03:56.436 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:03:56.436 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:03:56.436 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:03:56.437 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:03:56.438 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:03:56.439 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2190] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:03:56.445 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1366] - 新JSON格式Word文档导出完成，文件大小: 4351 bytes
15:03:56.469 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 7832 bytes
15:03:56.474 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757401436470.docx, 大小: 7832 bytes
15:06:22.416 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:06:22.420 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:06:27.381 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 76614 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:06:27.382 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:06:28.352 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:06:28.353 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:06:28.353 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:06:28.396 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:06:29.460 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:06:29.550 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:06:29.955 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:06:29.957 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:06:30.621 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:06:30.702 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:06:30.706 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:06:30.925 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:06:30.935 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:06:30.943 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:06:30.946 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:06:30.947 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:06:30.948 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:06:30.948 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:06:30.948 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:06:30.950 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:06:30.954 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:06:31.055 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:06:31.108 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.168 seconds (JVM running for 4.792)
15:06:35.274 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:06:35.614 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:06:35.626 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
15:06:35.782 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:06:35.882 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:06:35.883 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
15:06:35.883 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:06:35.893 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:06:35.976 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
15:06:36.045 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
15:06:36.047 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
15:06:36.047 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 7, 检验记录数量: 4
15:06:36.060 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
15:06:36.062 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
15:06:36.063 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:06:36.063 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:06:36.063 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:06:36.101 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 715px (14300twips)
15:06:36.101 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:06:36.143 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:06:36.143 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 410磅 (8200twips)
15:06:36.144 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1620] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
15:06:36.144 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1657] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
15:06:36.145 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1741] - 创建嵌套表格: 3行 x 3列
15:06:36.145 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1747] - 开始创建嵌套表格
15:06:36.148 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1766] - 设置嵌套表格总宽度: 159px (3180twips)
15:06:36.152 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1818] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
15:06:36.152 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2049] - 开始处理嵌套表格合并单元格，合并数量: 3
15:06:36.153 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2055] - 应用嵌套表格数据行合并，数量: 3
15:06:36.153 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2077] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '第一二行第一列合并'
15:06:36.156 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2077] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
15:06:36.157 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
15:06:36.158 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
15:06:36.159 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2077] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
15:06:36.159 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
15:06:36.159 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
15:06:36.159 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
15:06:36.160 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
15:06:36.160 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2061] - 嵌套表格合并单元格处理完成
15:06:36.160 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1719] - 嵌套表格处理完成
15:06:36.163 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2149] - 应用JSON格式表头合并单元格，数量: 7
15:06:36.164 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:06:36.164 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:06:36.165 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:06:36.166 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:06:36.166 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:06:36.167 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:06:36.169 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:06:36.170 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:06:36.171 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:06:36.182 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 3110 bytes
15:06:36.205 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:06:36.207 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:06:36.208 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
15:06:36.208 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:06:36.209 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:06:36.211 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
15:06:36.211 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
15:06:36.255 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
15:06:36.256 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:06:36.258 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:06:36.263 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5436 bytes
15:06:36.283 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 5 条
15:06:36.283 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 3 行绑定数据
15:06:36.283 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 9, 检验记录数量: 5
15:06:36.284 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 3 行
15:06:36.285 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
15:06:36.285 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:06:36.286 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:06:36.286 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 5, 总列数: 8
15:06:36.288 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 715px (14300twips)
15:06:36.288 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:06:36.298 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:06:36.298 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 14磅 (280twips)
15:06:36.300 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 87磅 (1740twips)
15:06:36.302 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 308磅 (6160twips)
15:06:36.303 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2149] - 应用JSON格式表头合并单元格，数量: 7
15:06:36.304 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:06:36.305 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:06:36.306 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:06:36.306 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:06:36.306 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:06:36.307 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:06:36.307 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:06:36.308 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:06:36.309 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:06:36.315 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 3984 bytes
15:06:36.334 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 0 条
15:06:36.335 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
15:06:36.335 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 10, 检验记录数量: 0
15:06:36.335 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
15:06:36.336 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第5部分
15:06:36.336 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:06:36.337 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:06:36.337 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:06:36.338 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 715px (14300twips)
15:06:36.338 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:06:36.348 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:06:36.348 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 391磅 (7820twips)
15:06:36.350 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2149] - 应用JSON格式表头合并单元格，数量: 7
15:06:36.350 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:06:36.351 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:06:36.353 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:06:36.354 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:06:36.354 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:06:36.354 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:06:36.355 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:06:36.356 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:06:36.356 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2192] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:06:36.363 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4350 bytes
15:06:36.383 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 7918 bytes
15:06:36.385 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757401596383.docx, 大小: 7918 bytes
15:41:08.644 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:41:08.661 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:41:15.120 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 84452 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:41:15.121 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:41:16.300 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:41:16.301 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:41:16.301 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:41:16.343 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:41:17.448 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:41:17.570 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:41:17.955 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:41:17.956 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:41:18.313 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:41:18.342 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:41:18.343 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:41:18.427 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:41:18.433 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:41:18.436 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:41:18.436 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:41:18.436 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:41:18.436 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:41:18.436 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:41:18.436 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:41:18.437 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:41:18.439 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:41:18.486 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:41:18.504 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.809 seconds (JVM running for 4.505)
15:41:39.634 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:41:39.933 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:41:39.939 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
15:41:40.071 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:41:40.172 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:41:40.173 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
15:41:40.173 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:41:40.183 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:41:40.262 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
15:41:40.326 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
15:41:40.327 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
15:41:40.327 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 7, 检验记录数量: 4
15:41:40.341 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
15:41:40.342 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
15:41:40.343 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:41:40.343 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:41:40.344 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:41:40.379 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 715px (14300twips)
15:41:40.380 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:41:40.429 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:41:40.430 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 410磅 (8200twips)
15:41:40.433 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1624] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
15:41:40.433 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1661] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
15:41:40.435 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1745] - 创建嵌套表格: 3行 x 3列
15:41:40.435 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1751] - 开始创建嵌套表格
15:41:40.438 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1770] - 设置嵌套表格总宽度: 159px (3180twips)
15:41:40.442 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1822] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
15:41:40.442 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2053] - 开始处理嵌套表格合并单元格，合并数量: 3
15:41:40.442 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2059] - 应用嵌套表格数据行合并，数量: 3
15:41:40.442 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2081] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '第一二行第一列合并'
15:41:40.445 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2081] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
15:41:40.446 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
15:41:40.448 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
15:41:40.448 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2081] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
15:41:40.448 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
15:41:40.449 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
15:41:40.449 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
15:41:40.449 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
15:41:40.450 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2065] - 嵌套表格合并单元格处理完成
15:41:40.450 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1723] - 嵌套表格处理完成
15:41:40.453 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2153] - 应用JSON格式表头合并单元格，数量: 7
15:41:40.454 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:41:40.455 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:41:40.455 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:41:40.457 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:41:40.457 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:41:40.458 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:41:40.460 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:41:40.461 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:41:40.463 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:41:40.475 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 3139 bytes
15:41:40.502 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:41:40.504 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:41:40.504 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
15:41:40.505 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:41:40.506 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:41:40.508 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
15:41:40.509 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
15:41:40.561 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
15:41:40.562 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:41:40.564 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:41:40.569 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
15:41:40.586 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 5 条
15:41:40.586 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 3 行绑定数据
15:41:40.586 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 9, 检验记录数量: 5
15:41:40.587 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 3 行
15:41:40.587 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
15:41:40.588 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:41:40.589 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:41:40.589 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 5, 总列数: 8
15:41:40.591 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 715px (14300twips)
15:41:40.591 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:41:40.603 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:41:40.604 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 14磅 (280twips)
15:41:40.605 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 87磅 (1740twips)
15:41:40.608 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 308磅 (6160twips)
15:41:40.609 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2153] - 应用JSON格式表头合并单元格，数量: 7
15:41:40.610 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:41:40.610 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:41:40.611 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:41:40.611 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:41:40.611 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:41:40.612 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:41:40.612 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:41:40.613 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:41:40.613 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:41:40.620 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4024 bytes
15:41:40.635 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 0 条
15:41:40.635 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
15:41:40.636 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 10, 检验记录数量: 0
15:41:40.636 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
15:41:40.636 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第5部分
15:41:40.637 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:41:40.637 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:41:40.637 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:41:40.638 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 715px (14300twips)
15:41:40.638 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:41:40.646 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:41:40.647 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 391磅 (7820twips)
15:41:40.648 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2153] - 应用JSON格式表头合并单元格，数量: 7
15:41:40.649 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:41:40.650 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:41:40.650 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:41:40.651 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:41:40.652 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:41:40.652 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:41:40.652 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:41:40.653 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:41:40.653 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2196] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:41:40.658 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4380 bytes
15:41:40.676 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 7950 bytes
15:41:40.678 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757403700676.docx, 大小: 7950 bytes
15:45:26.589 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:45:26.614 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:45:31.317 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 85648 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:45:31.318 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:45:32.297 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:45:32.298 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:45:32.298 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:45:32.338 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:45:33.390 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:45:33.479 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:45:33.817 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:45:33.818 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:45:34.135 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:45:34.167 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:45:34.168 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:45:34.242 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:45:34.247 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:45:34.249 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:45:34.250 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:45:34.250 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:45:34.250 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:45:34.250 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:45:34.250 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:45:34.251 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:45:34.253 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:45:34.297 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:45:34.312 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.402 seconds (JVM running for 4.008)
15:45:40.333 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:45:40.581 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:45:40.586 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
15:45:40.712 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:45:40.811 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:45:40.812 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
15:45:40.813 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:45:40.823 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:45:40.893 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
15:45:40.960 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
15:45:40.961 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
15:45:40.961 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 7, 检验记录数量: 4
15:45:40.975 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
15:45:40.976 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
15:45:40.978 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:45:40.978 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:45:40.979 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:45:41.014 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 715px (14300twips)
15:45:41.014 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:45:41.058 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:45:41.059 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 410磅 (8200twips)
15:45:41.061 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1626] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
15:45:41.061 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1663] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
15:45:41.063 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1747] - 创建嵌套表格: 3行 x 3列
15:45:41.063 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1753] - 开始创建嵌套表格
15:45:41.065 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1772] - 设置嵌套表格总宽度: 159px (3180twips)
15:45:41.071 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1824] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
15:45:41.071 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2055] - 开始处理嵌套表格合并单元格，合并数量: 3
15:45:41.071 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2061] - 应用嵌套表格数据行合并，数量: 3
15:45:41.071 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '第一二行第一列合并'
15:45:41.075 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
15:45:41.075 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
15:45:41.077 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
15:45:41.077 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
15:45:41.077 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
15:45:41.078 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
15:45:41.078 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
15:45:41.078 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
15:45:41.078 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2067] - 嵌套表格合并单元格处理完成
15:45:41.079 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1725] - 嵌套表格处理完成
15:45:41.082 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
15:45:41.082 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:45:41.083 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:45:41.083 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:45:41.085 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:45:41.085 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:45:41.085 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:45:41.087 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:45:41.088 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:45:41.090 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:45:41.102 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 3143 bytes
15:45:41.129 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:45:41.131 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:45:41.131 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
15:45:41.131 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:45:41.133 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:45:41.135 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
15:45:41.135 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
15:45:41.182 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
15:45:41.183 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:45:41.186 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:45:41.190 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
15:45:41.208 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 5 条
15:45:41.208 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 3 行绑定数据
15:45:41.208 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 9, 检验记录数量: 5
15:45:41.209 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 3 行
15:45:41.209 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
15:45:41.210 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:45:41.210 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:45:41.210 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 5, 总列数: 8
15:45:41.212 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 715px (14300twips)
15:45:41.212 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:45:41.221 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:45:41.222 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 14磅 (280twips)
15:45:41.223 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 87磅 (1740twips)
15:45:41.226 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 308磅 (6160twips)
15:45:41.227 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
15:45:41.228 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:45:41.229 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:45:41.230 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:45:41.231 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:45:41.231 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:45:41.231 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:45:41.232 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:45:41.233 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:45:41.233 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:45:41.239 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4027 bytes
15:45:41.254 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 0 条
15:45:41.254 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
15:45:41.255 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 10, 检验记录数量: 0
15:45:41.255 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
15:45:41.255 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第5部分
15:45:41.256 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:45:41.256 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:45:41.256 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:45:41.257 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 715px (14300twips)
15:45:41.257 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:45:41.266 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:45:41.266 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 391磅 (7820twips)
15:45:41.267 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
15:45:41.268 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:45:41.269 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:45:41.270 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:45:41.270 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:45:41.270 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:45:41.271 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:45:41.271 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:45:41.272 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:45:41.273 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:45:41.277 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4389 bytes
15:45:41.295 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 7955 bytes
15:45:41.297 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757403941295.docx, 大小: 7955 bytes
15:47:52.897 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:47:52.907 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:47:54.951 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 86373 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:47:54.952 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:47:55.931 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:47:55.931 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:47:55.932 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:47:55.971 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:47:57.018 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:47:57.104 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:47:57.433 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:47:57.434 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:47:57.764 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:47:57.792 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:47:57.792 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:47:57.867 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:47:57.872 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:47:57.875 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:47:57.875 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:47:57.876 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:47:57.876 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:47:57.876 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:47:57.876 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:47:57.876 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:47:57.878 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:47:57.922 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:47:57.938 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.406 seconds (JVM running for 4.059)
15:48:00.690 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:48:00.932 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:48:00.937 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
15:48:01.050 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:48:01.147 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:48:01.148 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
15:48:01.148 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:48:01.160 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:48:01.257 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
15:48:01.326 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
15:48:01.328 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
15:48:01.328 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 7, 检验记录数量: 4
15:48:01.341 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
15:48:01.343 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
15:48:01.344 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:48:01.345 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:48:01.345 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:48:01.390 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 715px (14300twips)
15:48:01.391 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:48:01.448 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:48:01.449 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 410磅 (8200twips)
15:48:01.453 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1626] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
15:48:01.454 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1663] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
15:48:01.459 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1747] - 创建嵌套表格: 3行 x 3列
15:48:01.459 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1753] - 开始创建嵌套表格
15:48:01.462 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1772] - 设置嵌套表格总宽度: 159px (3180twips)
15:48:01.467 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1824] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
15:48:01.467 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2055] - 开始处理嵌套表格合并单元格，合并数量: 3
15:48:01.467 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2061] - 应用嵌套表格数据行合并，数量: 3
15:48:01.467 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '第一二行第一列合并'
15:48:01.471 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
15:48:01.471 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
15:48:01.472 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
15:48:01.473 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
15:48:01.473 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
15:48:01.473 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
15:48:01.473 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
15:48:01.474 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
15:48:01.474 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2067] - 嵌套表格合并单元格处理完成
15:48:01.474 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1725] - 嵌套表格处理完成
15:48:01.477 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
15:48:01.477 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:48:01.478 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:48:01.478 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:48:01.479 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:48:01.479 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:48:01.479 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:48:01.481 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:48:01.482 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:48:01.484 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:48:01.495 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 3143 bytes
15:48:01.519 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:48:01.521 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:48:01.521 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
15:48:01.521 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:48:01.523 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:48:01.524 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
15:48:01.525 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
15:48:01.581 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
15:48:01.581 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:48:01.585 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:48:01.591 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
15:48:01.619 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 5 条
15:48:01.620 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 3 行绑定数据
15:48:01.620 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 9, 检验记录数量: 5
15:48:01.621 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 3 行
15:48:01.621 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
15:48:01.622 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:48:01.622 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:48:01.622 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 5, 总列数: 8
15:48:01.624 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 715px (14300twips)
15:48:01.624 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:48:01.637 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:48:01.637 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 14磅 (280twips)
15:48:01.639 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 87磅 (1740twips)
15:48:01.642 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 308磅 (6160twips)
15:48:01.643 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
15:48:01.644 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:48:01.645 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:48:01.645 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:48:01.646 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:48:01.646 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:48:01.647 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:48:01.647 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:48:01.648 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:48:01.649 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:48:01.655 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4027 bytes
15:48:01.670 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 0 条
15:48:01.670 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
15:48:01.670 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 10, 检验记录数量: 0
15:48:01.671 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
15:48:01.671 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第5部分
15:48:01.672 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:48:01.672 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:48:01.672 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:48:01.673 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 715px (14300twips)
15:48:01.673 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:48:01.681 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:48:01.681 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 391磅 (7820twips)
15:48:01.683 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
15:48:01.684 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:48:01.685 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:48:01.686 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:48:01.687 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:48:01.687 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:48:01.687 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:48:01.688 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:48:01.688 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:48:01.689 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:48:01.694 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4389 bytes
15:48:01.711 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 7955 bytes
15:48:01.713 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757404081711.docx, 大小: 7955 bytes
15:48:48.209 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:48:48.213 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:48:50.141 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 86799 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:48:50.142 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:48:51.109 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:48:51.110 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:48:51.110 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:48:51.149 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:48:52.188 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:48:52.276 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:48:52.608 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:48:52.609 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:48:52.911 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:48:52.938 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:48:52.939 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:48:53.014 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:48:53.019 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:48:53.022 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:48:53.022 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:48:53.022 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:48:53.022 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:48:53.022 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:48:53.023 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:48:53.023 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:48:53.025 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:48:53.071 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:48:53.087 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.346 seconds (JVM running for 3.908)
15:48:57.150 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:48:57.366 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:48:57.370 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
15:48:57.480 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:48:57.562 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:48:57.563 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
15:48:57.563 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:48:57.571 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:48:57.645 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3018 bytes
15:48:57.708 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
15:48:57.709 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
15:48:57.709 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 7, 检验记录数量: 4
15:48:57.721 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
15:48:57.722 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
15:48:57.723 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:48:57.724 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:48:57.724 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:48:57.756 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 715px (14300twips)
15:48:57.756 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:48:57.794 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:48:57.794 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 410磅 (8200twips)
15:48:57.797 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1626] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
15:48:57.797 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1663] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
15:48:57.798 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1747] - 创建嵌套表格: 3行 x 3列
15:48:57.798 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1753] - 开始创建嵌套表格
15:48:57.801 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1772] - 设置嵌套表格总宽度: 159px (3180twips)
15:48:57.806 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1824] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
15:48:57.806 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2055] - 开始处理嵌套表格合并单元格，合并数量: 3
15:48:57.806 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2061] - 应用嵌套表格数据行合并，数量: 3
15:48:57.806 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '第一二行第一列合并'
15:48:57.810 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
15:48:57.810 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
15:48:57.812 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
15:48:57.812 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
15:48:57.812 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
15:48:57.813 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
15:48:57.813 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
15:48:57.813 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
15:48:57.813 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2067] - 嵌套表格合并单元格处理完成
15:48:57.813 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1725] - 嵌套表格处理完成
15:48:57.816 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
15:48:57.817 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:48:57.818 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:48:57.818 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:48:57.820 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:48:57.820 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:48:57.820 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:48:57.822 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:48:57.823 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:48:57.824 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:48:57.834 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 3144 bytes
15:48:57.859 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:48:57.860 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:48:57.861 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
15:48:57.861 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:48:57.863 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:48:57.864 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
15:48:57.865 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
15:48:57.906 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
15:48:57.906 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:48:57.908 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:48:57.913 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5438 bytes
15:48:57.930 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 5 条
15:48:57.930 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 3 行绑定数据
15:48:57.930 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 9, 检验记录数量: 5
15:48:57.931 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 3 行
15:48:57.931 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
15:48:57.932 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:48:57.932 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:48:57.932 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 5, 总列数: 8
15:48:57.933 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 715px (14300twips)
15:48:57.933 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:48:57.944 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:48:57.944 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 14磅 (280twips)
15:48:57.946 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 87磅 (1740twips)
15:48:57.948 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 308磅 (6160twips)
15:48:57.949 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
15:48:57.950 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:48:57.951 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:48:57.952 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:48:57.952 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:48:57.952 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:48:57.953 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:48:57.953 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:48:57.954 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:48:57.954 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:48:57.959 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4028 bytes
15:48:57.972 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 0 条
15:48:57.972 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
15:48:57.972 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 10, 检验记录数量: 0
15:48:57.973 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
15:48:57.973 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第5部分
15:48:57.973 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:48:57.974 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:48:57.974 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:48:57.974 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 715px (14300twips)
15:48:57.974 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:48:57.983 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:48:57.984 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 391磅 (7820twips)
15:48:57.985 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
15:48:57.985 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:48:57.986 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:48:57.987 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:48:57.987 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:48:57.987 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:48:57.988 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:48:57.988 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:48:57.989 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:48:57.989 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:48:57.994 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4390 bytes
15:48:58.012 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 7956 bytes
15:48:58.014 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757404138013.docx, 大小: 7956 bytes
15:52:20.868 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:52:20.883 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:52:22.844 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 87782 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:52:22.845 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:52:23.839 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:52:23.840 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:52:23.840 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:52:23.880 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:52:24.936 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:52:25.025 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:52:25.361 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:52:25.362 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:52:25.696 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:52:25.722 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:52:25.723 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:52:25.801 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:52:25.806 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:52:25.809 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:52:25.809 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:52:25.809 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:52:25.809 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:52:25.809 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:52:25.810 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:52:25.810 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:52:25.812 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:52:25.859 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:52:25.876 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.432 seconds (JVM running for 4.046)
15:52:30.892 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:52:31.115 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:52:31.119 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
15:52:31.231 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:52:31.316 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:52:31.317 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
15:52:31.318 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:52:31.326 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:52:31.396 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
15:52:31.455 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
15:52:31.456 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
15:52:31.456 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 7, 检验记录数量: 4
15:52:31.469 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
15:52:31.470 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
15:52:31.471 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:52:31.472 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:52:31.472 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:52:31.504 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 715px (14300twips)
15:52:31.505 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:52:31.542 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:52:31.542 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 410磅 (8200twips)
15:52:31.545 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1626] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
15:52:31.546 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1663] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
15:52:31.547 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1747] - 创建嵌套表格: 3行 x 3列
15:52:31.547 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1753] - 开始创建嵌套表格
15:52:31.549 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1772] - 设置嵌套表格总宽度: 159px (3180twips)
15:52:31.553 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1824] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
15:52:31.554 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2055] - 开始处理嵌套表格合并单元格，合并数量: 3
15:52:31.554 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2061] - 应用嵌套表格数据行合并，数量: 3
15:52:31.554 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '第一二行第一列合并'
15:52:31.558 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
15:52:31.558 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
15:52:31.560 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
15:52:31.561 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
15:52:31.561 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
15:52:31.561 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
15:52:31.561 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
15:52:31.562 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
15:52:31.562 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2067] - 嵌套表格合并单元格处理完成
15:52:31.562 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1725] - 嵌套表格处理完成
15:52:31.565 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
15:52:31.566 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:52:31.567 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:52:31.567 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:52:31.568 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:52:31.569 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:52:31.569 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:52:31.570 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:52:31.571 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:52:31.572 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:52:31.584 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 3143 bytes
15:52:31.610 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:52:31.613 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:52:31.613 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
15:52:31.613 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:52:31.615 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:52:31.617 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
15:52:31.617 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
15:52:31.662 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
15:52:31.663 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:52:31.665 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:52:31.669 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
15:52:31.686 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 5 条
15:52:31.686 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 3 行绑定数据
15:52:31.686 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 9, 检验记录数量: 5
15:52:31.687 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 3 行
15:52:31.687 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
15:52:31.688 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:52:31.688 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:52:31.688 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 5, 总列数: 8
15:52:31.690 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 715px (14300twips)
15:52:31.690 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:52:31.700 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:52:31.700 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 14磅 (280twips)
15:52:31.702 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 87磅 (1740twips)
15:52:31.704 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 308磅 (6160twips)
15:52:31.705 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
15:52:31.705 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:52:31.706 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:52:31.707 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:52:31.707 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:52:31.707 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:52:31.708 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:52:31.708 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:52:31.709 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:52:31.709 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:52:31.715 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4027 bytes
15:52:31.728 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 0 条
15:52:31.728 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
15:52:31.728 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 10, 检验记录数量: 0
15:52:31.728 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
15:52:31.728 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第5部分
15:52:31.729 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:52:31.729 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:52:31.730 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:52:31.730 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 715px (14300twips)
15:52:31.730 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:52:31.738 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:52:31.738 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 391磅 (7820twips)
15:52:31.739 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
15:52:31.740 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:52:31.741 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:52:31.742 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:52:31.742 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:52:31.743 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:52:31.743 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:52:31.744 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:52:31.744 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:52:31.745 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:52:31.750 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4389 bytes
15:52:31.769 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 7954 bytes
15:52:31.771 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757404351769.docx, 大小: 7954 bytes
15:53:06.411 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,592] - 批量插入剩余数据成功，数量: 5
15:53:09.078 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:53:09.079 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
15:53:09.079 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:53:09.082 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:53:09.082 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
15:53:09.083 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:53:09.086 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:53:09.092 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
15:53:09.106 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
15:53:09.106 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
15:53:09.106 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 2, 检验记录数量: 4
15:53:09.107 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
15:53:09.108 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
15:53:09.108 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:53:09.109 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:53:09.109 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:53:09.109 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
15:53:09.109 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:53:09.120 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:53:09.120 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 380磅 (7600twips)
15:53:09.121 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1626] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
15:53:09.121 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1663] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
15:53:09.121 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createNestedTable,1747] - 创建嵌套表格: 3行 x 3列
15:53:09.122 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createNestedTable,1753] - 开始创建嵌套表格
15:53:09.122 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createNestedTable,1772] - 设置嵌套表格总宽度: 159px (3180twips)
15:53:09.123 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createNestedTable,1824] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
15:53:09.123 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2055] - 开始处理嵌套表格合并单元格，合并数量: 3
15:53:09.123 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2061] - 应用嵌套表格数据行合并，数量: 3
15:53:09.123 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '第一二行第一列合并'
15:53:09.124 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
15:53:09.124 [http-nio-9335-exec-3] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
15:53:09.124 [http-nio-9335-exec-3] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
15:53:09.124 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
15:53:09.124 [http-nio-9335-exec-3] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
15:53:09.125 [http-nio-9335-exec-3] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
15:53:09.125 [http-nio-9335-exec-3] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
15:53:09.125 [http-nio-9335-exec-3] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
15:53:09.125 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2067] - 嵌套表格合并单元格处理完成
15:53:09.125 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1725] - 嵌套表格处理完成
15:53:09.127 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
15:53:09.128 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:53:09.128 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:53:09.129 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:53:09.129 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:53:09.129 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:53:09.130 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:53:09.130 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:53:09.131 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:53:09.131 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:53:09.136 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 3144 bytes
15:53:09.149 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:53:09.151 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:53:09.151 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
15:53:09.151 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:53:09.152 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:53:09.153 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
15:53:09.153 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
15:53:09.155 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
15:53:09.155 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:53:09.157 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:53:09.162 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
15:53:09.183 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 5 条
15:53:09.184 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 3 行绑定数据
15:53:09.184 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 4, 检验记录数量: 5
15:53:09.184 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 3 行
15:53:09.185 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
15:53:09.185 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:53:09.186 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:53:09.186 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 5, 总列数: 8
15:53:09.186 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
15:53:09.187 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:53:09.197 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:53:09.197 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 14磅 (280twips)
15:53:09.199 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 87磅 (1740twips)
15:53:09.201 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 277磅 (5540twips)
15:53:09.201 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
15:53:09.202 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:53:09.202 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:53:09.203 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:53:09.203 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:53:09.203 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:53:09.205 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:53:09.206 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:53:09.206 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:53:09.207 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:53:09.212 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4028 bytes
15:53:09.227 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 0 条
15:53:09.227 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
15:53:09.227 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 5, 检验记录数量: 0
15:53:09.227 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
15:53:09.227 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第5部分
15:53:09.228 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
15:53:09.228 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:53:09.229 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:53:09.229 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
15:53:09.229 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
15:53:09.236 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
15:53:09.237 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 408磅 (8160twips)
15:53:09.238 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
15:53:09.238 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:53:09.238 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:53:09.239 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:53:09.239 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:53:09.239 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
15:53:09.239 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
15:53:09.240 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:53:09.240 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:53:09.240 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:53:09.244 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4390 bytes
15:53:09.262 [http-nio-9335-exec-3] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 7958 bytes
15:53:09.262 [http-nio-9335-exec-3] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757404389262.docx, 大小: 7958 bytes
18:19:01.748 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
18:19:01.754 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
18:19:07.873 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 114912 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
18:19:07.875 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
18:19:09.078 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
18:19:09.079 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:19:09.079 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
18:19:09.123 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:19:10.254 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
18:19:10.365 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
18:19:10.760 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
18:19:10.762 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
18:19:11.121 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
18:19:11.153 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
18:19:11.155 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
18:19:11.257 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
18:19:11.264 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
18:19:11.267 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
18:19:11.267 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
18:19:11.268 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
18:19:11.268 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
18:19:11.268 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
18:19:11.268 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
18:19:11.269 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
18:19:11.270 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
18:19:11.318 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
18:19:11.337 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.964 seconds (JVM running for 4.684)
18:19:33.456 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:21:57.911 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
18:21:57.916 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
18:22:02.091 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 115786 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
18:22:02.093 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
18:22:03.070 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
18:22:03.071 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:22:03.071 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
18:22:03.110 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:22:04.162 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
18:22:04.254 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
18:22:04.604 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
18:22:04.605 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
18:22:04.921 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
18:22:04.951 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
18:22:04.952 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
18:22:05.027 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
18:22:05.032 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
18:22:05.035 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
18:22:05.035 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
18:22:05.035 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
18:22:05.036 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
18:22:05.036 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
18:22:05.036 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
18:22:05.036 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
18:22:05.038 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
18:22:05.083 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
18:22:05.099 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.42 seconds (JVM running for 4.023)
18:22:11.242 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:22:11.499 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,612] - 批量插入剩余数据成功，数量: 6
18:23:12.131 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
18:23:12.135 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
18:23:12.143 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
18:23:12.144 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 2 行绑定数据
18:23:12.145 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 1, 检验记录数量: 4
18:23:12.148 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
18:23:12.150 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
18:23:12.151 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
18:23:12.152 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
18:23:12.152 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
18:23:12.209 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
18:23:12.209 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
18:23:12.271 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
18:23:12.272 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 233磅 (4660twips)
18:23:12.313 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
18:23:12.314 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
18:23:12.317 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
18:23:12.318 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
18:23:12.318 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
18:23:12.319 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
18:23:12.320 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
18:23:12.322 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
18:23:12.323 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
18:23:12.323 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
18:23:12.425 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 3874 bytes
18:23:12.510 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
18:23:12.524 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
18:23:12.525 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
18:23:12.525 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
18:23:12.530 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
18:23:12.535 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3016 bytes
18:23:12.548 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 2 行绑定数据
18:23:12.548 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 3, 检验记录数量: 0
18:23:12.548 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
18:23:12.548 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第3部分
18:23:12.549 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
18:23:12.550 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
18:23:12.550 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
18:23:12.552 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
18:23:12.552 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
18:23:12.559 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
18:23:12.559 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 408磅 (8160twips)
18:23:12.561 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
18:23:12.561 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
18:23:12.562 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
18:23:12.564 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
18:23:12.565 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
18:23:12.565 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
18:23:12.565 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
18:23:12.566 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
18:23:12.566 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
18:23:12.567 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
18:23:12.572 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4397 bytes
18:23:12.585 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
18:23:12.585 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
18:23:12.585 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 4, 检验记录数量: 4
18:23:12.593 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
18:23:12.593 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
18:23:12.594 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
18:23:12.594 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
18:23:12.595 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
18:23:12.596 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
18:23:12.596 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
18:23:12.605 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
18:23:12.605 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 380磅 (7600twips)
18:23:12.605 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1626] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
18:23:12.605 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1663] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
18:23:12.606 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1747] - 创建嵌套表格: 3行 x 3列
18:23:12.606 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1753] - 开始创建嵌套表格
18:23:12.608 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1772] - 设置嵌套表格总宽度: 159px (3180twips)
18:23:12.610 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1824] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
18:23:12.610 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2055] - 开始处理嵌套表格合并单元格，合并数量: 3
18:23:12.610 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2061] - 应用嵌套表格数据行合并，数量: 3
18:23:12.610 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '第一二行第一列合并'
18:23:12.611 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
18:23:12.611 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
18:23:12.611 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
18:23:12.612 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
18:23:12.612 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
18:23:12.612 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
18:23:12.612 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
18:23:12.612 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
18:23:12.613 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2067] - 嵌套表格合并单元格处理完成
18:23:12.613 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1725] - 嵌套表格处理完成
18:23:12.614 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
18:23:12.615 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
18:23:12.615 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
18:23:12.616 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
18:23:12.616 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
18:23:12.616 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
18:23:12.617 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
18:23:12.617 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
18:23:12.618 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
18:23:12.618 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
18:23:12.622 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 3143 bytes
18:23:12.634 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
18:23:12.636 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
18:23:12.636 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
18:23:12.636 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
18:23:12.638 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
18:23:12.640 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
18:23:12.641 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
18:23:12.709 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
18:23:12.709 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
18:23:12.712 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
18:23:12.717 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5436 bytes
18:23:12.738 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 1 条
18:23:12.738 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
18:23:12.738 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 6, 检验记录数量: 1
18:23:12.738 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
18:23:12.739 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第6部分
18:23:12.739 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
18:23:12.740 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
18:23:12.740 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
18:23:12.741 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
18:23:12.741 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
18:23:12.749 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
18:23:12.749 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 14磅 (280twips)
18:23:12.751 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
18:23:12.751 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
18:23:12.752 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
18:23:12.752 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
18:23:12.753 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
18:23:12.753 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
18:23:12.753 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
18:23:12.754 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
18:23:12.754 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
18:23:12.755 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
18:23:12.759 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 2955 bytes
18:23:12.779 [http-nio-9335-exec-4] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 7986 bytes
18:23:12.781 [http-nio-9335-exec-4] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757413392779.docx, 大小: 7986 bytes
18:24:53.430 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
18:24:53.435 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
18:24:55.532 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 116623 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
18:24:55.533 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
18:24:56.494 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
18:24:56.494 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:24:56.495 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
18:24:56.534 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:24:57.583 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
18:24:57.675 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
18:24:58.004 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
18:24:58.005 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
18:24:58.344 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
18:24:58.371 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
18:24:58.372 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
18:24:58.449 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
18:24:58.454 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
18:24:58.458 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
18:24:58.459 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
18:24:58.459 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
18:24:58.459 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
18:24:58.459 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
18:24:58.459 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
18:24:58.460 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
18:24:58.462 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
18:24:58.510 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
18:24:58.528 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.421 seconds (JVM running for 4.091)
18:25:08.569 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:25:08.807 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,612] - 批量插入剩余数据成功，数量: 5
18:25:38.565 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
18:25:38.568 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
18:25:38.579 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
18:25:38.675 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
18:25:38.676 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
18:25:38.676 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
18:25:38.683 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
18:25:38.749 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3016 bytes
18:25:38.816 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
18:25:38.816 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 8, 检验记录数量: 4
18:25:38.829 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
18:25:38.831 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
18:25:38.832 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
18:25:38.832 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
18:25:38.833 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
18:25:38.876 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
18:25:38.876 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
18:25:38.916 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
18:25:38.916 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 380磅 (7600twips)
18:25:38.918 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1626] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
18:25:38.918 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1663] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
18:25:38.919 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1747] - 创建嵌套表格: 3行 x 3列
18:25:38.919 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1753] - 开始创建嵌套表格
18:25:38.922 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1772] - 设置嵌套表格总宽度: 159px (3180twips)
18:25:38.926 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1824] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
18:25:38.927 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2055] - 开始处理嵌套表格合并单元格，合并数量: 3
18:25:38.927 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2061] - 应用嵌套表格数据行合并，数量: 3
18:25:38.927 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '第一二行第一列合并'
18:25:38.932 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
18:25:38.932 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
18:25:38.934 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
18:25:38.935 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
18:25:38.935 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
18:25:38.935 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
18:25:38.936 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
18:25:38.936 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
18:25:38.937 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2067] - 嵌套表格合并单元格处理完成
18:25:38.937 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1725] - 嵌套表格处理完成
18:25:38.939 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
18:25:38.940 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
18:25:38.941 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
18:25:38.941 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
18:25:38.943 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
18:25:38.943 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
18:25:38.944 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
18:25:38.945 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
18:25:38.946 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
18:25:38.947 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
18:25:38.957 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 3095 bytes
18:25:38.978 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
18:25:38.980 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
18:25:38.980 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
18:25:38.980 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
18:25:38.982 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
18:25:38.983 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
18:25:38.984 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
18:25:39.026 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
18:25:39.026 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
18:25:39.029 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
18:25:39.034 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5436 bytes
18:25:39.053 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 5 条
18:25:39.053 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 10, 检验记录数量: 5
18:25:39.054 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 2 行
18:25:39.054 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
18:25:39.055 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
18:25:39.056 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
18:25:39.057 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 4, 总列数: 8
18:25:39.059 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
18:25:39.059 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
18:25:39.070 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
18:25:39.071 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 14磅 (280twips)
18:25:39.073 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 233磅 (4660twips)
18:25:39.074 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
18:25:39.075 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
18:25:39.076 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
18:25:39.077 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
18:25:39.077 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
18:25:39.077 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
18:25:39.078 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
18:25:39.078 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
18:25:39.079 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
18:25:39.080 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
18:25:39.085 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 3899 bytes
18:25:39.097 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 11, 检验记录数量: 0
18:25:39.097 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
18:25:39.097 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第5部分
18:25:39.098 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
18:25:39.098 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
18:25:39.099 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
18:25:39.100 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
18:25:39.100 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
18:25:39.111 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
18:25:39.111 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 408磅 (8160twips)
18:25:39.112 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
18:25:39.113 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
18:25:39.113 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
18:25:39.114 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
18:25:39.115 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
18:25:39.115 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
18:25:39.115 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
18:25:39.116 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
18:25:39.116 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
18:25:39.117 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
18:25:39.121 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4397 bytes
18:25:39.139 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 7806 bytes
18:25:39.141 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757413539139.docx, 大小: 7806 bytes
18:27:51.538 [http-nio-9335-exec-5] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,612] - 批量插入剩余数据成功，数量: 5
18:27:53.101 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
18:27:53.102 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
18:27:53.102 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
18:27:53.105 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
18:27:53.105 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
18:27:53.106 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
18:27:53.109 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
18:27:53.115 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
18:27:53.129 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
18:27:53.131 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
18:27:53.131 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 2, 检验记录数量: 4
18:27:53.133 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
18:27:53.133 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
18:27:53.134 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
18:27:53.134 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
18:27:53.134 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
18:27:53.135 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
18:27:53.135 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
18:27:53.143 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
18:27:53.144 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 380磅 (7600twips)
18:27:53.144 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1626] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
18:27:53.144 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1663] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
18:27:53.145 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1747] - 创建嵌套表格: 3行 x 3列
18:27:53.145 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1753] - 开始创建嵌套表格
18:27:53.145 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1772] - 设置嵌套表格总宽度: 159px (3180twips)
18:27:53.146 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1824] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
18:27:53.146 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2055] - 开始处理嵌套表格合并单元格，合并数量: 3
18:27:53.146 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2061] - 应用嵌套表格数据行合并，数量: 3
18:27:53.146 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '123'
18:27:53.147 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
18:27:53.147 [http-nio-9335-exec-6] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
18:27:53.147 [http-nio-9335-exec-6] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
18:27:53.147 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
18:27:53.147 [http-nio-9335-exec-6] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
18:27:53.148 [http-nio-9335-exec-6] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
18:27:53.148 [http-nio-9335-exec-6] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
18:27:53.148 [http-nio-9335-exec-6] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
18:27:53.148 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2067] - 嵌套表格合并单元格处理完成
18:27:53.149 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1725] - 嵌套表格处理完成
18:27:53.150 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
18:27:53.150 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
18:27:53.151 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
18:27:53.151 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
18:27:53.152 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
18:27:53.152 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
18:27:53.152 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
18:27:53.153 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
18:27:53.153 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
18:27:53.154 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
18:27:53.158 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 3141 bytes
18:27:53.169 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
18:27:53.170 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
18:27:53.170 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
18:27:53.170 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
18:27:53.171 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
18:27:53.172 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
18:27:53.172 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
18:27:53.174 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
18:27:53.174 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
18:27:53.176 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
18:27:53.180 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
18:27:53.197 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 5 条
18:27:53.197 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 3 行绑定数据
18:27:53.197 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 4, 检验记录数量: 5
18:27:53.198 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 2 行
18:27:53.198 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
18:27:53.199 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
18:27:53.199 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
18:27:53.199 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 4, 总列数: 8
18:27:53.200 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
18:27:53.200 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
18:27:53.207 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
18:27:53.209 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 14磅 (280twips)
18:27:53.210 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 233磅 (4660twips)
18:27:53.212 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
18:27:53.212 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
18:27:53.213 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
18:27:53.213 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
18:27:53.213 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
18:27:53.214 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
18:27:53.214 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
18:27:53.215 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
18:27:53.216 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
18:27:53.216 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
18:27:53.221 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 3948 bytes
18:27:53.230 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
18:27:53.230 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 5, 检验记录数量: 0
18:27:53.230 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
18:27:53.230 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第5部分
18:27:53.231 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
18:27:53.231 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
18:27:53.232 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
18:27:53.232 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
18:27:53.232 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
18:27:53.240 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
18:27:53.240 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 408磅 (8160twips)
18:27:53.241 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
18:27:53.242 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
18:27:53.242 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
18:27:53.242 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
18:27:53.242 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
18:27:53.243 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
18:27:53.243 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
18:27:53.243 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
18:27:53.243 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
18:27:53.244 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
18:27:53.247 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4398 bytes
18:27:53.264 [http-nio-9335-exec-6] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 7900 bytes
18:27:53.265 [http-nio-9335-exec-6] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757413673265.docx, 大小: 7900 bytes
20:21:19.144 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
20:21:19.151 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
20:21:26.109 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 142033 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
20:21:26.110 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
20:21:27.455 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
20:21:27.456 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:21:27.456 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
20:21:27.501 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:21:28.636 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
20:21:28.745 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
20:21:29.119 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
20:21:29.120 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
20:21:29.478 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
20:21:29.508 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
20:21:29.509 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
20:21:29.587 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
20:21:29.593 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
20:21:29.597 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
20:21:29.598 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
20:21:29.598 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
20:21:29.598 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
20:21:29.598 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
20:21:29.598 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
20:21:29.599 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
20:21:29.601 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
20:21:29.648 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
20:21:29.664 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.085 seconds (JVM running for 4.755)
20:22:50.919 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:26:23.903 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
20:26:23.907 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
20:26:28.386 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 143390 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
20:26:28.388 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
20:26:29.445 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
20:26:29.446 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:26:29.446 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
20:26:29.489 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:26:30.591 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
20:26:30.691 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
20:26:31.077 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
20:26:31.078 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
20:26:31.428 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
20:26:31.460 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
20:26:31.462 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
20:26:31.555 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
20:26:31.562 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
20:26:31.565 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
20:26:31.565 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
20:26:31.565 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
20:26:31.565 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
20:26:31.566 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
20:26:31.566 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
20:26:31.566 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
20:26:31.568 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
20:26:31.615 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
20:26:31.632 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.642 seconds (JVM running for 4.229)
20:26:37.175 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:49:43.125 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 145943 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
22:49:43.126 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
22:49:44.241 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
22:49:44.242 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:49:44.242 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
22:49:44.288 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:49:45.474 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
22:49:45.582 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
22:49:45.947 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
22:49:45.948 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
22:49:46.357 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
22:49:46.383 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
22:49:46.384 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
22:49:46.461 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
22:49:46.465 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
22:49:46.469 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
22:49:46.470 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
22:49:46.470 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
22:49:46.470 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
22:49:46.470 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
22:49:46.470 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
22:49:46.471 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
22:49:46.472 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
22:49:46.520 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
22:49:46.537 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.785 seconds (JVM running for 4.98)
22:49:53.492 [http-nio-9335-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:49:53.803 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,706] - 批量插入剩余数据成功，数量: 5
22:51:32.701 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
22:51:32.705 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
22:51:32.717 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
22:51:32.819 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
22:51:32.820 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
22:51:32.820 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
22:51:32.829 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
22:51:32.919 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3016 bytes
22:51:33.002 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
22:51:33.004 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
22:51:33.004 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 2, 检验记录数量: 4
22:51:33.018 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
22:51:33.020 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
22:51:33.021 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
22:51:33.021 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
22:51:33.022 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
22:51:33.058 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
22:51:33.059 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
22:51:33.099 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
22:51:33.099 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 380磅 (7600twips)
22:51:33.102 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1626] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
22:51:33.102 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1663] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
22:51:33.104 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1747] - 创建嵌套表格: 3行 x 3列
22:51:33.104 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1753] - 开始创建嵌套表格
22:51:33.105 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1772] - 设置嵌套表格总宽度: 159px (3180twips)
22:51:33.109 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1824] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
22:51:33.109 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2055] - 开始处理嵌套表格合并单元格，合并数量: 3
22:51:33.109 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2061] - 应用嵌套表格数据行合并，数量: 3
22:51:33.110 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '123'
22:51:33.113 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
22:51:33.113 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
22:51:33.115 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
22:51:33.116 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
22:51:33.116 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
22:51:33.116 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
22:51:33.116 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
22:51:33.117 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
22:51:33.117 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2067] - 嵌套表格合并单元格处理完成
22:51:33.117 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1725] - 嵌套表格处理完成
22:51:33.120 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
22:51:33.121 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
22:51:33.121 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
22:51:33.122 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
22:51:33.123 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
22:51:33.123 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
22:51:33.124 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
22:51:33.126 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
22:51:33.127 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
22:51:33.127 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
22:51:33.143 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 3138 bytes
22:51:33.168 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
22:51:33.170 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
22:51:33.171 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
22:51:33.171 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
22:51:33.173 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
22:51:33.174 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
22:51:33.175 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
22:51:33.238 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
22:51:33.239 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
22:51:33.241 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
22:51:33.246 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5436 bytes
22:51:33.262 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 6 条
22:51:33.263 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 3 行绑定数据
22:51:33.263 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 4, 检验记录数量: 6
22:51:33.263 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 3 行
22:51:33.263 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
22:51:33.264 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
22:51:33.264 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
22:51:33.264 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 5, 总列数: 8
22:51:33.266 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
22:51:33.266 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
22:51:33.274 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
22:51:33.275 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 14磅 (280twips)
22:51:33.277 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 73磅 (1460twips)
22:51:33.279 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 292磅 (5840twips)
22:51:33.281 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
22:51:33.281 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
22:51:33.282 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
22:51:33.282 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
22:51:33.283 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
22:51:33.283 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
22:51:33.283 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
22:51:33.284 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
22:51:33.284 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
22:51:33.285 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
22:51:33.290 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4013 bytes
22:51:33.304 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 1 条
22:51:33.304 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
22:51:33.304 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 5, 检验记录数量: 1
22:51:33.304 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
22:51:33.304 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第5部分
22:51:33.305 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
22:51:33.305 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
22:51:33.305 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
22:51:33.306 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
22:51:33.306 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
22:51:33.314 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
22:51:33.314 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 408磅 (8160twips)
22:51:33.316 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
22:51:33.316 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
22:51:33.317 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
22:51:33.317 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
22:51:33.318 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
22:51:33.318 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
22:51:33.318 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
22:51:33.319 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
22:51:33.319 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
22:51:33.319 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
22:51:33.324 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4419 bytes
22:51:33.345 [http-nio-9335-exec-4] INFO  c.l.w.c.WorkCheckController - [exportAll,152] - Word文档生成成功，文档大小: 7969 bytes
22:51:33.347 [http-nio-9335-exec-4] INFO  c.l.w.c.WorkCheckController - [exportAll,162] - 复杂表格Word文档导出成功，文件名: all_1757429493345.docx, 大小: 7969 bytes
23:03:31.022 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:03:31.027 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:03:35.818 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 149159 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
23:03:35.820 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:03:36.845 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
23:03:36.845 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:03:36.846 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:03:36.885 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:03:38.023 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:03:38.148 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:03:38.560 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
23:03:38.561 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
23:03:38.894 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
23:03:38.921 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
23:03:38.922 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
23:03:39.006 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
23:03:39.013 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
23:03:39.017 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
23:03:39.018 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
23:03:39.018 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
23:03:39.018 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
23:03:39.018 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
23:03:39.018 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
23:03:39.019 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
23:03:39.021 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
23:03:39.069 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
23:03:39.085 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.694 seconds (JVM running for 4.391)
23:04:08.598 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:04:08.817 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
23:04:08.822 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
23:04:08.936 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
23:04:09.025 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
23:04:09.027 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
23:04:09.027 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
23:04:09.036 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
23:04:09.107 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
23:04:09.166 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
23:04:09.166 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 2, 检验记录数量: 4
23:04:09.179 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
23:04:09.181 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
23:04:09.182 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
23:04:09.182 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
23:04:09.183 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
23:04:09.215 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
23:04:09.216 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
23:04:09.254 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
23:04:09.254 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 380磅 (7600twips)
23:04:09.257 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1626] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
23:04:09.257 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1663] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
23:04:09.258 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1747] - 创建嵌套表格: 3行 x 3列
23:04:09.258 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1753] - 开始创建嵌套表格
23:04:09.261 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1772] - 设置嵌套表格总宽度: 159px (3180twips)
23:04:09.265 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1824] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
23:04:09.265 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2055] - 开始处理嵌套表格合并单元格，合并数量: 3
23:04:09.265 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2061] - 应用嵌套表格数据行合并，数量: 3
23:04:09.265 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '123'
23:04:09.269 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
23:04:09.269 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
23:04:09.271 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
23:04:09.271 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
23:04:09.272 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
23:04:09.272 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
23:04:09.272 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
23:04:09.273 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
23:04:09.273 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2067] - 嵌套表格合并单元格处理完成
23:04:09.273 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1725] - 嵌套表格处理完成
23:04:09.276 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
23:04:09.277 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:04:09.277 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:04:09.278 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:04:09.279 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:04:09.280 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
23:04:09.280 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
23:04:09.282 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:04:09.283 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:04:09.284 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:04:09.295 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 3164 bytes
23:04:09.319 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
23:04:09.321 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
23:04:09.321 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
23:04:09.321 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
23:04:09.323 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
23:04:09.324 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
23:04:09.325 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
23:04:09.373 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
23:04:09.373 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
23:04:09.376 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
23:04:09.381 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
23:04:09.400 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 6 条
23:04:09.400 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 4, 检验记录数量: 6
23:04:09.401 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 3 行
23:04:09.401 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
23:04:09.402 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
23:04:09.402 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
23:04:09.402 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 5, 总列数: 8
23:04:09.404 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
23:04:09.404 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
23:04:09.414 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
23:04:09.414 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 14磅 (280twips)
23:04:09.416 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 73磅 (1460twips)
23:04:09.418 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 292磅 (5840twips)
23:04:09.420 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
23:04:09.420 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:04:09.421 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:04:09.422 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:04:09.422 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:04:09.422 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
23:04:09.423 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
23:04:09.423 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:04:09.424 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:04:09.425 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:04:09.431 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4031 bytes
23:04:09.446 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 1 条
23:04:09.446 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 5, 检验记录数量: 1
23:04:09.447 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
23:04:09.447 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第5部分
23:04:09.448 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
23:04:09.448 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
23:04:09.448 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
23:04:09.449 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
23:04:09.449 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
23:04:09.458 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
23:04:09.459 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 408磅 (8160twips)
23:04:09.462 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
23:04:09.462 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:04:09.463 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:04:09.465 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:04:09.466 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:04:09.466 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
23:04:09.467 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
23:04:09.468 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:04:09.468 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:04:09.469 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:04:09.475 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4420 bytes
23:04:09.494 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,152] - Word文档生成成功，文档大小: 8007 bytes
23:04:09.496 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,162] - 复杂表格Word文档导出成功，文件名: all_1757430249494.docx, 大小: 8007 bytes
23:09:19.154 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:09:19.159 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:09:23.308 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 150616 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
23:09:23.309 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:09:24.287 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
23:09:24.287 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:09:24.288 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:09:24.327 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:09:25.351 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:09:25.439 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:09:25.789 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
23:09:25.790 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
23:09:26.103 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
23:09:26.133 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
23:09:26.134 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
23:09:26.209 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
23:09:26.214 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
23:09:26.217 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
23:09:26.217 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
23:09:26.218 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
23:09:26.218 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
23:09:26.218 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
23:09:26.218 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
23:09:26.219 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
23:09:26.220 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
23:09:26.267 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
23:09:26.284 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.368 seconds (JVM running for 4.017)
23:09:38.052 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:09:57.523 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,703] - 批量插入剩余数据成功，数量: 5
23:10:50.650 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,703] - 批量插入剩余数据成功，数量: 5
23:11:09.679 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,703] - 批量插入剩余数据成功，数量: 5
23:17:50.334 [http-nio-9335-exec-5] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,703] - 批量插入剩余数据成功，数量: 5
23:17:50.552 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:17:50.556 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:17:54.794 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 152662 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
23:17:54.796 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:17:55.777 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
23:17:55.778 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:17:55.778 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:17:55.817 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:17:56.865 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:17:56.953 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:17:57.305 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
23:17:57.306 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
23:17:57.620 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
23:17:57.648 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
23:17:57.649 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
23:17:57.726 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
23:17:57.732 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
23:17:57.736 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
23:17:57.736 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
23:17:57.736 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
23:17:57.736 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
23:17:57.736 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
23:17:57.737 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
23:17:57.737 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
23:17:57.739 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
23:17:57.784 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
23:17:57.801 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.423 seconds (JVM running for 4.049)
23:18:20.594 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:18:27.441 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,714] - 批量插入剩余数据成功，数量: 5
23:18:31.472 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
23:18:31.476 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
23:18:31.487 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
23:18:31.577 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
23:18:31.578 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
23:18:31.578 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
23:18:31.587 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
23:18:31.650 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3016 bytes
23:18:31.707 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
23:18:31.707 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 2, 检验记录数量: 4
23:18:31.717 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
23:18:31.718 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
23:18:31.719 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
23:18:31.720 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
23:18:31.721 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
23:18:31.757 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
23:18:31.757 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
23:18:31.795 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
23:18:31.796 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 380磅 (7600twips)
23:18:31.798 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1626] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
23:18:31.798 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1663] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
23:18:31.799 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1747] - 创建嵌套表格: 3行 x 3列
23:18:31.800 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1753] - 开始创建嵌套表格
23:18:31.802 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1772] - 设置嵌套表格总宽度: 159px (3180twips)
23:18:31.806 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1824] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
23:18:31.806 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2055] - 开始处理嵌套表格合并单元格，合并数量: 3
23:18:31.806 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2061] - 应用嵌套表格数据行合并，数量: 3
23:18:31.806 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '123'
23:18:31.810 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
23:18:31.810 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
23:18:31.811 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
23:18:31.812 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
23:18:31.812 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
23:18:31.812 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
23:18:31.812 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
23:18:31.812 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
23:18:31.813 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2067] - 嵌套表格合并单元格处理完成
23:18:31.813 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1725] - 嵌套表格处理完成
23:18:31.816 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
23:18:31.816 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:18:31.817 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:18:31.817 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:18:31.818 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:18:31.818 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
23:18:31.818 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
23:18:31.819 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:18:31.820 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:18:31.821 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:18:31.831 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 3163 bytes
23:18:31.853 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
23:18:31.855 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
23:18:31.856 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
23:18:31.856 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
23:18:31.858 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
23:18:31.860 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
23:18:31.860 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
23:18:31.900 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
23:18:31.900 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
23:18:31.903 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
23:18:31.910 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5436 bytes
23:18:31.932 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 6 条
23:18:31.932 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 4, 检验记录数量: 6
23:18:31.933 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 3 行
23:18:31.933 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
23:18:31.934 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
23:18:31.934 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
23:18:31.935 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 5, 总列数: 8
23:18:31.936 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
23:18:31.936 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
23:18:31.947 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
23:18:31.948 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 14磅 (280twips)
23:18:31.950 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 73磅 (1460twips)
23:18:31.952 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 292磅 (5840twips)
23:18:31.953 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
23:18:31.954 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:18:31.955 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:18:31.956 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:18:31.957 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:18:31.957 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
23:18:31.958 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
23:18:31.958 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:18:31.959 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:18:31.960 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:18:31.965 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4030 bytes
23:18:31.981 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 1 条
23:18:31.981 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 5, 检验记录数量: 1
23:18:31.982 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
23:18:31.982 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第5部分
23:18:31.983 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
23:18:31.983 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
23:18:31.983 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
23:18:31.984 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
23:18:31.984 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
23:18:31.993 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
23:18:31.994 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 408磅 (8160twips)
23:18:31.995 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
23:18:31.996 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:18:31.996 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:18:31.997 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:18:31.997 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:18:31.998 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
23:18:31.998 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
23:18:31.998 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:18:31.999 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:18:31.999 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:18:32.005 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4416 bytes
23:18:32.026 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportAll,152] - Word文档生成成功，文档大小: 8000 bytes
23:18:32.028 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportAll,162] - 复杂表格Word文档导出成功，文件名: all_1757431112026.docx, 大小: 8000 bytes
23:25:24.229 [http-nio-9335-exec-5] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,714] - 批量插入剩余数据成功，数量: 5
23:25:24.495 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:25:24.498 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:25:28.477 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 154510 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
23:25:28.479 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:25:29.503 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
23:25:29.503 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:25:29.504 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:25:29.542 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:25:30.593 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:25:30.683 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:25:31.044 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
23:25:31.045 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
23:25:31.362 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
23:25:31.389 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
23:25:31.390 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
23:25:31.467 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
23:25:31.472 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
23:25:31.475 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
23:25:31.476 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
23:25:31.476 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
23:25:31.476 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
23:25:31.477 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
23:25:31.477 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
23:25:31.477 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
23:25:31.479 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
23:25:31.524 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
23:25:31.540 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.468 seconds (JVM running for 4.043)
23:25:55.466 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:26:00.630 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,717] - 批量插入剩余数据成功，数量: 5
23:26:05.653 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
23:26:05.656 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
23:26:05.667 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
23:26:05.751 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
23:26:05.752 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
23:26:05.752 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
23:26:05.761 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
23:26:05.823 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3016 bytes
23:26:05.877 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
23:26:05.878 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 2, 检验记录数量: 4
23:26:05.887 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
23:26:05.888 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
23:26:05.889 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
23:26:05.889 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
23:26:05.890 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
23:26:05.923 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
23:26:05.923 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
23:26:05.956 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
23:26:05.957 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 380磅 (7600twips)
23:26:05.959 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1626] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
23:26:05.960 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1663] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
23:26:05.961 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1747] - 创建嵌套表格: 3行 x 3列
23:26:05.961 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1753] - 开始创建嵌套表格
23:26:05.963 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1772] - 设置嵌套表格总宽度: 159px (3180twips)
23:26:05.966 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1824] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
23:26:05.966 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2055] - 开始处理嵌套表格合并单元格，合并数量: 3
23:26:05.966 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2061] - 应用嵌套表格数据行合并，数量: 3
23:26:05.966 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '123'
23:26:05.969 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
23:26:05.970 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
23:26:05.971 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
23:26:05.971 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2083] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
23:26:05.971 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
23:26:05.971 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
23:26:05.971 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
23:26:05.972 [http-nio-9335-exec-2] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
23:26:05.972 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2067] - 嵌套表格合并单元格处理完成
23:26:05.972 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1725] - 嵌套表格处理完成
23:26:05.975 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
23:26:05.976 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:26:05.976 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:26:05.976 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:26:05.977 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:26:05.978 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
23:26:05.978 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
23:26:05.979 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:26:05.980 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:26:05.981 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:26:05.990 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 3163 bytes
23:26:06.013 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
23:26:06.015 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
23:26:06.015 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
23:26:06.015 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
23:26:06.017 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
23:26:06.018 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
23:26:06.019 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
23:26:06.061 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
23:26:06.061 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
23:26:06.063 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
23:26:06.068 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5436 bytes
23:26:06.088 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 6 条
23:26:06.088 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 4, 检验记录数量: 6
23:26:06.089 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 3 行
23:26:06.089 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
23:26:06.090 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
23:26:06.090 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
23:26:06.090 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 5, 总列数: 8
23:26:06.092 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
23:26:06.092 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
23:26:06.102 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
23:26:06.102 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 14磅 (280twips)
23:26:06.105 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 73磅 (1460twips)
23:26:06.107 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 292磅 (5840twips)
23:26:06.110 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
23:26:06.111 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:26:06.112 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:26:06.113 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:26:06.113 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:26:06.114 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
23:26:06.114 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
23:26:06.115 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:26:06.115 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:26:06.116 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:26:06.122 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4036 bytes
23:26:06.135 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 1 条
23:26:06.136 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 5, 检验记录数量: 1
23:26:06.136 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
23:26:06.136 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1346] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第5部分
23:26:06.137 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,773] - 已设置文档为横向纸张
23:26:06.137 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,823] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
23:26:06.137 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1433] - 创建新JSON格式表格，总行数: 3, 总列数: 8
23:26:06.138 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1525] - 设置表格总宽度: 695px (13900twips)
23:26:06.138 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1457] - 开始处理表头，表头行数: 2
23:26:06.146 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1469] - 表头处理完成，当前行索引: 2
23:26:06.146 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1605] - 设置数据行高度: 408磅 (8160twips)
23:26:06.148 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2155] - 应用JSON格式表头合并单元格，数量: 7
23:26:06.148 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:26:06.149 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:26:06.149 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:26:06.149 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:26:06.149 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1031] - 应用水平合并: 行0, 列3-4, 跨度2列
23:26:06.150 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1072] - 水平合并完成: 行0, 列3-4
23:26:06.150 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:26:06.151 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:26:06.151 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2198] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:26:06.156 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1364] - 新JSON格式Word文档导出完成，文件大小: 4416 bytes
23:26:06.172 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportAll,152] - Word文档生成成功，文档大小: 8002 bytes
23:26:06.174 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportAll,162] - 复杂表格Word文档导出成功，文件名: all_1757431566173.docx, 大小: 8002 bytes
