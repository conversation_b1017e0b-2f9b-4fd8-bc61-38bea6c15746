08:16:41.895 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 46738 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
08:16:41.897 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
08:16:43.060 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
08:16:43.060 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
08:16:43.060 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
08:16:43.102 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
08:16:44.211 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
08:16:44.327 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
08:16:44.717 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
08:16:44.719 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
08:16:45.107 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
08:16:45.138 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
08:16:45.139 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
08:16:45.225 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
08:16:45.232 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
08:16:45.235 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
08:16:45.236 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
08:16:45.236 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
08:16:45.236 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
08:16:45.236 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
08:16:45.236 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
08:16:45.237 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
08:16:45.239 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
08:16:45.287 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
08:16:45.304 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.8 seconds (JVM running for 4.978)
08:18:56.078 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
08:19:04.257 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,548] - 批量插入剩余数据成功，数量: 4
08:19:11.131 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:19:11.135 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
08:19:11.142 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 4 条
08:19:11.148 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 1 行绑定数据
08:19:11.149 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 43, 检验记录数量: 4
08:19:11.155 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 1 行
08:19:11.158 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
08:19:11.159 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:19:11.160 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
08:19:11.161 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
08:19:11.219 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
08:19:11.220 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
08:19:11.285 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
08:19:11.285 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 410磅 (8200twips)
08:19:11.291 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
08:19:11.292 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
08:19:11.295 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
08:19:11.296 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
08:19:11.296 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
08:19:11.296 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
08:19:11.298 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
08:19:11.299 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
08:19:11.300 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
08:19:11.301 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
08:19:11.394 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 2986 bytes
08:19:11.459 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 2 条
08:19:11.459 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 2 行绑定数据
08:19:11.460 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 44, 检验记录数量: 2
08:19:11.460 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 2 行
08:19:11.460 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
08:19:11.461 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:19:11.461 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
08:19:11.462 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 4, 总列数: 8
08:19:11.463 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
08:19:11.463 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
08:19:11.472 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
08:19:11.473 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 14磅 (280twips)
08:19:11.474 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 395磅 (7900twips)
08:19:11.475 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
08:19:11.476 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
08:19:11.477 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
08:19:11.478 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
08:19:11.478 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
08:19:11.479 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
08:19:11.479 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
08:19:11.479 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
08:19:11.480 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
08:19:11.481 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
08:19:11.486 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3225 bytes
08:19:11.502 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 0 条
08:19:11.502 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 1 行绑定数据
08:19:11.502 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 45, 检验记录数量: 0
08:19:11.503 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 1 行
08:19:11.503 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第3部分
08:19:11.504 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:19:11.504 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
08:19:11.504 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
08:19:11.505 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
08:19:11.505 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
08:19:11.513 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
08:19:11.513 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 410磅 (8200twips)
08:19:11.552 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
08:19:11.552 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
08:19:11.553 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
08:19:11.554 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
08:19:11.554 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
08:19:11.554 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
08:19:11.555 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
08:19:11.555 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
08:19:11.556 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
08:19:11.557 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
08:19:11.561 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3613 bytes
08:19:11.574 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 0 条
08:19:11.574 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 1 行绑定数据
08:19:11.574 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 46, 检验记录数量: 0
08:19:11.574 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 1 行
08:19:11.574 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
08:19:11.575 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:19:11.575 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
08:19:11.576 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
08:19:11.576 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
08:19:11.576 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
08:19:11.585 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
08:19:11.585 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 391磅 (7820twips)
08:19:11.586 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
08:19:11.587 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
08:19:11.587 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
08:19:11.588 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
08:19:11.588 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
08:19:11.588 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
08:19:11.589 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
08:19:11.589 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
08:19:11.590 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
08:19:11.590 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
08:19:11.594 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 4350 bytes
08:19:12.588 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 5118 bytes
08:19:12.590 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757117952588.docx, 大小: 5118 bytes
08:47:26.136 [http-nio-9335-exec-5] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,548] - 批量插入剩余数据成功，数量: 4
08:47:36.415 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:47:36.415 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
08:47:36.421 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 4 条
08:47:36.422 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 1 行绑定数据
08:47:36.422 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 43, 检验记录数量: 4
08:47:36.423 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 1 行
08:47:36.424 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
08:47:36.424 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:47:36.425 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
08:47:36.425 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
08:47:36.427 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
08:47:36.427 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
08:47:36.436 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
08:47:36.437 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 410磅 (8200twips)
08:47:36.440 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
08:47:36.440 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
08:47:36.441 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
08:47:36.441 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
08:47:36.441 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
08:47:36.441 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
08:47:36.442 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
08:47:36.442 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
08:47:36.442 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
08:47:36.442 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
08:47:36.450 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 2987 bytes
08:47:36.463 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 2 条
08:47:36.463 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 2 行绑定数据
08:47:36.463 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 44, 检验记录数量: 2
08:47:36.463 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 2 行
08:47:36.464 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
08:47:36.464 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:47:36.465 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
08:47:36.465 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 4, 总列数: 8
08:47:36.466 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
08:47:36.466 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
08:47:36.472 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
08:47:36.472 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 14磅 (280twips)
08:47:36.472 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 395磅 (7900twips)
08:47:36.474 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
08:47:36.474 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
08:47:36.474 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
08:47:36.474 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
08:47:36.475 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
08:47:36.475 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
08:47:36.475 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
08:47:36.475 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
08:47:36.475 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
08:47:36.476 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
08:47:36.480 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3226 bytes
08:47:36.490 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 0 条
08:47:36.490 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 1 行绑定数据
08:47:36.490 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 45, 检验记录数量: 0
08:47:36.490 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 1 行
08:47:36.491 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第3部分
08:47:36.491 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:47:36.491 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
08:47:36.492 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
08:47:36.492 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
08:47:36.492 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
08:47:36.497 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
08:47:36.497 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 410磅 (8200twips)
08:47:36.498 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
08:47:36.498 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
08:47:36.499 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
08:47:36.499 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
08:47:36.499 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
08:47:36.500 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
08:47:36.500 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
08:47:36.500 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
08:47:36.500 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
08:47:36.501 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
08:47:36.504 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3614 bytes
08:47:36.517 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 0 条
08:47:36.517 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 1 行绑定数据
08:47:36.517 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 46, 检验记录数量: 0
08:47:36.517 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 1 行
08:47:36.518 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
08:47:36.518 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:47:36.519 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
08:47:36.519 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
08:47:36.519 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
08:47:36.519 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
08:47:36.523 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
08:47:36.524 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 391磅 (7820twips)
08:47:36.524 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
08:47:36.525 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
08:47:36.525 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
08:47:36.525 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
08:47:36.525 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
08:47:36.525 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
08:47:36.526 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
08:47:36.526 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
08:47:36.526 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
08:47:36.527 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
08:47:36.531 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 4351 bytes
08:47:36.545 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 4 条
08:47:36.545 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 1 行绑定数据
08:47:36.546 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 47, 检验记录数量: 4
08:47:36.546 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 1 行
08:47:36.546 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
08:47:36.547 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:47:36.547 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
08:47:36.547 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
08:47:36.548 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
08:47:36.548 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
08:47:36.552 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
08:47:36.552 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 410磅 (8200twips)
08:47:36.553 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
08:47:36.554 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
08:47:36.554 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
08:47:36.554 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
08:47:36.554 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
08:47:36.554 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
08:47:36.555 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
08:47:36.555 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
08:47:36.555 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
08:47:36.555 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
08:47:36.559 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 2987 bytes
08:47:36.572 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 2 条
08:47:36.572 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 2 行绑定数据
08:47:36.572 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 48, 检验记录数量: 2
08:47:36.573 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 2 行
08:47:36.573 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
08:47:36.574 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:47:36.574 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
08:47:36.575 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 4, 总列数: 8
08:47:36.575 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
08:47:36.575 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
08:47:36.580 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
08:47:36.581 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 14磅 (280twips)
08:47:36.581 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 395磅 (7900twips)
08:47:36.582 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
08:47:36.583 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
08:47:36.583 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
08:47:36.583 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
08:47:36.583 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
08:47:36.584 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
08:47:36.584 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
08:47:36.584 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
08:47:36.584 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
08:47:36.584 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
08:47:36.588 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3226 bytes
08:47:36.599 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 0 条
08:47:36.599 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 1 行绑定数据
08:47:36.599 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 49, 检验记录数量: 0
08:47:36.599 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 1 行
08:47:36.599 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第3部分
08:47:36.600 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:47:36.600 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
08:47:36.600 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
08:47:36.601 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
08:47:36.601 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
08:47:36.605 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
08:47:36.605 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 410磅 (8200twips)
08:47:36.606 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
08:47:36.606 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
08:47:36.606 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
08:47:36.606 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
08:47:36.607 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
08:47:36.607 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
08:47:36.607 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
08:47:36.607 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
08:47:36.607 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
08:47:36.608 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
08:47:36.611 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3614 bytes
08:47:36.623 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 0 条
08:47:36.623 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 1 行绑定数据
08:47:36.623 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 50, 检验记录数量: 0
08:47:36.624 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 1 行
08:47:36.624 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
08:47:36.625 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:47:36.625 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
08:47:36.625 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
08:47:36.626 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
08:47:36.626 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
08:47:36.632 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
08:47:36.632 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 391磅 (7820twips)
08:47:36.633 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
08:47:36.633 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
08:47:36.633 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
08:47:36.634 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
08:47:36.634 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
08:47:36.634 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
08:47:36.634 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
08:47:36.634 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
08:47:36.635 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
08:47:36.635 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
08:47:36.640 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 4351 bytes
08:47:36.660 [http-nio-9335-exec-7] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 6670 bytes
08:47:36.660 [http-nio-9335-exec-7] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757119656660.docx, 大小: 6670 bytes
08:59:36.584 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:59:36.585 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
08:59:36.589 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 4 条
08:59:58.423 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 1 行绑定数据
08:59:58.423 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 47, 检验记录数量: 4
08:59:58.424 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 1 行
08:59:58.424 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
08:59:58.425 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
08:59:58.426 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
08:59:58.426 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
08:59:58.427 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
08:59:58.427 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
08:59:58.432 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
08:59:58.432 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 410磅 (8200twips)
08:59:58.434 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
08:59:58.434 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
08:59:58.434 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
08:59:58.435 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
08:59:58.435 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
08:59:58.435 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
08:59:58.436 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
08:59:58.436 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
08:59:58.436 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
08:59:58.436 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
08:59:58.440 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 2987 bytes
08:59:58.452 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 2 条
09:00:20.057 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 2 行绑定数据
09:00:20.057 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 48, 检验记录数量: 2
09:00:20.058 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 2 行
09:00:20.059 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
09:00:20.060 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
09:00:20.060 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
09:00:20.060 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 4, 总列数: 8
09:00:20.061 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
09:00:20.061 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
09:00:20.067 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
09:00:20.067 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 14磅 (280twips)
09:00:20.068 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 395磅 (7900twips)
09:00:20.069 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
09:00:20.070 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
09:00:20.070 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
09:00:20.070 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
09:00:20.071 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
09:00:20.071 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
09:00:20.071 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
09:00:20.072 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
09:00:20.073 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
09:00:20.074 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
09:00:20.078 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3225 bytes
09:00:20.095 [http-nio-9335-exec-10] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 3437 bytes
09:00:20.095 [http-nio-9335-exec-10] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757120420095.docx, 大小: 3437 bytes
09:18:27.321 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,548] - 批量插入剩余数据成功，数量: 3
09:18:29.022 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
09:18:29.023 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
09:18:29.027 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 4 条
09:18:29.028 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 1 行绑定数据
09:18:29.028 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 51, 检验记录数量: 4
09:18:29.028 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 1 行
09:18:29.028 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
09:18:29.029 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
09:18:29.029 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
09:18:29.030 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
09:18:29.030 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
09:18:29.030 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
09:18:29.035 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
09:18:29.035 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 410磅 (8200twips)
09:18:29.036 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
09:18:29.036 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
09:18:29.037 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
09:18:29.037 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
09:18:29.037 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
09:18:29.037 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
09:18:29.037 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
09:18:29.038 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
09:18:29.038 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
09:18:29.038 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
09:18:29.042 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 2986 bytes
09:18:29.053 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 5 条
09:18:29.053 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 3 行绑定数据
09:18:29.053 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 52, 检验记录数量: 5
09:18:29.053 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 3 行
09:18:29.053 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
09:18:29.054 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
09:18:29.054 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
09:18:29.054 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 5, 总列数: 8
09:18:29.055 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
09:18:29.056 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
09:18:29.060 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
09:18:29.061 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 14磅 (280twips)
09:18:29.062 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 87磅 (1740twips)
09:18:29.063 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 308磅 (6160twips)
09:18:29.063 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
09:18:29.064 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
09:18:29.064 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
09:18:29.064 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
09:18:29.064 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
09:18:29.064 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
09:18:29.065 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
09:18:29.065 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
09:18:29.065 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
09:18:29.065 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
09:18:29.070 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3984 bytes
09:18:29.081 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 0 条
09:18:29.081 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 1 行绑定数据
09:18:29.081 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 53, 检验记录数量: 0
09:18:29.081 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 1 行
09:18:29.081 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第3部分
09:18:29.082 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
09:18:29.082 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
09:18:29.082 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
09:18:29.082 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
09:18:29.083 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
09:18:29.087 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
09:18:29.087 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 391磅 (7820twips)
09:18:29.087 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
09:18:29.088 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
09:18:29.088 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
09:18:29.088 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
09:18:29.088 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
09:18:29.088 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
09:18:29.089 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
09:18:29.089 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
09:18:29.089 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
09:18:29.089 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
09:18:29.093 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 4350 bytes
09:18:29.104 [http-nio-9335-exec-4] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 5073 bytes
09:18:29.104 [http-nio-9335-exec-4] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757121509104.docx, 大小: 5073 bytes
09:18:59.975 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
09:18:59.975 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
09:18:59.979 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 4 条
09:18:59.980 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 1 行绑定数据
09:18:59.980 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 51, 检验记录数量: 4
09:18:59.980 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 1 行
09:18:59.980 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
09:18:59.981 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
09:18:59.982 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
09:18:59.982 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
09:18:59.983 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
09:18:59.983 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
09:19:00.053 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
09:19:00.054 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 410磅 (8200twips)
09:19:00.055 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
09:19:00.056 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
09:19:00.056 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
09:19:00.056 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
09:19:00.056 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
09:19:00.056 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
09:19:00.057 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
09:19:00.057 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
09:19:00.057 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
09:19:00.058 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
09:19:00.063 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 2987 bytes
09:19:00.075 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 5 条
09:19:00.075 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 3 行绑定数据
09:19:00.076 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 52, 检验记录数量: 5
09:19:00.076 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 3 行
09:19:00.076 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
09:19:00.077 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
09:19:00.077 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
09:19:00.078 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 5, 总列数: 8
09:19:00.078 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
09:19:00.078 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
09:19:00.084 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
09:19:00.085 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 14磅 (280twips)
09:19:00.086 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 87磅 (1740twips)
09:19:00.087 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 308磅 (6160twips)
09:19:00.088 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
09:19:00.088 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
09:19:00.088 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
09:19:00.089 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
09:19:00.089 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
09:19:00.089 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
09:19:00.089 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
09:19:00.090 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
09:19:00.090 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
09:19:00.090 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
09:19:00.095 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3984 bytes
09:19:00.107 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 0 条
09:19:00.107 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 1 行绑定数据
09:19:00.107 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 53, 检验记录数量: 0
09:19:00.108 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 1 行
09:19:00.108 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第3部分
09:19:00.108 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
09:19:00.109 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
09:19:00.109 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
09:19:00.109 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
09:19:00.109 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
09:19:00.114 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
09:19:00.114 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 391磅 (7820twips)
09:19:00.115 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
09:19:00.115 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
09:19:00.116 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
09:19:00.116 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
09:19:00.116 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
09:19:00.116 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
09:19:00.117 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
09:19:00.117 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
09:19:00.117 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
09:19:00.117 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
09:19:00.121 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 4350 bytes
09:19:00.138 [http-nio-9335-exec-6] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 5074 bytes
09:19:00.138 [http-nio-9335-exec-6] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757121540138.docx, 大小: 5074 bytes
09:22:55.631 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
 ) count_
09:22:55.636 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records  order by sort
 limit ?,?
09:22:55.636 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0(Long), 10(Long)
09:23:08.875 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
09:23:08.876 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
09:23:08.877 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
09:23:08.877 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
09:23:22.836 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
09:23:22.836 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
09:23:22.838 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
09:23:22.839 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
09:23:30.668 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
 ) count_
09:23:30.670 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records  order by sort
 limit ?,?
09:23:30.671 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0(Long), 10(Long)
09:35:13.022 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
09:35:13.022 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
09:35:13.023 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
09:35:13.024 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
09:35:16.050 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
09:35:16.050 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
09:35:16.052 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
09:35:16.052 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
09:35:18.606 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
 ) count_
09:35:18.607 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records  order by sort
 limit ?,?
09:35:18.608 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0(Long), 10(Long)
09:36:08.751 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
 ) count_
09:36:08.753 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records  order by sort
 limit ?,?
09:36:08.753 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0(Long), 10(Long)
09:53:46.898 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
09:53:46.901 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
10:09:17.116 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 73420 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
10:09:17.118 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:09:18.087 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
10:09:18.088 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:09:18.088 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:09:18.129 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:09:19.102 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:09:19.191 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:09:19.473 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:09:19.473 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:09:19.780 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:09:19.807 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:09:19.808 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:09:19.883 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:09:19.888 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:09:19.891 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:09:19.891 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:09:19.891 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:09:19.891 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:09:19.892 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:09:19.892 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:09:19.892 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:09:19.894 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:09:19.954 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
10:09:19.968 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.247 seconds (JVM running for 3.819)
10:09:36.552 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:11:52.741 [http-nio-9335-exec-7] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,122] - 验证简单Word导出请求，标题: 简单Word文档
10:11:52.741 [http-nio-9335-exec-7] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,140] - JSON内容验证通过 - 节点总数: 4, 图片数量: 0, 文本节点: 2
10:11:52.741 [http-nio-9335-exec-7] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,170] - 简单Word导出请求验证通过
10:11:52.754 [http-nio-9335-exec-8] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,44] - 接收到简单Word导出请求，标题: 简单Word文档
10:11:52.754 [http-nio-9335-exec-8] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,50] - JSON内容统计 - 总节点: 4, 图片数量: 0, 文本节点: 2
10:11:52.754 [http-nio-9335-exec-8] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
10:11:53.050 [http-nio-9335-exec-8] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 3
10:11:53.051 [http-nio-9335-exec-8] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
10:11:53.051 [http-nio-9335-exec-8] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 3
10:11:53.054 [http-nio-9335-exec-8] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
10:11:53.136 [http-nio-9335-exec-8] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 2896 bytes
10:11:53.142 [http-nio-9335-exec-8] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,75] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250906_101153.docx, 大小: 2896 bytes
11:16:54.414 [http-nio-9335-exec-4] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,122] - 验证简单Word导出请求，标题: 简单Word文档
11:16:54.419 [http-nio-9335-exec-4] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,140] - JSON内容验证通过 - 节点总数: 12, 图片数量: 0, 文本节点: 6
11:16:54.419 [http-nio-9335-exec-4] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,170] - 简单Word导出请求验证通过
11:16:54.436 [http-nio-9335-exec-5] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,44] - 接收到简单Word导出请求，标题: 简单Word文档
11:16:54.437 [http-nio-9335-exec-5] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,50] - JSON内容统计 - 总节点: 12, 图片数量: 0, 文本节点: 6
11:16:54.437 [http-nio-9335-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
11:16:54.455 [http-nio-9335-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 9
11:16:54.456 [http-nio-9335-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
11:16:54.456 [http-nio-9335-exec-5] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 9
11:16:54.463 [http-nio-9335-exec-5] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
11:16:54.481 [http-nio-9335-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 2998 bytes
11:16:54.486 [http-nio-9335-exec-5] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,75] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250906_111654.docx, 大小: 2998 bytes
11:23:16.814 [http-nio-9335-exec-7] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,122] - 验证简单Word导出请求，标题: 简单Word文档
11:23:16.816 [http-nio-9335-exec-7] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,140] - JSON内容验证通过 - 节点总数: 12, 图片数量: 0, 文本节点: 6
11:23:16.819 [http-nio-9335-exec-7] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,170] - 简单Word导出请求验证通过
11:23:16.836 [http-nio-9335-exec-8] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,44] - 接收到简单Word导出请求，标题: 简单Word文档
11:23:16.837 [http-nio-9335-exec-8] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,50] - JSON内容统计 - 总节点: 12, 图片数量: 0, 文本节点: 6
11:23:16.837 [http-nio-9335-exec-8] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
11:23:16.843 [http-nio-9335-exec-8] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 9
11:23:16.844 [http-nio-9335-exec-8] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
11:23:16.844 [http-nio-9335-exec-8] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 9
11:23:16.858 [http-nio-9335-exec-8] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
11:23:16.879 [http-nio-9335-exec-8] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3002 bytes
11:23:16.886 [http-nio-9335-exec-8] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,75] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250906_112316.docx, 大小: 3002 bytes
11:29:15.794 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
11:29:15.797 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
11:29:23.056 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 91569 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
11:29:23.058 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
11:29:24.002 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
11:29:24.003 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:29:24.003 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
11:29:24.037 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:29:24.967 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
11:29:25.048 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
11:29:25.385 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
11:29:25.387 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
11:29:25.688 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
11:29:25.711 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
11:29:25.712 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
11:29:25.777 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
11:29:25.781 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
11:29:25.783 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
11:29:25.784 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
11:29:25.784 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
11:29:25.784 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
11:29:25.784 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
11:29:25.784 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
11:29:25.785 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
11:29:25.786 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
11:29:25.827 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
11:29:25.842 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.16 seconds (JVM running for 3.507)
11:29:40.063 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:29:40.097 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,122] - 验证简单Word导出请求，标题: 简单Word文档
11:29:40.097 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,140] - JSON内容验证通过 - 节点总数: 12, 图片数量: 0, 文本节点: 6
11:29:40.097 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,170] - 简单Word导出请求验证通过
11:29:40.128 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,44] - 接收到简单Word导出请求，标题: 简单Word文档
11:29:40.129 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,50] - JSON内容统计 - 总节点: 12, 图片数量: 0, 文本节点: 6
11:29:40.129 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
11:29:40.406 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 9
11:29:40.406 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
11:29:40.407 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 9
11:29:40.413 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
11:29:40.473 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3003 bytes
11:29:40.479 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,75] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250906_112940.docx, 大小: 3003 bytes
11:31:41.621 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
11:31:41.624 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
11:31:46.468 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 92429 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
11:31:46.469 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
11:31:47.427 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
11:31:47.428 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:31:47.428 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
11:31:47.466 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:31:48.488 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
11:31:48.571 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
11:31:48.892 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
11:31:48.894 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
11:31:49.211 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
11:31:49.235 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
11:31:49.236 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
11:31:49.306 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
11:31:49.310 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
11:31:49.313 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
11:31:49.313 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
11:31:49.313 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
11:31:49.314 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
11:31:49.314 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
11:31:49.314 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
11:31:49.314 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
11:31:49.316 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
11:31:49.358 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
11:31:49.373 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.257 seconds (JVM running for 4.253)
11:31:54.500 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:31:54.593 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,122] - 验证简单Word导出请求，标题: 简单Word文档
11:31:54.594 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,140] - JSON内容验证通过 - 节点总数: 12, 图片数量: 0, 文本节点: 6
11:31:54.595 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,170] - 简单Word导出请求验证通过
11:31:54.658 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,44] - 接收到简单Word导出请求，标题: 简单Word文档
11:31:54.659 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,50] - JSON内容统计 - 总节点: 12, 图片数量: 0, 文本节点: 6
11:31:54.659 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
11:31:55.026 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 9
11:31:55.027 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
11:31:55.027 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 9
11:31:55.033 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
11:31:55.091 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3003 bytes
11:31:55.098 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,75] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250906_113155.docx, 大小: 3003 bytes
11:32:48.115 [http-nio-9335-exec-3] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,122] - 验证简单Word导出请求，标题: 简单Word文档
11:32:48.116 [http-nio-9335-exec-3] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,140] - JSON内容验证通过 - 节点总数: 12, 图片数量: 0, 文本节点: 6
11:32:48.116 [http-nio-9335-exec-3] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,170] - 简单Word导出请求验证通过
11:32:48.131 [http-nio-9335-exec-4] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,44] - 接收到简单Word导出请求，标题: 简单Word文档
11:32:48.132 [http-nio-9335-exec-4] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,50] - JSON内容统计 - 总节点: 12, 图片数量: 0, 文本节点: 6
11:32:48.132 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
11:32:48.139 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 9
11:32:48.139 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
11:32:48.140 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 9
11:33:25.394 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
11:33:25.402 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3003 bytes
11:33:25.405 [http-nio-9335-exec-4] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,75] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250906_113325.docx, 大小: 3003 bytes
11:37:31.111 [http-nio-9335-exec-5] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,122] - 验证简单Word导出请求，标题: 简单Word文档
11:37:31.113 [http-nio-9335-exec-5] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,140] - JSON内容验证通过 - 节点总数: 12, 图片数量: 0, 文本节点: 6
11:37:31.113 [http-nio-9335-exec-5] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,170] - 简单Word导出请求验证通过
11:37:31.129 [http-nio-9335-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,44] - 接收到简单Word导出请求，标题: 简单Word文档
11:37:31.130 [http-nio-9335-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,50] - JSON内容统计 - 总节点: 12, 图片数量: 0, 文本节点: 6
11:37:31.130 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
11:37:31.137 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 9
11:37:31.138 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
11:37:31.138 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 9
11:43:50.256 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
11:43:50.266 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3002 bytes
11:43:50.275 [http-nio-9335-exec-6] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,75] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250906_114350.docx, 大小: 3002 bytes
11:43:50.406 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
11:43:50.413 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
11:43:55.608 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 95138 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
11:43:55.609 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
11:43:56.727 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
11:43:56.728 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:43:56.728 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
11:43:56.779 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:43:57.818 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
11:43:57.907 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
11:43:58.241 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
11:43:58.242 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
11:43:58.548 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
11:43:58.574 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
11:43:58.575 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
11:43:58.652 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
11:43:58.657 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
11:43:58.661 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
11:43:58.661 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
11:43:58.661 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
11:43:58.661 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
11:43:58.662 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
11:43:58.662 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
11:43:58.662 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
11:43:58.664 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
11:43:58.708 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
11:43:58.723 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.564 seconds (JVM running for 4.203)
11:44:09.247 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:44:09.294 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,122] - 验证简单Word导出请求，标题: 简单Word文档
11:44:09.294 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,140] - JSON内容验证通过 - 节点总数: 12, 图片数量: 0, 文本节点: 6
11:44:09.294 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,170] - 简单Word导出请求验证通过
11:44:09.338 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,44] - 接收到简单Word导出请求，标题: 简单Word文档
11:44:09.338 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,50] - JSON内容统计 - 总节点: 12, 图片数量: 0, 文本节点: 6
11:44:09.338 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
11:44:09.669 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 9
11:44:09.670 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
11:44:09.671 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 9
11:44:09.678 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
11:44:09.751 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3003 bytes
11:44:09.758 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,75] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250906_114409.docx, 大小: 3003 bytes
11:45:18.587 [http-nio-9335-exec-3] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,122] - 验证简单Word导出请求，标题: 简单Word文档
11:45:18.589 [http-nio-9335-exec-3] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,140] - JSON内容验证通过 - 节点总数: 12, 图片数量: 0, 文本节点: 6
11:45:18.590 [http-nio-9335-exec-3] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,170] - 简单Word导出请求验证通过
11:45:18.614 [http-nio-9335-exec-4] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,44] - 接收到简单Word导出请求，标题: 简单Word文档
11:45:18.615 [http-nio-9335-exec-4] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,50] - JSON内容统计 - 总节点: 12, 图片数量: 0, 文本节点: 6
11:45:18.615 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
11:45:18.622 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 9
11:45:18.623 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
11:45:18.623 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 9
11:45:49.245 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
11:45:49.251 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3003 bytes
11:45:49.254 [http-nio-9335-exec-4] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,75] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250906_114549.docx, 大小: 3003 bytes
11:47:48.015 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
11:47:48.018 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
11:47:52.329 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 96294 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
11:47:52.330 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
11:47:53.271 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
11:47:53.272 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:47:53.272 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
11:47:53.310 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:47:54.402 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
11:47:54.487 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
11:47:54.922 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
11:47:54.923 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
11:47:55.243 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
11:47:55.270 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
11:47:55.271 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
11:47:55.348 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
11:47:55.353 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
11:47:55.357 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
11:47:55.357 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
11:47:55.357 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
11:47:55.357 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
11:47:55.358 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
11:47:55.358 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
11:47:55.358 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
11:47:55.360 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
11:47:55.404 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
11:47:55.420 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.475 seconds (JVM running for 4.037)
11:48:15.198 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:48:15.249 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,122] - 验证简单Word导出请求，标题: 简单Word文档
11:48:15.249 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,140] - JSON内容验证通过 - 节点总数: 12, 图片数量: 0, 文本节点: 6
11:48:15.250 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,170] - 简单Word导出请求验证通过
11:48:15.290 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,44] - 接收到简单Word导出请求，标题: 简单Word文档
11:48:15.291 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,50] - JSON内容统计 - 总节点: 12, 图片数量: 0, 文本节点: 6
11:48:15.291 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
11:48:15.599 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 9
11:48:15.600 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
11:48:15.601 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 9
11:48:25.626 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
11:48:25.690 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3015 bytes
11:48:25.696 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,75] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250906_114825.docx, 大小: 3015 bytes
11:53:05.345 [http-nio-9335-exec-3] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,122] - 验证简单Word导出请求，标题: 简单Word文档
11:53:05.346 [http-nio-9335-exec-3] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,140] - JSON内容验证通过 - 节点总数: 12, 图片数量: 0, 文本节点: 6
11:53:05.346 [http-nio-9335-exec-3] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,170] - 简单Word导出请求验证通过
11:53:05.358 [http-nio-9335-exec-4] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,44] - 接收到简单Word导出请求，标题: 简单Word文档
11:53:05.359 [http-nio-9335-exec-4] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,50] - JSON内容统计 - 总节点: 12, 图片数量: 0, 文本节点: 6
11:53:05.359 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
11:53:05.366 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 9
11:53:05.366 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
11:53:05.367 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 9
11:54:19.583 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
11:54:19.590 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3014 bytes
11:54:19.593 [http-nio-9335-exec-4] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,75] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250906_115419.docx, 大小: 3014 bytes
11:54:29.114 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
11:54:29.117 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
11:54:34.896 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 98016 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
11:54:34.897 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
11:54:35.830 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
11:54:35.831 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:54:35.831 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
11:54:35.868 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:54:36.876 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
11:54:36.971 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
11:54:37.382 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
11:54:37.383 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
11:54:37.691 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
11:54:37.717 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
11:54:37.719 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
11:54:37.794 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
11:54:37.799 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
11:54:37.802 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
11:54:37.802 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
11:54:37.803 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
11:54:37.803 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
11:54:37.803 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
11:54:37.803 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
11:54:37.804 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
11:54:37.806 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
11:54:37.855 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
11:54:37.871 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.37 seconds (JVM running for 3.953)
11:54:40.977 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:54:41.028 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,122] - 验证简单Word导出请求，标题: 简单Word文档
11:54:41.029 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,140] - JSON内容验证通过 - 节点总数: 12, 图片数量: 0, 文本节点: 6
11:54:41.029 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,170] - 简单Word导出请求验证通过
11:54:41.073 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,44] - 接收到简单Word导出请求，标题: 简单Word文档
11:54:41.074 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,50] - JSON内容统计 - 总节点: 12, 图片数量: 0, 文本节点: 6
11:54:41.075 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
11:54:41.393 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 9
11:54:41.394 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
11:54:41.395 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 9
11:54:41.401 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
11:54:41.463 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3009 bytes
11:54:41.469 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,75] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250906_115441.docx, 大小: 3009 bytes
12:01:37.230 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
12:01:37.234 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
12:01:41.216 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 99785 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
12:01:41.218 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
12:01:42.210 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
12:01:42.210 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:01:42.211 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
12:01:42.247 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:01:43.253 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
12:01:43.341 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
12:01:43.674 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
12:01:43.675 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
12:01:43.972 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
12:01:43.998 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
12:01:43.999 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
12:01:44.075 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
12:01:44.081 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
12:01:44.083 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
12:01:44.084 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
12:01:44.084 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
12:01:44.084 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
12:01:44.084 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
12:01:44.085 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
12:01:44.085 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
12:01:44.087 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
12:01:44.133 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
12:01:44.148 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.324 seconds (JVM running for 3.859)
12:02:03.751 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:02:03.794 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,122] - 验证简单Word导出请求，标题: 简单Word文档
12:02:03.795 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,140] - JSON内容验证通过 - 节点总数: 12, 图片数量: 0, 文本节点: 6
12:02:03.795 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,170] - 简单Word导出请求验证通过
12:02:03.834 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,44] - 接收到简单Word导出请求，标题: 简单Word文档
12:02:03.835 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,50] - JSON内容统计 - 总节点: 12, 图片数量: 0, 文本节点: 6
12:02:03.835 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
12:02:04.160 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 9
12:02:04.162 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
12:02:04.162 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 9
12:02:04.169 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
12:02:04.246 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
12:02:04.252 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,75] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250906_120204.docx, 大小: 3017 bytes
12:02:35.896 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
12:02:35.900 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
12:02:39.960 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 100356 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
12:02:39.961 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
12:02:40.884 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
12:02:40.884 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:02:40.884 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
12:02:40.921 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:02:41.897 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
12:02:41.981 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
12:02:42.293 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
12:02:42.294 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
12:02:42.605 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
12:02:42.631 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
12:02:42.632 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
12:02:42.705 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
12:02:42.709 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
12:02:42.712 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
12:02:42.712 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
12:02:42.713 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
12:02:42.713 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
12:02:42.713 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
12:02:42.713 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
12:02:42.714 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
12:02:42.715 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
12:02:42.758 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
12:02:42.773 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.251 seconds (JVM running for 3.812)
12:02:45.323 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:02:45.387 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,122] - 验证简单Word导出请求，标题: 简单Word文档
12:02:45.387 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,140] - JSON内容验证通过 - 节点总数: 12, 图片数量: 0, 文本节点: 6
12:02:45.388 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,170] - 简单Word导出请求验证通过
12:02:45.423 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,44] - 接收到简单Word导出请求，标题: 简单Word文档
12:02:45.423 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,50] - JSON内容统计 - 总节点: 12, 图片数量: 0, 文本节点: 6
12:02:45.424 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
12:02:45.735 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 9
12:02:45.736 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
12:02:45.736 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 9
12:02:45.746 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
12:02:45.823 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3014 bytes
12:02:45.829 [http-nio-9335-exec-2] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,75] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250906_120245.docx, 大小: 3014 bytes
