11:49:48.326 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
11:49:48.329 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
11:49:48.340 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 4 条
11:49:48.341 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 1 行绑定数据
11:49:48.342 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 51, 检验记录数量: 4
11:49:48.344 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 1 行
11:49:48.346 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
11:49:48.347 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
11:49:48.347 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
11:49:48.348 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
11:49:48.481 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
11:49:48.481 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
11:49:48.547 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
11:49:48.547 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 410磅 (8200twips)
11:49:48.552 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
11:49:48.552 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:49:48.567 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:49:48.571 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:49:48.572 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:49:48.573 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
11:49:48.579 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
11:49:48.580 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:49:48.583 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:49:48.586 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:49:48.609 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 2987 bytes
11:49:48.717 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 5 条
11:49:48.717 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 3 行绑定数据
11:49:48.717 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 52, 检验记录数量: 5
11:49:48.718 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 3 行
11:49:48.718 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
11:49:48.720 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
11:49:48.720 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
11:49:48.720 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 5, 总列数: 8
11:49:48.723 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
11:49:48.723 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
11:49:48.733 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
11:49:48.734 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 14磅 (280twips)
11:49:48.736 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 87磅 (1740twips)
11:49:48.738 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 308磅 (6160twips)
11:49:48.739 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
11:49:48.740 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:49:48.742 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:49:48.744 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:49:48.745 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:49:48.745 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
11:49:48.746 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
11:49:48.747 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:49:48.747 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:49:48.748 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:49:48.755 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3985 bytes
11:49:48.780 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 0 条
11:49:48.780 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 1 行绑定数据
11:49:48.780 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 53, 检验记录数量: 0
11:49:48.781 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 1 行
11:49:48.781 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第3部分
11:49:48.782 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
11:49:48.782 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
11:49:48.782 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
11:49:48.783 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
11:49:48.783 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
11:49:48.795 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
11:49:48.796 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 391磅 (7820twips)
11:49:48.797 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
11:49:48.797 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:49:48.798 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:49:48.799 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:49:48.799 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:49:48.799 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
11:49:48.800 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
11:49:48.800 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:49:48.801 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:49:48.801 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:49:48.808 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 4351 bytes
11:49:48.831 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 5074 bytes
11:49:48.832 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757216988831.docx, 大小: 5074 bytes
11:51:06.970 [http-nio-9335-exec-4] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,104] - 开始测试根据设计表ID导出Word文档，设计表ID: 51
11:51:06.980 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,110] - 查询到检验记录数据 4 条
11:51:06.980 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,160] - 解析行模板绑定关系成功，共 1 行绑定数据
11:51:06.980 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,358] - 开始使用JSON格式生成Word文档，设计表ID: 51, 检验记录数量: 4
11:51:06.980 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,608] - 构建数据行完成，共 1 行
11:51:06.981 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
11:51:06.981 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
11:51:06.982 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
11:51:06.982 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
11:51:06.983 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
11:51:06.983 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
11:51:06.992 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
11:51:06.992 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 410磅 (8200twips)
11:51:06.994 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
11:51:06.994 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:51:06.995 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:51:06.996 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:51:06.996 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:51:06.996 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
11:51:06.997 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
11:51:06.997 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:51:06.998 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:51:06.999 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:51:07.005 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 2987 bytes
11:51:07.008 [http-nio-9335-exec-4] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,109] - Word文档生成成功，设计表ID: 51，文档大小: 2987 bytes
11:51:07.009 [http-nio-9335-exec-4] INFO  c.l.w.c.WorkCheckController - [exportByDesignId,119] - 复杂表格Word文档导出成功，文件名: test1757217067009.docx, 大小: 2987 bytes
11:55:17.848 [http-nio-9335-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,84] - 接收到新JSON格式的表格导出请求，标题: null
11:55:17.848 [http-nio-9335-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,86] - 表头合并数量: 7, 数据合并数量: 0
11:55:17.849 [http-nio-9335-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 检测到嵌套表格，使用增强导出模式
11:55:17.849 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: null
11:55:17.850 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
11:55:17.851 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
11:55:17.851 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 4, 总列数: 8
11:55:17.852 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 960px (19200twips)
11:55:17.853 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
11:55:17.865 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
11:55:17.865 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 208磅 (4160twips)
11:55:17.865 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1621] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
11:55:17.866 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1656] - 开始处理嵌套表格，父单元格内容: 详细检查结果
11:55:17.866 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1736] - 创建嵌套表格: 3行 x 3列
11:55:17.866 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1742] - 开始创建嵌套表格
11:55:17.870 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1714] - 嵌套表格处理完成
11:55:17.871 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 208磅 (4160twips)
11:55:17.872 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1621] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
11:55:17.872 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1656] - 开始处理嵌套表格，父单元格内容: 测试数据
11:55:17.872 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1736] - 创建嵌套表格: 3行 x 3列
11:55:17.872 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1742] - 开始创建嵌套表格
11:55:17.873 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1714] - 嵌套表格处理完成
11:55:17.874 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
11:55:17.875 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:55:17.876 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:55:17.876 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:55:17.877 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:55:17.877 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
11:55:17.878 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
11:55:17.878 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:55:17.879 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:55:17.880 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:55:17.887 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3271 bytes
11:55:17.890 [http-nio-9335-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,109] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250907_115517.docx, 大小: 3271 bytes
14:36:37.257 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 143014 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
14:36:37.260 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:36:38.617 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
14:36:38.618 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:36:38.618 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:36:38.671 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:36:40.000 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:36:40.131 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:36:40.556 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
14:36:40.558 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
14:36:43.036 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
14:36:43.070 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
14:36:43.071 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
14:36:43.166 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
14:36:43.173 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
14:36:43.176 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
14:36:43.177 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
14:36:43.177 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
14:36:43.177 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
14:36:43.178 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
14:36:43.178 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
14:36:43.179 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
14:36:43.181 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
14:36:43.249 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
14:36:43.272 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.575 seconds (JVM running for 6.1)
14:37:07.330 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:11:23.394 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:11:23.398 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:14:05.487 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 151548 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:14:05.489 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:14:06.444 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:14:06.445 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:14:06.445 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:14:06.484 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:14:07.484 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:14:07.568 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:14:07.938 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:14:07.939 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:14:08.229 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:14:08.255 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:14:08.256 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:14:08.329 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:14:08.334 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:14:08.336 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:14:08.337 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:14:08.337 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:14:08.337 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:14:08.337 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:14:08.337 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:14:08.338 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:14:08.340 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:14:08.382 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:14:08.398 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.307 seconds (JVM running for 3.85)
15:14:29.275 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:15:19.686 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:15:19.690 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:15:23.408 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 152064 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:15:23.409 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:15:24.327 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:15:24.328 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:15:24.328 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:15:24.366 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:15:25.335 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:15:25.422 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:15:25.781 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:15:25.782 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:15:26.089 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:15:26.115 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:15:26.116 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:15:26.189 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:15:26.195 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:15:26.198 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:15:26.199 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:15:26.199 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:15:26.199 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:15:26.199 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:15:26.199 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:15:26.200 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:15:26.202 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:15:26.249 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:15:26.266 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.25 seconds (JVM running for 3.796)
15:15:32.934 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:15:33.140 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,564] - 批量插入剩余数据成功，数量: 4
15:17:29.583 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,564] - 批量插入剩余数据成功，数量: 4
15:17:29.787 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:17:29.791 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:17:31.870 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 152732 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:17:31.871 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:17:32.798 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:17:32.799 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:17:32.799 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:17:32.836 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:17:33.867 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:17:33.952 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:17:34.627 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:17:34.628 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:17:35.038 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:17:35.066 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:17:35.068 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:17:35.151 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:17:35.156 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:17:35.158 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:17:35.159 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:17:35.159 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:17:35.159 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:17:35.159 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:17:35.159 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:17:35.160 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:17:35.161 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:17:35.208 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:17:35.224 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.828 seconds (JVM running for 4.424)
15:17:40.213 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:17:40.438 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,564] - 批量插入剩余数据成功，数量: 4
15:18:15.640 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,564] - 批量插入剩余数据成功，数量: 4
15:25:43.323 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:25:43.328 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:25:47.179 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 154588 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:25:47.180 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:25:48.052 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:25:48.053 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:25:48.053 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:25:48.089 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:25:49.081 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:25:49.161 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:25:49.481 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:25:49.482 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:25:49.768 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:25:49.793 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:25:49.794 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:25:49.872 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:25:49.877 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:25:49.880 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:25:49.881 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:25:49.881 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:25:49.881 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:25:49.881 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:25:49.882 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:25:49.882 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:25:49.884 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:25:49.931 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:25:49.949 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.124 seconds (JVM running for 3.661)
15:25:55.737 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:25:55.986 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:25:55.990 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
15:25:55.998 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,114] - 查询到检验记录数据 4 条
15:25:58.432 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,164] - 解析行模板绑定关系成功，共 1 行绑定数据
15:25:58.432 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 51, 检验记录数量: 4
15:25:58.438 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,612] - 构建数据行完成，共 1 行
15:25:58.439 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
15:25:58.440 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:25:58.441 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:25:58.441 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:25:58.494 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
15:25:58.494 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
15:25:58.553 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
15:25:58.553 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 410磅 (8200twips)
15:25:58.559 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
15:25:58.560 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:25:58.563 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:25:58.564 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:25:58.565 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:25:58.565 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
15:25:58.567 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
15:25:58.568 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:25:58.569 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:25:58.569 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:25:58.656 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 2987 bytes
15:25:58.719 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,114] - 查询到检验记录数据 5 条
15:25:58.720 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,164] - 解析行模板绑定关系成功，共 3 行绑定数据
15:25:58.720 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 52, 检验记录数量: 5
15:25:58.721 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,612] - 构建数据行完成，共 3 行
15:25:58.722 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
15:25:58.722 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:25:58.723 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:25:58.723 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 5, 总列数: 8
15:25:58.725 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
15:25:58.725 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
15:25:58.735 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
15:25:58.735 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 14磅 (280twips)
15:25:58.737 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 87磅 (1740twips)
15:25:58.739 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 308磅 (6160twips)
15:25:58.771 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
15:25:58.772 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:25:58.773 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:25:58.774 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:25:58.775 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:25:58.775 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
15:25:58.775 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
15:25:58.776 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:25:58.777 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:25:58.778 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:25:58.784 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3985 bytes
15:25:58.803 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,114] - 查询到检验记录数据 0 条
15:25:58.803 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,164] - 解析行模板绑定关系成功，共 1 行绑定数据
15:25:58.803 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 53, 检验记录数量: 0
15:25:58.804 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,612] - 构建数据行完成，共 1 行
15:25:58.804 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第3部分
15:25:58.804 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:25:58.805 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:25:58.805 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:25:58.806 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
15:25:58.806 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
15:25:58.815 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
15:25:58.815 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 391磅 (7820twips)
15:25:58.817 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
15:25:58.818 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:25:58.819 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:25:58.819 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:25:58.820 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:25:58.820 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
15:25:58.820 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
15:25:58.821 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:25:58.821 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:25:58.822 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:25:58.826 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 4351 bytes
15:25:58.835 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 54, 检验记录数量: 0
15:27:52.791 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:27:52.794 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:27:54.690 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 155223 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:27:54.691 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:27:55.559 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:27:55.560 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:27:55.560 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:27:55.596 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:27:56.536 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:27:56.615 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:27:56.906 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:27:56.907 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:27:57.204 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:27:57.231 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:27:57.232 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:27:57.299 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:27:57.304 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:27:57.306 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:27:57.307 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:27:57.307 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:27:57.307 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:27:57.307 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:27:57.307 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:27:57.308 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:27:57.309 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:27:57.352 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:27:57.366 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.099 seconds (JVM running for 3.673)
15:28:07.177 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:28:07.378 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:28:07.381 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
15:28:07.389 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,114] - 查询到检验记录数据 4 条
15:28:07.457 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,164] - 解析行模板绑定关系成功，共 1 行绑定数据
15:28:07.457 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 51, 检验记录数量: 4
15:28:07.462 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,612] - 构建数据行完成，共 1 行
15:28:07.463 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
15:28:07.464 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:28:07.464 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:28:07.465 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:28:07.510 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
15:28:07.510 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
15:28:07.567 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
15:28:07.568 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 410磅 (8200twips)
15:28:07.573 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
15:28:07.574 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:28:07.577 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:28:07.577 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:28:07.578 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:28:07.578 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
15:28:07.580 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
15:28:07.581 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:28:07.583 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:28:07.584 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:28:07.657 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 2987 bytes
15:28:07.718 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,114] - 查询到检验记录数据 5 条
15:28:07.718 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,164] - 解析行模板绑定关系成功，共 3 行绑定数据
15:28:07.718 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 52, 检验记录数量: 5
15:28:07.720 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,612] - 构建数据行完成，共 3 行
15:28:07.720 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
15:28:07.721 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:28:07.721 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:28:07.721 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 5, 总列数: 8
15:28:07.724 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
15:28:07.724 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
15:28:07.732 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
15:28:07.733 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 14磅 (280twips)
15:28:07.734 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 87磅 (1740twips)
15:28:07.736 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 308磅 (6160twips)
15:28:07.767 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
15:28:07.768 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:28:07.769 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:28:07.770 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:28:07.770 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:28:07.771 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
15:28:07.771 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
15:28:07.772 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:28:07.772 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:28:07.773 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:28:07.778 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3985 bytes
15:28:07.796 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,114] - 查询到检验记录数据 0 条
15:28:07.796 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,164] - 解析行模板绑定关系成功，共 1 行绑定数据
15:28:07.796 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 53, 检验记录数量: 0
15:28:07.797 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,612] - 构建数据行完成，共 1 行
15:28:07.797 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第3部分
15:28:07.798 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:28:07.798 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:28:07.798 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:28:07.799 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
15:28:07.799 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
15:28:07.808 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
15:28:07.808 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 391磅 (7820twips)
15:28:07.809 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
15:28:07.810 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:28:07.810 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:28:07.811 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:28:07.812 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:28:07.812 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
15:28:07.812 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
15:28:07.813 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:28:07.813 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:28:07.814 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:28:07.819 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 4351 bytes
15:28:07.831 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 54, 检验记录数量: 0
15:29:00.044 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:29:00.045 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
15:29:00.049 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,114] - 查询到检验记录数据 4 条
15:29:00.049 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,164] - 解析行模板绑定关系成功，共 1 行绑定数据
15:29:00.049 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 51, 检验记录数量: 4
15:29:00.049 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,612] - 构建数据行完成，共 1 行
15:29:00.050 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
15:29:00.050 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:29:00.051 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:29:00.051 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:29:00.052 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
15:29:00.052 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
15:29:00.060 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
15:29:00.060 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 410磅 (8200twips)
15:29:00.061 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
15:29:00.062 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:29:00.062 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:29:00.063 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:29:00.063 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:29:00.063 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
15:29:00.064 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
15:29:00.064 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:29:00.065 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:29:00.065 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:29:00.069 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 2987 bytes
15:29:00.082 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,114] - 查询到检验记录数据 5 条
15:29:00.082 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,164] - 解析行模板绑定关系成功，共 3 行绑定数据
15:29:00.083 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 52, 检验记录数量: 5
15:29:00.083 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,612] - 构建数据行完成，共 3 行
15:29:00.083 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
15:29:00.084 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:29:00.084 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:29:00.084 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 5, 总列数: 8
15:29:00.085 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
15:29:00.085 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
15:29:00.094 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
15:29:00.094 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 14磅 (280twips)
15:29:00.095 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 87磅 (1740twips)
15:29:00.097 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 308磅 (6160twips)
15:29:00.097 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
15:29:00.098 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:29:00.099 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:29:00.100 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:29:00.100 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:29:00.100 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
15:29:00.101 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
15:29:00.101 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:29:00.102 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:29:00.102 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:29:00.106 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3985 bytes
15:29:00.116 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,114] - 查询到检验记录数据 0 条
15:29:00.117 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,164] - 解析行模板绑定关系成功，共 1 行绑定数据
15:29:00.117 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 53, 检验记录数量: 0
15:29:00.117 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,612] - 构建数据行完成，共 1 行
15:29:00.117 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第3部分
15:29:00.118 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:29:00.118 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:29:00.119 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:29:00.119 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
15:29:00.119 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
15:29:00.126 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
15:29:00.126 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 391磅 (7820twips)
15:29:00.127 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
15:29:00.127 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:29:00.127 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:29:00.127 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:29:00.128 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:29:00.128 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
15:29:00.128 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
15:29:00.128 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:29:00.128 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:29:00.129 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:29:00.133 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 4351 bytes
15:29:00.140 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 54, 检验记录数量: 0
15:29:38.612 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:29:39.038 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
15:29:52.165 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:29:52.268 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:29:52.272 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
15:29:52.273 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:29:52.294 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:29:52.337 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
15:30:01.095 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,114] - 查询到检验记录数据 4 条
15:30:01.095 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,164] - 解析行模板绑定关系成功，共 1 行绑定数据
15:30:01.095 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 67, 检验记录数量: 4
15:30:01.096 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,612] - 构建数据行完成，共 1 行
15:30:01.096 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
15:30:01.097 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:30:01.097 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:30:01.097 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:30:01.098 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
15:30:01.098 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
15:30:01.102 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
15:30:01.102 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 410磅 (8200twips)
15:30:01.103 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
15:30:01.103 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:30:01.104 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:30:01.104 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:30:01.104 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:30:01.104 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
15:30:01.105 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
15:30:01.105 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:30:01.105 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:30:01.105 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:30:01.108 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 2986 bytes
15:30:01.119 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,114] - 查询到检验记录数据 5 条
15:30:01.119 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,164] - 解析行模板绑定关系成功，共 3 行绑定数据
15:30:01.119 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 68, 检验记录数量: 5
15:30:01.119 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,612] - 构建数据行完成，共 3 行
15:30:01.120 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第3部分
15:30:01.120 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:30:01.120 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:30:01.121 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 5, 总列数: 8
15:30:01.121 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
15:30:01.121 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
15:30:01.126 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
15:30:01.126 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 14磅 (280twips)
15:30:01.127 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 87磅 (1740twips)
15:30:01.129 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 308磅 (6160twips)
15:30:01.129 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
15:30:01.129 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:30:01.130 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:30:01.130 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:30:01.130 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:30:01.130 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
15:30:01.131 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
15:30:01.131 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:30:01.132 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:30:01.132 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:30:01.136 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3984 bytes
15:30:01.148 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,114] - 查询到检验记录数据 0 条
15:30:01.148 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,164] - 解析行模板绑定关系成功，共 1 行绑定数据
15:30:01.148 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 69, 检验记录数量: 0
15:30:01.148 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,612] - 构建数据行完成，共 1 行
15:30:01.148 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
15:30:01.149 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:30:01.149 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:30:01.149 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:30:01.150 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
15:30:01.150 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
15:30:01.154 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
15:30:01.154 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 391磅 (7820twips)
15:30:01.155 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
15:30:01.155 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:30:01.156 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:30:01.156 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:30:01.156 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:30:01.156 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
15:30:01.157 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
15:30:01.157 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:30:01.157 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:30:01.157 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:30:01.163 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 4350 bytes
15:30:01.176 [http-nio-9335-exec-4] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 5239 bytes
15:30:01.178 [http-nio-9335-exec-4] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757230201176.docx, 大小: 5239 bytes
15:32:19.361 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,564] - 批量插入剩余数据成功，数量: 5
15:33:29.693 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:33:29.693 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
15:33:29.694 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:33:29.696 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:33:29.696 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
15:33:29.697 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:33:29.699 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:33:29.716 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
15:33:29.726 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:33:29.736 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:33:29.736 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
15:33:29.737 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:33:29.738 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:33:29.739 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
15:33:29.740 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
15:33:29.791 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
15:33:29.791 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:33:29.793 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:33:29.795 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
15:33:29.808 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,114] - 查询到检验记录数据 4 条
15:33:29.808 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,164] - 解析行模板绑定关系成功，共 1 行绑定数据
15:33:29.808 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 72, 检验记录数量: 4
15:33:29.808 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,612] - 构建数据行完成，共 1 行
15:33:29.808 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第3部分
15:33:29.809 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:33:29.809 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:33:29.809 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:33:29.810 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
15:33:29.810 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
15:33:29.814 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
15:33:29.814 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 410磅 (8200twips)
15:33:29.815 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
15:33:29.815 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:33:29.816 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:33:29.816 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:33:29.816 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:33:29.816 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
15:33:29.816 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
15:33:29.816 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:33:29.817 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:33:29.817 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:33:29.820 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 2987 bytes
15:33:29.830 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,114] - 查询到检验记录数据 5 条
15:33:29.830 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,164] - 解析行模板绑定关系成功，共 3 行绑定数据
15:33:29.830 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 73, 检验记录数量: 5
15:33:29.831 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,612] - 构建数据行完成，共 3 行
15:33:29.831 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
15:33:29.832 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:33:29.832 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:33:29.832 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 5, 总列数: 8
15:33:29.833 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
15:33:29.833 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
15:33:29.836 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
15:33:29.837 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 14磅 (280twips)
15:33:29.838 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 87磅 (1740twips)
15:33:29.839 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 308磅 (6160twips)
15:33:29.839 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
15:33:29.840 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:33:29.840 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:33:29.840 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:33:29.840 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:33:29.840 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
15:33:29.841 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
15:33:29.841 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:33:29.841 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:33:29.841 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:33:29.844 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3985 bytes
15:33:29.853 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,114] - 查询到检验记录数据 0 条
15:33:29.854 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,164] - 解析行模板绑定关系成功，共 1 行绑定数据
15:33:29.854 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 74, 检验记录数量: 0
15:33:29.854 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,612] - 构建数据行完成，共 1 行
15:33:29.854 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第5部分
15:33:29.855 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:33:29.855 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:33:29.855 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:33:29.855 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
15:33:29.855 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
15:33:29.859 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
15:33:29.859 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 391磅 (7820twips)
15:33:29.860 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
15:33:29.860 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:33:29.860 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:33:29.861 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:33:29.861 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:33:29.861 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
15:33:29.861 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
15:33:29.861 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:33:29.861 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:33:29.862 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:33:29.865 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 4351 bytes
15:33:29.876 [http-nio-9335-exec-9] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 5615 bytes
15:33:29.876 [http-nio-9335-exec-9] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757230409876.docx, 大小: 5615 bytes
15:34:03.246 [http-nio-9335-exec-10] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,122] - 验证简单Word导出请求，标题: 简单Word文档
15:34:03.248 [http-nio-9335-exec-10] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,140] - JSON内容验证通过 - 节点总数: 10, 图片数量: 1, 文本节点: 6
15:34:03.248 [http-nio-9335-exec-10] INFO  c.l.w.c.SimpleWordExportController - [validateExportRequest,170] - 简单Word导出请求验证通过
15:34:03.267 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,44] - 接收到简单Word导出请求，标题: 简单Word文档
15:34:03.268 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,50] - JSON内容统计 - 总节点: 10, 图片数量: 1, 文本节点: 6
15:34:03.268 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:34:03.269 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:34:03.270 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
15:34:03.270 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:34:03.270 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:34:03.271 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
15:34:03.271 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
15:34:03.272 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
15:34:03.273 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:34:03.274 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:34:03.277 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
15:34:03.279 [http-nio-9335-exec-1] INFO  c.l.w.c.SimpleWordExportController - [exportSimpleWord,75] - 简单Word文档导出成功，文件名: %E7%AE%80%E5%8D%95Word%E6%96%87%E6%A1%A3_20250907_153403.docx, 大小: 5437 bytes
15:42:08.290 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:42:08.291 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
15:42:08.291 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:42:08.293 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:42:08.293 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
15:42:08.293 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:42:08.297 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:42:08.299 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
15:42:08.307 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:42:08.308 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:42:08.308 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
15:42:08.308 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:42:42.490 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:42:48.262 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
15:42:48.266 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
15:42:48.277 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
15:42:49.819 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:43:05.123 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:43:05.126 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
15:43:05.138 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,114] - 查询到检验记录数据 4 条
15:43:05.138 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,164] - 解析行模板绑定关系成功，共 1 行绑定数据
15:43:05.138 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 72, 检验记录数量: 4
15:43:05.138 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,612] - 构建数据行完成，共 1 行
15:43:05.139 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第3部分
15:43:05.139 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:43:05.140 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:43:05.140 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:43:05.140 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
15:43:05.140 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
15:43:05.145 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
15:43:05.145 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 410磅 (8200twips)
15:43:05.146 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
15:43:05.146 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:43:05.146 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:43:05.147 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:43:05.147 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:43:05.147 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
15:43:05.147 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
15:43:05.147 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:43:05.147 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:43:05.148 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:43:05.151 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 2987 bytes
15:43:05.160 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,114] - 查询到检验记录数据 5 条
15:43:05.161 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,164] - 解析行模板绑定关系成功，共 3 行绑定数据
15:43:05.161 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 73, 检验记录数量: 5
15:43:05.161 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,612] - 构建数据行完成，共 3 行
15:43:05.161 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
15:43:05.162 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:43:05.162 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:43:05.162 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 5, 总列数: 8
15:43:05.163 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
15:43:05.163 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
15:43:05.166 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
15:43:05.167 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 14磅 (280twips)
15:43:05.167 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 87磅 (1740twips)
15:43:05.168 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 308磅 (6160twips)
15:43:05.169 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
15:43:05.169 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:43:05.169 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:43:05.170 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:43:05.170 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:43:05.170 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
15:43:05.170 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
15:43:05.170 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:43:05.170 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:43:05.171 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:43:05.174 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3985 bytes
15:43:05.184 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,114] - 查询到检验记录数据 0 条
15:43:05.184 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,164] - 解析行模板绑定关系成功，共 1 行绑定数据
15:43:05.184 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,362] - 开始使用JSON格式生成Word文档，设计表ID: 74, 检验记录数量: 0
15:43:05.185 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,612] - 构建数据行完成，共 1 行
15:43:05.185 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第5部分
15:43:05.185 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:43:05.185 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
15:43:05.186 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
15:43:05.186 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
15:43:05.186 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
15:43:05.190 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
15:43:05.190 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 391磅 (7820twips)
15:43:05.191 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
15:43:05.191 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:43:05.191 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:43:05.192 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:43:05.192 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:43:05.192 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
15:43:05.192 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
15:43:05.192 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:43:05.192 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:43:05.193 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:43:05.195 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 4351 bytes
15:43:05.206 [http-nio-9335-exec-3] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 5615 bytes
15:43:05.206 [http-nio-9335-exec-3] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757230985206.docx, 大小: 5615 bytes
15:48:13.149 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
15:48:13.149 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
15:48:13.150 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
15:48:13.152 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
15:48:13.152 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
15:48:13.152 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
15:48:20.111 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:48:20.111 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
15:48:20.112 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
15:48:20.114 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
15:48:20.114 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
15:48:20.116 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
15:48:20.120 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
15:48:56.059 [http-nio-9335-exec-7] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 2871 bytes
15:48:56.059 [http-nio-9335-exec-7] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757231336059.docx, 大小: 2871 bytes
