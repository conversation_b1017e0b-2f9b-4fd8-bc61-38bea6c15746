10:48:31.504 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 158268 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
10:48:31.507 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:48:32.726 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
10:48:32.727 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:48:32.727 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:48:32.774 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:48:34.152 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:48:34.282 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:48:34.648 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:48:34.649 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:48:34.997 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:48:35.026 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:48:35.027 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:48:35.104 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:48:35.110 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:48:35.112 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:48:35.113 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:48:35.113 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:48:35.113 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:48:35.113 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:48:35.113 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:48:35.114 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:48:35.116 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:48:35.172 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
10:48:35.197 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.153 seconds (JVM running for 4.776)
10:49:09.699 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:50:28.253 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
10:50:28.254 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
10:50:28.259 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
10:50:28.260 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
11:30:00.151 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
11:30:00.158 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
11:30:00.162 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
11:30:00.163 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
11:34:07.464 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
11:34:07.466 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
11:34:07.469 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
11:34:07.470 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
12:23:25.499 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
12:23:25.500 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
12:23:25.504 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
12:23:25.505 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
12:30:08.186 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
12:30:08.187 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
12:30:08.190 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
12:30:08.192 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
12:31:33.719 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
12:31:33.720 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
12:31:33.723 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
12:31:33.724 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
12:59:36.558 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
12:59:36.560 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
12:59:36.564 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
12:59:36.565 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:11:03.271 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:11:03.272 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:11:03.276 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:11:03.277 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:26:01.609 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:26:01.610 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:26:01.614 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:26:01.615 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:30:55.825 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:30:55.826 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:30:55.829 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:30:55.830 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:33:08.154 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:33:08.155 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:33:08.158 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:33:08.159 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:34:26.971 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:34:26.972 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:34:26.975 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:34:26.976 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:38:36.981 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:38:36.982 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:38:36.986 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:38:36.986 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:47:17.997 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:47:17.998 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:47:18.010 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:47:18.010 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:47:38.117 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:47:38.118 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:47:38.121 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:47:38.122 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:48:17.333 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:48:17.333 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:48:17.340 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:48:17.341 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:49:03.366 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:49:03.368 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:49:03.371 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:49:03.372 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:59:06.007 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:59:06.008 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:59:06.012 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:59:06.014 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:59:10.931 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:59:10.932 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:59:10.935 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:59:10.935 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
14:40:43.666 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
14:40:43.677 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
16:54:23.932 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 238705 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
16:54:23.935 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
16:54:25.074 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
16:54:25.075 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:54:25.075 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
16:54:25.122 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:54:26.346 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
16:54:26.443 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
16:54:26.779 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
16:54:26.780 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
16:54:27.133 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
16:54:27.163 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
16:54:27.164 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
16:54:27.243 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
16:54:27.248 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
16:54:27.251 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
16:54:27.252 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
16:54:27.252 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
16:54:27.252 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
16:54:27.252 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
16:54:27.252 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
16:54:27.253 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
16:54:27.255 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
16:54:27.307 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
16:54:27.325 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.924 seconds (JVM running for 4.506)
16:55:46.634 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:55:46.837 [http-nio-9335-exec-1] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 8
16:55:46.837 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
16:55:46.838 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 8
16:55:46.844 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
16:55:46.844 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
16:55:46.852 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
16:55:47.058 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
16:55:47.059 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 6, 总列数: 8
16:55:47.145 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
16:55:47.146 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
16:55:47.238 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
16:55:47.239 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:55:47.246 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:55:47.248 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:55:47.251 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:55:47.254 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
16:55:47.256 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:55:47.260 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:55:47.261 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:55:47.262 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:55:47.263 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
16:55:47.265 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
16:55:47.266 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:55:47.266 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:55:47.268 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:55:47.409 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3036 bytes
16:55:47.417 [http-nio-9335-exec-1] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC8%E9%A1%B5_20250901_165547.docx, 大小: 3036 bytes
16:57:09.036 [http-nio-9335-exec-3] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
16:57:09.036 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
16:57:09.037 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
16:57:09.041 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
16:57:09.041 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
16:57:09.043 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
16:57:09.045 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
16:57:09.046 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
16:57:09.050 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
16:57:09.050 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
16:57:09.058 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
16:57:09.059 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:57:09.061 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:57:09.062 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:57:09.065 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:57:09.066 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
16:57:09.067 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
16:57:11.876 [http-nio-9335-exec-4] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
16:57:11.876 [http-nio-9335-exec-4] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
16:57:11.876 [http-nio-9335-exec-4] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
16:57:11.880 [http-nio-9335-exec-4] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
16:57:11.881 [http-nio-9335-exec-4] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
16:57:11.882 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
16:57:11.883 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
16:57:11.883 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
16:57:11.887 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
16:57:11.888 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
16:57:11.892 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
16:57:11.893 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:57:11.894 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:57:11.896 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:57:11.897 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:57:11.898 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
16:57:11.899 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
16:59:14.059 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
16:59:14.066 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
16:59:17.693 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 240633 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
16:59:17.697 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
16:59:18.964 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
16:59:18.965 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:59:18.965 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
16:59:19.032 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:59:20.469 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
16:59:20.603 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
16:59:21.013 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
16:59:21.014 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
16:59:21.415 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
16:59:21.448 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
16:59:21.450 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
16:59:21.551 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
16:59:21.559 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
16:59:21.562 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
16:59:21.563 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
16:59:21.563 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
16:59:21.563 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
16:59:21.563 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
16:59:21.563 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
16:59:21.564 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
16:59:21.566 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
16:59:21.637 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
16:59:21.671 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.585 seconds (JVM running for 5.153)
16:59:25.237 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:59:25.431 [http-nio-9335-exec-1] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
16:59:25.432 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
16:59:25.432 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
16:59:25.439 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
16:59:25.440 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
16:59:25.446 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
16:59:25.747 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
16:59:25.747 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
16:59:25.833 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
16:59:25.833 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
16:59:25.934 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
16:59:25.935 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:59:25.942 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:59:25.947 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:59:25.951 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:59:25.954 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
16:59:25.955 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
16:59:25.958 [http-nio-9335-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
16:59:26.340 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
16:59:26.343 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
16:59:26.344 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
16:59:26.346 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
16:59:26.348 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
16:59:26.350 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:59:26.354 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:59:26.355 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:59:26.356 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:59:26.356 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
16:59:26.358 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
16:59:26.359 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:59:26.359 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:59:26.361 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:59:26.447 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3287 bytes
16:59:26.457 [http-nio-9335-exec-1] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250901_165926.docx, 大小: 3287 bytes
17:00:05.304 [http-nio-9335-exec-2] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
17:00:05.304 [http-nio-9335-exec-2] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
17:00:05.304 [http-nio-9335-exec-2] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
17:00:05.309 [http-nio-9335-exec-2] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
17:00:05.309 [http-nio-9335-exec-2] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
17:00:05.310 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
17:00:05.314 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
17:00:05.314 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
17:00:05.317 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:00:05.318 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
17:00:05.325 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
17:00:05.325 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:00:05.328 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:00:05.331 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:00:05.333 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:00:05.334 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
17:00:05.335 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:00:05.336 [http-nio-9335-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:00:05.469 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:00:05.470 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:00:05.472 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:00:05.473 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:00:05.475 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:00:05.475 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:00:05.476 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:00:05.477 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:00:05.477 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:00:05.477 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:00:05.478 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:00:05.478 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:00:05.478 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:00:05.479 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:00:05.490 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3286 bytes
17:00:05.496 [http-nio-9335-exec-2] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250901_170005.docx, 大小: 3286 bytes
17:00:27.583 [http-nio-9335-exec-3] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
17:00:27.584 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
17:00:27.584 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
17:00:27.589 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
17:00:27.592 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
17:00:27.595 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
17:00:27.597 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
17:00:27.598 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
17:00:27.603 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:00:27.603 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
17:00:27.611 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
17:00:27.612 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:00:27.613 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:00:27.614 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:00:27.615 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:00:27.617 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
17:00:27.617 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:00:27.617 [http-nio-9335-exec-3] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:00:27.716 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:00:27.717 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:00:27.717 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:00:27.718 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:00:27.719 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:00:27.720 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:00:27.720 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:00:27.721 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:00:27.721 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:00:27.721 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:00:27.722 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:00:27.722 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:00:27.722 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:00:27.723 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:00:27.730 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3286 bytes
17:00:27.734 [http-nio-9335-exec-3] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250901_170027.docx, 大小: 3286 bytes
17:02:13.567 [http-nio-9335-exec-5] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
17:02:13.568 [http-nio-9335-exec-5] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
17:02:13.569 [http-nio-9335-exec-5] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
17:02:13.579 [http-nio-9335-exec-5] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
17:02:13.580 [http-nio-9335-exec-5] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
17:02:13.582 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
17:02:13.583 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
17:02:13.584 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
17:02:13.586 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:02:13.587 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
17:02:13.591 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
17:02:13.592 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:02:13.595 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:02:13.597 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:02:13.598 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:02:13.600 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
17:02:13.601 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:02:13.602 [http-nio-9335-exec-5] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:02:13.750 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:02:13.752 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:02:13.754 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:02:13.755 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:02:13.756 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:02:13.758 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:02:13.760 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:02:13.761 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:02:13.762 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:02:13.763 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:02:13.763 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:02:13.764 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:02:13.764 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:02:13.765 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:02:13.778 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3286 bytes
17:02:13.788 [http-nio-9335-exec-5] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250901_170213.docx, 大小: 3286 bytes
17:02:14.956 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
17:02:14.957 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
17:02:14.961 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
17:02:14.961 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
17:08:09.444 [http-nio-9335-exec-4] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
17:08:09.445 [http-nio-9335-exec-4] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
17:08:09.445 [http-nio-9335-exec-4] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
17:08:09.449 [http-nio-9335-exec-4] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
17:08:09.450 [http-nio-9335-exec-4] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
17:08:09.452 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
17:08:09.454 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
17:08:09.454 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
17:08:09.456 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:08:09.457 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
17:08:09.460 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
17:08:09.460 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:08:09.461 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:08:09.462 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:08:09.462 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:08:09.463 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
17:08:09.463 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:08:09.463 [http-nio-9335-exec-4] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:08:09.553 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:08:09.554 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:08:09.554 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:08:09.555 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:08:09.555 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:08:09.556 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:08:09.557 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:08:09.558 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:08:09.559 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:08:09.559 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:08:09.559 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:08:09.560 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:08:09.560 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:08:09.560 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:08:09.569 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3286 bytes
17:08:09.573 [http-nio-9335-exec-4] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250901_170809.docx, 大小: 3286 bytes
17:09:12.657 [http-nio-9335-exec-6] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
17:09:12.657 [http-nio-9335-exec-6] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
17:09:12.657 [http-nio-9335-exec-6] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
17:09:12.662 [http-nio-9335-exec-6] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
17:09:12.663 [http-nio-9335-exec-6] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
17:09:12.664 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
17:09:12.666 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
17:09:12.666 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
17:09:12.667 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:09:12.668 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
17:09:12.671 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
17:09:12.672 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:09:12.673 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:09:12.674 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:09:12.675 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:09:12.677 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
17:09:12.678 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:09:12.678 [http-nio-9335-exec-6] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:09:12.786 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:09:12.788 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:09:12.788 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:09:12.789 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:09:12.790 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:09:12.790 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:09:12.791 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:09:12.791 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:09:12.792 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:09:12.792 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:09:12.792 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:09:12.793 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:09:12.794 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:09:12.795 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:09:12.803 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3286 bytes
17:09:12.807 [http-nio-9335-exec-6] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250901_170912.docx, 大小: 3286 bytes
17:11:39.333 [http-nio-9335-exec-9] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
17:11:39.333 [http-nio-9335-exec-9] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
17:11:39.333 [http-nio-9335-exec-9] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
17:11:39.340 [http-nio-9335-exec-9] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
17:11:39.341 [http-nio-9335-exec-9] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
17:11:39.342 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
17:11:39.343 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
17:11:39.344 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
17:11:39.344 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:11:39.345 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
17:11:39.347 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
17:11:39.347 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:11:39.347 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:11:39.348 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:11:39.348 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:11:39.349 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
17:11:39.349 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:11:39.350 [http-nio-9335-exec-9] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:11:39.426 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:11:39.427 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:11:39.427 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:11:39.428 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:11:39.429 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:11:39.429 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:11:39.429 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:11:39.430 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:11:39.431 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:11:39.431 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:11:39.431 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:11:39.432 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:11:39.433 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:11:39.434 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:11:39.442 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3286 bytes
17:11:39.447 [http-nio-9335-exec-9] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250901_171139.docx, 大小: 3286 bytes
17:15:53.430 [http-nio-9335-exec-9] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
17:15:53.430 [http-nio-9335-exec-9] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
17:15:53.431 [http-nio-9335-exec-9] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
17:15:53.435 [http-nio-9335-exec-9] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
17:15:53.435 [http-nio-9335-exec-9] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
17:15:53.437 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
17:15:53.438 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
17:15:53.438 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
17:15:53.439 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:15:53.439 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
17:15:53.442 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
17:15:53.443 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:15:53.444 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:15:53.446 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:15:53.447 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:15:53.448 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
17:15:53.449 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:15:53.449 [http-nio-9335-exec-9] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:15:53.576 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:15:53.577 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:15:53.578 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:15:53.579 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:15:53.579 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:15:53.579 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:15:53.580 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:15:53.580 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:15:53.581 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:15:53.582 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:15:53.582 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:15:53.583 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:15:53.584 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:15:53.585 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:15:53.594 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3286 bytes
17:15:53.598 [http-nio-9335-exec-9] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250901_171553.docx, 大小: 3286 bytes
17:18:43.940 [http-nio-9335-exec-1] INFO  c.l.w.c.CheckRecordExportController - [exportAllPages,94] - 接收到导出全部页面请求，车辆ID: 0822
17:18:43.941 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: all_pages
17:18:43.941 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportAllPages,81] - 导出全部页面，车辆ID: 0822
17:18:43.948 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
17:18:43.948 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
17:18:43.949 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,740] - 开始导出多页面文档，页面数量: 8
17:18:43.949 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,761] - 处理第1页数据，页面名称: 第1页
17:18:43.950 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [buildCellRowsForSinglePage,665] - 页面1构建了9行数据
17:18:43.950 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,761] - 处理第2页数据，页面名称: 第2页
17:18:43.950 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [buildCellRowsForSinglePage,665] - 页面2构建了5行数据
17:18:43.951 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,761] - 处理第3页数据，页面名称: 第3页
17:18:43.951 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [buildCellRowsForSinglePage,665] - 页面3构建了8行数据
17:18:43.951 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,761] - 处理第4页数据，页面名称: 第4页
17:18:43.952 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [buildCellRowsForSinglePage,665] - 页面4构建了8行数据
17:18:43.952 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,761] - 处理第5页数据，页面名称: 第5页
17:18:43.953 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [buildCellRowsForSinglePage,665] - 页面5构建了7行数据
17:18:43.953 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,761] - 处理第6页数据，页面名称: 第6页
17:18:43.953 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [buildCellRowsForSinglePage,665] - 页面6构建了5行数据
17:18:43.953 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,761] - 处理第7页数据，页面名称: 第7页
17:18:43.954 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [buildCellRowsForSinglePage,665] - 页面7构建了6行数据
17:18:43.954 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,761] - 处理第8页数据，页面名称: 第8页
17:18:43.962 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [buildCellRowsForSinglePage,665] - 页面8构建了4行数据
17:18:43.962 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWordWithPageBreaks,2248] - 开始导出支持分页符的JSON格式Word文档，表格标题: 检验记录表
17:18:43.964 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
17:18:43.970 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2282] - 创建支持分页符的JSON格式表格，表头行数: 2, 总列数: 8
17:18:43.971 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [splitRowsByPageBreaks,2360] - 数据行按分页符分割完成，共8页
17:18:43.972 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:18:43.975 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:43.976 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:43.977 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:43.978 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:43.979 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
17:18:43.979 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:18:43.980 [http-nio-9335-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:18:44.072 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:18:44.073 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:18:44.074 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:18:44.074 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:18:44.075 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:18:44.075 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:18:44.076 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:18:44.076 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:18:44.076 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:18:44.077 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:18:44.077 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:18:44.077 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:18:44.078 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:18:44.078 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:18:44.079 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2323] - 第1页表格创建完成，数据行数: 9
17:18:44.084 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:18:44.085 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.086 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.086 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.087 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.087 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 120px (1800twips)
17:18:44.088 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:18:44.088 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:18:44.088 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:18:44.089 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:18:44.089 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:18:44.089 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:18:44.089 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:18:44.090 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:18:44.090 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:18:44.091 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:18:44.091 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2323] - 第2页表格创建完成，数据行数: 5
17:18:44.092 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:18:44.094 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.095 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.095 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.096 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.096 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:18:44.096 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:18:44.097 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:18:44.097 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:18:44.098 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:18:44.098 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:18:44.098 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:18:44.099 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:18:44.099 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:18:44.100 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:18:44.100 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:18:44.101 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:18:44.101 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:18:44.102 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:18:44.102 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2323] - 第3页表格创建完成，数据行数: 8
17:18:44.103 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:18:44.105 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.105 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.106 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.107 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.107 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 158px (2370twips)
17:18:44.107 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
hello
h
2.ssa去除浮漆
__MATH_FORMULA_1__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>, __MATH_FORMULA_1__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:18:44.108 [http-nio-9335-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:18:44.200 [http-nio-9335-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:18:44.318 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 79px (1185twips)
17:18:44.318 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:18:44.319 [http-nio-9335-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:18:44.423 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 79px (1185twips)
17:18:44.424 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 2.ssa去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:18:44.424 [http-nio-9335-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:18:44.506 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 79px (1185twips)
17:18:44.507 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:18:44.507 [http-nio-9335-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:18:44.586 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:18:44.587 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:18:44.588 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:18:44.588 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:18:44.588 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:18:44.589 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:18:44.589 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:18:44.589 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:18:44.589 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:18:44.590 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:18:44.590 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2323] - 第4页表格创建完成，数据行数: 8
17:18:44.591 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:18:44.594 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.595 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.596 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.596 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.597 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 158px (2370twips)
17:18:44.597 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
hello
h
2.ssa去除浮漆
__MATH_FORMULA_1__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>, __MATH_FORMULA_1__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:18:44.598 [http-nio-9335-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:18:44.686 [http-nio-9335-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:18:44.791 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 79px (1185twips)
17:18:44.792 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:18:44.792 [http-nio-9335-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:18:44.864 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 79px (1185twips)
17:18:44.864 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 2.ssa去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:18:44.864 [http-nio-9335-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:18:44.946 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:18:44.947 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:18:44.947 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:18:44.948 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:18:44.948 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:18:44.949 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:18:44.949 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:18:44.949 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:18:44.950 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:18:44.950 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:18:44.951 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2323] - 第5页表格创建完成，数据行数: 7
17:18:44.952 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:18:44.953 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.953 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.954 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.954 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:44.955 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 158px (2370twips)
17:18:44.955 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 2.ssa去除浮漆
__MATH_FORMULA_0__
hello
h
1.去除浮漆
__MATH_FORMULA_1__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>, __MATH_FORMULA_1__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:18:44.955 [http-nio-9335-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:18:45.041 [http-nio-9335-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:18:45.128 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:18:45.129 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:18:45.129 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:18:45.129 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:18:45.129 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:18:45.130 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:18:45.130 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:18:45.130 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:18:45.130 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:18:45.130 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:18:45.131 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2323] - 第6页表格创建完成，数据行数: 5
17:18:45.131 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:18:45.133 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:45.134 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:45.134 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:45.135 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:45.135 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 79px (1185twips)
17:18:45.135 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:18:45.136 [http-nio-9335-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:18:45.228 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 79px (1185twips)
17:18:45.229 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 2.ssa去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:18:45.230 [http-nio-9335-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:18:45.307 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:18:45.307 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:18:45.308 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:18:45.308 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:18:45.308 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:18:45.308 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:18:45.309 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:18:45.309 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:18:45.309 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:18:45.310 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:18:45.310 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2323] - 第7页表格创建完成，数据行数: 6
17:18:45.311 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:18:45.312 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:45.313 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:45.313 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:45.314 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:18:45.314 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:18:45.314 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:18:45.315 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:18:45.315 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:18:45.315 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:18:45.315 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:18:45.315 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:18:45.316 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:18:45.316 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:18:45.317 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:18:45.317 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2323] - 第8页表格创建完成，数据行数: 4
17:18:45.333 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWordWithPageBreaks,2269] - 支持分页符的JSON格式Word文档导出完成，文件大小: 4977 bytes
17:18:45.337 [http-nio-9335-exec-1] INFO  c.l.w.c.CheckRecordExportController - [exportAllPages,117] - 检验记录全部页面导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E5%85%A8%E9%83%A8%E9%A1%B5%E9%9D%A2_20250901_171845.docx, 大小: 4977 bytes
17:20:12.692 [http-nio-9335-exec-3] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
17:20:12.693 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
17:20:12.693 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
17:20:12.706 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
17:20:12.706 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
17:20:12.710 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
17:20:12.712 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
17:20:12.713 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
17:20:12.715 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:20:12.715 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
17:20:12.718 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
17:20:12.718 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:20:12.721 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:20:12.722 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:20:12.725 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:20:12.727 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
17:20:12.727 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:20:12.728 [http-nio-9335-exec-3] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:20:12.896 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:20:12.897 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:20:12.898 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:20:12.899 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:20:12.901 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:20:12.902 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:20:12.904 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:20:12.905 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:20:12.906 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:20:12.906 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:20:12.907 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:20:12.907 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:20:12.908 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:20:12.909 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:20:12.935 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3286 bytes
17:20:12.948 [http-nio-9335-exec-3] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250901_172012.docx, 大小: 3286 bytes
17:21:06.919 [http-nio-9335-exec-1] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
17:21:06.919 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
17:21:06.920 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
17:21:06.924 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
17:21:06.924 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
17:21:06.925 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
17:21:06.926 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
17:21:06.927 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
17:21:06.928 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:21:06.928 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
17:21:06.930 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
17:21:06.930 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:21:06.931 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:21:06.931 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:21:06.932 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:21:06.932 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
17:21:06.933 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:21:06.933 [http-nio-9335-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:21:07.005 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:21:07.006 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:21:07.007 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:21:07.007 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:21:07.008 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:21:07.008 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:21:07.008 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:21:07.008 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:21:07.009 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:21:07.009 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:21:07.009 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:21:07.009 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:21:07.009 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:21:07.010 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:21:07.015 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3286 bytes
17:21:07.017 [http-nio-9335-exec-1] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250901_172107.docx, 大小: 3286 bytes
17:26:37.540 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
17:26:37.547 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
17:50:48.405 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 253251 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
17:50:48.408 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
17:50:49.675 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
17:50:49.676 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:50:49.676 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
17:50:49.731 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:50:51.054 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
17:50:51.156 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
17:50:51.564 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
17:50:51.566 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
17:50:51.965 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
17:50:51.992 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
17:50:51.993 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
17:50:52.072 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
17:50:52.078 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
17:50:52.081 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
17:50:52.081 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
17:50:52.081 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
17:50:52.081 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
17:50:52.081 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
17:50:52.082 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
17:50:52.082 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
17:50:52.084 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
17:50:52.137 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
17:50:52.157 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.309 seconds (JVM running for 4.836)
17:50:55.706 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:50:55.967 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
17:50:55.968 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
17:50:55.971 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
17:50:55.972 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
17:52:16.106 [http-nio-9335-exec-5] INFO  c.l.i.i.MagicWebInterceptor - [preHandle,37] - 拦截请求: /magic/web/index.html
17:53:30.507 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
17:53:30.515 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
17:53:34.726 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 254238 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
17:53:34.729 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
17:53:35.913 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
17:53:35.914 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:53:35.914 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
17:53:35.964 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:53:36.030 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
17:53:53.635 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 254484 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
17:53:53.638 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
17:53:54.728 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
17:53:54.729 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:53:54.729 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
17:53:54.779 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:53:54.843 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
17:54:15.671 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 254781 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
17:54:15.674 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
17:54:16.799 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
17:54:16.800 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:54:16.800 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
17:54:16.843 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:54:18.034 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
17:54:18.125 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
17:54:18.487 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
17:54:18.488 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
17:54:18.843 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
17:54:18.873 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
17:54:18.874 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
17:54:18.958 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
17:54:18.964 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
17:54:18.967 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
17:54:18.967 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
17:54:18.967 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
17:54:18.967 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
17:54:18.968 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
17:54:18.968 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
17:54:18.969 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
17:54:18.971 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
17:54:19.028 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
17:54:19.049 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.851 seconds (JVM running for 4.347)
17:54:28.521 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:55:53.101 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
17:55:53.102 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：825(String)
17:55:53.105 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
17:55:53.106 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：825(String), 0(Long), 10(Long)
18:16:52.054 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:16:52.056 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:16:52.058 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:16:52.059 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:22:49.623 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:22:49.624 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:22:49.626 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:22:49.627 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:26:33.315 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:26:33.316 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:26:33.319 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:26:33.321 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:26:50.960 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:26:50.961 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:26:50.963 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:26:50.964 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:27:04.499 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:27:04.500 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:27:04.506 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:27:04.507 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:27:19.629 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:27:19.629 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:27:19.632 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:27:19.632 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:29:05.007 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:29:05.008 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:29:05.010 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:29:05.011 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:35:53.752 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
18:35:53.756 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
18:37:04.198 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 264838 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
18:37:04.201 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
18:37:05.829 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
18:37:05.829 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:37:05.830 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
18:37:05.906 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:37:07.380 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
18:37:07.503 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
18:37:07.879 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
18:37:07.880 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
18:37:08.312 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
18:37:08.359 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
18:37:08.360 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
18:37:08.491 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
18:37:08.499 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
18:37:08.502 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
18:37:08.503 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
18:37:08.504 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
18:37:08.504 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
18:37:08.504 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
18:37:08.504 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
18:37:08.505 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
18:37:08.507 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
18:37:08.584 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
18:37:08.643 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 5.027 seconds (JVM running for 5.654)
18:37:17.527 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:37:18.440 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:37:18.441 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:37:18.445 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:37:18.446 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:40:43.678 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:40:43.679 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:40:43.683 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:40:43.684 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:40:58.429 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:40:58.432 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:40:58.441 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:40:58.443 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:41:14.051 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:41:14.052 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:41:14.060 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:41:14.061 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:41:27.691 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:41:27.693 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:41:27.697 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:41:27.698 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:41:40.471 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:41:40.472 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:41:40.474 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:41:40.474 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:42:40.090 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:42:40.091 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:42:40.093 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:42:40.094 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:45:24.539 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
18:45:24.547 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
18:45:29.849 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 266969 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
18:45:29.853 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
18:45:31.364 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
18:45:31.366 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:45:31.366 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
18:45:31.428 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:45:32.792 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
18:45:32.898 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
18:45:33.342 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
18:45:33.343 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
18:45:33.820 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
18:45:33.854 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
18:45:33.856 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
18:45:33.974 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
18:45:33.978 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
18:45:33.982 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
18:45:33.983 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
18:45:33.983 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
18:45:33.984 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
18:45:33.984 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
18:45:33.984 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
18:45:33.985 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
18:45:33.988 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
18:45:34.066 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
18:45:34.093 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.786 seconds (JVM running for 5.34)
18:45:35.080 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:45:45.873 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:45:45.874 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:45:45.880 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:45:45.881 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:46:41.143 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:46:41.143 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:46:41.146 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:46:41.147 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:51:40.681 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:51:40.682 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:51:40.686 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:51:40.687 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:51:57.103 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:51:57.104 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:51:57.107 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:51:57.108 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:52:15.831 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:52:15.832 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:52:15.835 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:52:15.837 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:52:41.162 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:52:41.164 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:52:41.167 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:52:41.168 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:53:18.669 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:53:18.670 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:53:18.672 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:53:18.673 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:53:32.359 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:53:32.360 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:53:32.363 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:53:32.364 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:53:43.876 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:53:43.877 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:53:43.880 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:53:43.881 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:53:55.880 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:53:55.880 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:53:55.883 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:53:55.884 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
18:57:44.868 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
18:57:44.874 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
18:57:49.569 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 269921 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
18:57:49.572 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
18:57:50.708 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
18:57:50.708 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:57:50.708 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
18:57:50.752 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:57:52.008 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
18:57:52.094 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
18:57:52.440 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
18:57:52.442 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
18:57:52.816 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
18:57:52.849 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
18:57:52.850 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
18:57:52.953 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
18:57:52.958 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
18:57:52.962 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
18:57:52.962 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
18:57:52.962 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
18:57:52.963 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
18:57:52.963 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
18:57:52.963 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
18:57:52.964 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
18:57:52.967 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
18:57:53.031 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
18:57:53.057 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.98 seconds (JVM running for 4.547)
18:57:55.305 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:57:58.770 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
18:57:58.772 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
18:57:58.784 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
18:57:58.785 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:09:55.595 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:09:55.597 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:09:55.601 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:09:55.602 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:10:10.932 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:10:10.933 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:10:10.936 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:10:10.936 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:13:15.439 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:13:15.440 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:13:15.443 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:13:15.444 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:16:15.708 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:16:15.709 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:16:15.711 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:16:15.712 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:16:41.426 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
19:16:41.433 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
19:16:48.547 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 274194 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
19:16:48.551 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
19:16:49.848 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
19:16:49.848 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:16:49.849 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
19:16:49.900 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:16:51.215 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
19:16:51.305 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
19:16:51.665 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
19:16:51.666 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
19:16:52.022 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
19:16:52.054 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
19:16:52.056 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
19:16:52.142 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
19:16:52.149 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
19:16:52.153 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
19:16:52.154 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
19:16:52.155 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
19:16:52.155 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
19:16:52.155 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
19:16:52.156 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
19:16:52.157 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
19:16:52.159 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
19:16:52.226 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
19:16:52.253 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.307 seconds (JVM running for 4.913)
19:16:55.772 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:16:56.707 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:16:56.708 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:16:56.714 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:16:56.714 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:18:41.809 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:18:41.810 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:18:41.815 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:18:41.815 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:18:55.808 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:18:55.809 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:18:55.812 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:18:55.812 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:20:20.404 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:20:20.405 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:20:20.411 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:20:20.412 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:21:34.102 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:21:34.103 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:21:34.106 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:21:34.107 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:22:15.184 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:22:15.185 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:22:15.187 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:22:15.188 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:22:23.691 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:22:23.692 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:22:23.695 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:22:23.696 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:23:26.998 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:23:26.998 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:23:27.001 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:23:27.001 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:24:38.414 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:24:38.415 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:24:38.417 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:24:38.418 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:25:30.659 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:25:30.660 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:25:30.661 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:25:30.662 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:28:26.227 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:28:26.228 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:28:26.231 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:28:26.232 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:30:24.942 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:30:24.944 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:30:24.947 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:30:24.948 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:30:41.000 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:30:41.001 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:30:41.003 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:30:41.004 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:31:01.580 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:31:01.581 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:31:01.583 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:31:01.584 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:31:13.773 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:31:13.774 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:31:13.778 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:31:13.779 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:31:27.681 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:31:27.682 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:31:27.684 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:31:27.685 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:31:40.900 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:31:40.902 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:31:40.906 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:31:40.907 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:31:55.598 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:31:55.599 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:31:55.602 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:31:55.603 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:32:06.310 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:32:06.310 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:32:06.312 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:32:06.313 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:32:44.983 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:32:44.984 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:32:44.988 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:32:44.989 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:34:03.218 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
19:34:03.224 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
19:34:11.128 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 278175 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
19:34:11.133 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
19:34:13.213 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
19:34:13.214 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:34:13.215 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
19:34:13.289 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:34:15.360 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
19:34:15.477 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
19:34:15.870 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
19:34:15.872 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
19:34:16.242 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
19:34:16.275 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
19:34:16.276 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
19:34:16.363 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
19:34:16.369 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
19:34:16.371 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
19:34:16.372 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
19:34:16.372 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
19:34:16.372 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
19:34:16.372 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
19:34:16.372 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
19:34:16.373 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
19:34:16.375 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
19:34:16.427 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
19:34:16.449 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 6.052 seconds (JVM running for 6.66)
19:34:19.481 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:34:22.070 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:34:22.070 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:34:22.074 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:34:22.075 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:37:30.057 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:37:30.058 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:37:30.060 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:37:30.061 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:37:36.850 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:37:36.851 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:37:36.854 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:37:36.854 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
19:40:36.754 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
19:40:36.755 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
19:40:36.757 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
record_id as template_id,
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
19:40:36.757 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
20:20:52.199 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
20:20:52.213 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
