01:13:49.422 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
01:13:49.434 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
01:13:54.794 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 210522 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
01:13:54.796 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
01:13:55.938 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
01:13:55.938 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
01:13:55.938 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
01:13:55.988 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
01:13:57.515 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
01:13:57.716 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
01:13:58.187 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
01:13:58.191 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
01:13:58.580 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
01:13:58.610 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
01:13:58.612 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
01:13:58.717 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
01:13:58.726 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
01:13:58.731 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
01:13:58.732 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
01:13:58.733 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
01:13:58.733 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
01:13:58.733 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
01:13:58.734 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
01:13:58.735 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
01:13:58.737 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
01:13:58.803 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
01:13:58.828 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.521 seconds (JVM running for 5.188)
01:14:03.172 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
01:16:26.736 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
01:16:26.740 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
01:16:30.652 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 211304 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
01:16:30.653 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
01:16:31.617 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
01:16:31.617 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
01:16:31.618 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
01:16:31.655 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
01:16:32.748 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
01:16:32.835 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
01:16:33.159 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
01:16:33.160 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
01:16:33.490 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
01:16:33.514 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
01:16:33.515 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
01:16:33.595 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
01:16:33.601 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
01:16:33.604 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
01:16:33.604 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
01:16:33.605 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
01:16:33.605 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
01:16:33.605 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
01:16:33.605 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
01:16:33.606 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
01:16:33.608 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
01:16:33.657 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
01:16:33.674 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.432 seconds (JVM running for 4.1)
01:16:38.442 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
01:17:41.843 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 39
01:17:41.847 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 39, 检验记录数量: 0
01:17:41.921 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 2 行
01:17:41.923 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
01:17:42.111 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
01:17:42.112 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 4, 总列数: 8
01:17:42.159 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
01:17:42.160 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
01:17:42.212 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
01:17:42.212 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
01:17:42.219 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
01:17:42.224 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
01:17:42.225 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
01:17:42.229 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
01:17:42.230 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
01:17:42.230 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
01:17:42.231 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
01:17:42.232 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
01:17:42.233 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
01:17:42.234 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
01:17:42.235 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
01:17:42.308 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3939 bytes
01:17:42.312 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 39，文档大小: 3939 bytes
01:17:42.314 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756833462313.docx, 大小: 3939 bytes
01:18:43.684 [http-nio-9335-exec-7] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 40
01:18:43.688 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 40, 检验记录数量: 0
01:18:43.688 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
01:18:43.689 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第3部分
01:18:43.691 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
01:18:43.691 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
01:18:43.692 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
01:18:43.692 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
01:18:43.698 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
01:18:43.698 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
01:18:43.699 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
01:18:43.699 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
01:18:43.700 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
01:18:43.701 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
01:18:43.702 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
01:18:43.702 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
01:18:43.703 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
01:18:43.704 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
01:18:43.705 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
01:18:43.706 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
01:18:43.711 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3149 bytes
01:18:43.714 [http-nio-9335-exec-7] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 40，文档大小: 3149 bytes
01:18:43.715 [http-nio-9335-exec-7] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756833523715.docx, 大小: 3149 bytes
01:34:59.839 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
01:34:59.845 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
07:58:20.114 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 216846 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
07:58:20.116 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
07:58:21.503 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
07:58:21.503 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
07:58:21.504 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
07:58:21.564 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
07:58:22.978 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
07:58:23.101 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
07:58:23.565 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
07:58:23.566 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
07:58:23.992 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
07:58:24.027 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
07:58:24.028 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
07:58:24.124 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
07:58:24.132 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
07:58:24.136 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
07:58:24.136 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
07:58:24.137 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
07:58:24.137 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
07:58:24.137 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
07:58:24.137 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
07:58:24.138 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
07:58:24.141 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
07:58:24.191 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
07:58:24.214 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.678 seconds (JVM running for 5.462)
07:58:40.560 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
08:01:20.581 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
08:01:20.588 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
08:01:25.841 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 217917 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
08:01:25.843 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
08:01:27.237 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
08:01:27.237 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
08:01:27.238 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
08:01:27.286 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
08:01:28.554 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
08:01:28.682 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
08:01:29.135 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
08:01:29.136 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
08:01:29.523 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
08:01:29.555 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
08:01:29.557 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
08:01:29.655 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
08:01:29.662 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
08:01:29.666 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
08:01:29.667 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
08:01:29.667 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
08:01:29.667 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
08:01:29.668 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
08:01:29.668 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
08:01:29.669 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
08:01:29.671 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
08:01:29.733 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
08:01:29.758 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.627 seconds (JVM running for 5.486)
08:01:32.660 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
08:06:17.709 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
08:06:17.715 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
08:06:22.110 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 219234 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
08:06:22.111 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
08:06:23.345 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
08:06:23.346 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
08:06:23.346 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
08:06:23.393 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
08:06:24.569 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
08:06:24.672 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
08:06:25.044 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
08:06:25.045 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
08:06:25.436 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
08:06:25.467 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
08:06:25.469 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
08:06:25.560 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
08:06:25.565 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
08:06:25.568 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
08:06:25.569 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
08:06:25.569 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
08:06:25.569 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
08:06:25.569 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
08:06:25.569 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
08:06:25.570 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
08:06:25.572 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
08:06:25.621 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
08:06:25.639 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.048 seconds (JVM running for 4.756)
08:08:29.476 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
08:09:34.607 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 45
08:09:34.611 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 45, 检验记录数量: 0
08:09:34.698 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 2 行
08:09:34.700 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
08:09:34.981 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
08:09:34.982 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 4, 总列数: 8
08:09:35.060 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
08:09:35.060 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
08:09:35.128 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
08:09:35.129 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 430px (6450twips)
08:09:35.140 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 430px (6450twips)
08:09:35.144 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
08:09:35.145 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
08:09:35.149 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
08:09:35.151 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
08:09:35.152 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
08:09:35.152 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
08:09:35.155 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
08:09:35.156 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
08:09:35.158 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
08:09:35.160 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
08:09:35.295 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3781 bytes
08:09:35.302 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 45，文档大小: 3781 bytes
08:09:35.305 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756858175303.docx, 大小: 3781 bytes
08:25:56.657 [http-nio-9335-exec-7] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 45
08:29:29.371 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 45, 检验记录数量: 0
08:30:48.467 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 2 行
08:30:48.468 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
08:30:48.471 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
08:30:48.471 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 4, 总列数: 8
08:30:48.473 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
08:30:48.473 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
08:30:48.482 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
08:30:48.483 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 430px (6450twips)
08:30:48.486 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 430px (6450twips)
08:30:48.489 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
08:30:48.490 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
08:30:48.492 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
08:30:48.493 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
08:30:48.495 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
08:30:48.495 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
08:30:48.495 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
08:30:48.496 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
08:30:48.498 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
08:30:48.499 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
08:30:48.506 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3781 bytes
08:30:48.510 [http-nio-9335-exec-7] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 45，文档大小: 3781 bytes
08:30:48.511 [http-nio-9335-exec-7] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756859448511.docx, 大小: 3781 bytes
08:36:09.300 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
08:36:09.308 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
08:36:15.226 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 226059 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
08:36:15.227 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
08:36:16.781 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
08:36:16.782 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
08:36:16.782 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
08:36:16.838 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
08:36:18.195 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
08:36:18.341 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
08:36:18.862 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
08:36:18.863 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
08:36:19.310 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
08:36:19.347 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
08:36:19.349 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
08:36:19.451 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
08:36:19.458 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
08:36:19.462 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
08:36:19.463 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
08:36:19.463 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
08:36:19.463 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
08:36:19.463 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
08:36:19.463 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
08:36:19.464 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
08:36:19.466 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
08:36:19.532 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
08:36:19.563 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.97 seconds (JVM running for 5.818)
08:36:25.801 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
08:52:45.883 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
08:52:45.891 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
08:52:53.713 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 229661 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
08:52:53.714 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
08:52:54.887 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
08:52:54.888 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
08:52:54.888 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
08:52:54.941 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
08:52:56.236 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
08:52:56.352 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
08:52:57.146 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
08:52:57.147 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
08:52:57.584 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
08:52:57.621 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
08:52:57.622 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
08:52:57.716 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
08:52:57.721 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
08:52:57.725 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
08:52:57.726 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
08:52:57.726 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
08:52:57.726 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
08:52:57.726 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
08:52:57.727 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
08:52:57.727 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
08:52:57.729 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
08:52:57.781 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
08:52:57.808 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.634 seconds (JVM running for 5.422)
08:53:39.198 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
08:58:43.227 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
08:58:43.232 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
08:58:45.558 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 231206 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
08:58:45.559 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
08:58:46.739 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
08:58:46.740 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
08:58:46.740 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
08:58:46.798 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
08:58:48.012 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
08:58:48.140 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
08:58:48.543 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
08:58:48.544 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
08:58:48.943 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
08:58:48.977 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
08:58:48.978 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
08:58:49.087 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
08:58:49.097 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
08:58:49.102 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
08:58:49.103 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
08:58:49.103 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
08:58:49.103 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
08:58:49.104 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
08:58:49.104 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
08:58:49.104 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
08:58:49.106 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
08:58:49.163 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
08:58:49.187 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.169 seconds (JVM running for 4.954)
08:58:54.569 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:02:32.533 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
09:02:32.539 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
09:02:37.439 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 232249 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
09:02:37.440 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
09:02:38.766 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
09:02:38.767 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:02:38.768 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
09:02:38.819 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:02:40.194 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
09:02:40.327 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
09:02:42.927 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
09:02:42.928 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
09:02:43.314 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
09:02:43.345 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
09:02:43.346 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
09:02:43.437 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
09:02:43.443 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
09:02:43.446 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
09:02:43.447 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
09:02:43.447 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
09:02:43.447 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
09:02:43.447 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
09:02:43.447 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
09:02:43.448 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
09:02:43.450 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
09:02:43.503 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
09:02:43.526 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.505 seconds (JVM running for 5.319)
09:03:10.161 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:09:42.769 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
09:09:42.774 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
09:09:47.397 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 233998 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
09:09:47.399 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
09:09:48.646 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
09:09:48.647 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:09:48.647 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
09:09:48.693 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:09:49.964 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
09:09:50.070 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
09:09:50.472 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
09:09:50.473 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
09:09:50.975 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
09:09:51.019 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
09:09:51.020 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
09:09:51.121 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
09:09:51.129 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
09:09:51.133 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
09:09:51.133 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
09:09:51.133 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
09:09:51.134 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
09:09:51.134 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
09:09:51.134 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
09:09:51.135 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
09:09:51.138 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
09:09:51.206 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
09:09:51.234 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.393 seconds (JVM running for 5.154)
09:16:40.613 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
09:16:40.618 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
09:16:45.521 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 235619 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
09:16:45.523 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
09:16:46.764 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
09:16:46.765 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:16:46.766 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
09:16:46.816 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:16:48.117 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
09:16:48.220 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
09:16:48.626 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
09:16:48.627 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
09:16:49.026 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
09:16:49.064 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
09:16:49.066 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
09:16:49.163 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
09:16:49.169 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
09:16:49.173 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
09:16:49.174 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
09:16:49.174 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
09:16:49.174 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
09:16:49.174 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
09:16:49.174 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
09:16:49.175 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
09:16:49.177 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
09:16:49.232 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
09:16:49.255 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.294 seconds (JVM running for 5.056)
09:16:52.278 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:11:29.934 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
10:11:29.941 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
10:11:36.880 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 246565 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
10:11:36.882 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:11:38.431 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
10:11:38.432 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:11:38.432 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:11:38.487 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:11:39.944 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:11:40.104 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:11:40.656 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:11:40.658 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:11:41.096 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:11:41.131 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:11:41.132 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:11:41.243 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:11:41.251 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:11:41.254 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:11:41.255 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:11:41.255 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:11:41.256 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:11:41.256 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:11:41.256 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:11:41.257 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:11:41.259 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:11:41.329 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
10:11:41.358 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 5.135 seconds (JVM running for 6.02)
10:12:08.383 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:15:26.449 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
10:15:26.457 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
10:15:31.111 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 247623 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
10:15:31.113 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:15:32.337 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
10:15:32.338 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:15:32.338 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:15:32.387 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:15:33.682 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:15:33.811 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:15:34.275 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:15:34.276 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:15:34.652 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:15:34.683 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:15:34.684 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:15:34.777 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:15:34.783 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:15:34.787 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:15:34.787 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:15:34.788 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:15:34.788 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:15:34.788 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:15:34.788 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:15:34.789 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:15:34.791 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:15:34.843 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
10:15:34.865 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.303 seconds (JVM running for 5.049)
10:15:37.715 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:19:09.607 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 64
10:19:14.586 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 64, 检验记录数量: 0
10:19:14.696 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 2 行
10:19:14.699 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
10:19:14.971 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
10:19:14.972 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 4, 总列数: 8
10:19:15.051 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
10:19:15.051 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
10:19:15.130 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
10:19:15.130 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 80px (1200twips)
10:19:15.135 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 160px (2400twips)
10:19:15.140 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
10:19:15.141 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
10:19:15.145 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
10:19:15.147 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
10:19:15.148 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
10:19:15.149 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
10:19:15.151 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
10:19:15.151 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
10:19:15.152 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
10:19:15.154 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
10:19:15.257 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3164 bytes
10:19:15.263 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 64，文档大小: 3164 bytes
10:19:15.265 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756865955263.docx, 大小: 3164 bytes
10:29:03.749 [http-nio-9335-exec-8] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 65
10:29:03.755 [http-nio-9335-exec-8] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 65, 检验记录数量: 0
10:29:03.756 [http-nio-9335-exec-8] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 3 行
10:29:03.757 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
10:29:03.760 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
10:29:03.760 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
10:29:03.763 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
10:29:03.763 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
10:29:03.769 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
10:29:03.770 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 80px (1200twips)
10:29:03.771 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 160px (2400twips)
10:29:03.773 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 350px (5250twips)
10:29:03.774 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
10:29:03.775 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
10:29:03.776 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
10:29:03.779 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
10:29:03.780 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
10:29:03.780 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
10:29:03.780 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
10:29:03.781 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
10:29:03.782 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
10:29:03.783 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
10:29:03.795 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3541 bytes
10:29:03.798 [http-nio-9335-exec-8] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 65，文档大小: 3541 bytes
10:29:03.799 [http-nio-9335-exec-8] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756866543799.docx, 大小: 3541 bytes
10:29:07.010 [http-nio-9335-exec-9] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 66
10:29:07.015 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 66, 检验记录数量: 0
10:29:07.016 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
10:29:07.016 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
10:29:07.017 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
10:29:07.017 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
10:29:07.019 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
10:29:07.019 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
10:29:07.025 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
10:29:07.026 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
10:29:07.030 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
10:29:07.031 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
10:29:07.032 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
10:29:07.033 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
10:29:07.034 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
10:29:07.034 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
10:29:07.034 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
10:29:07.035 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
10:29:07.035 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
10:29:07.036 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
10:29:07.044 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3845 bytes
10:29:07.049 [http-nio-9335-exec-9] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 66，文档大小: 3845 bytes
10:29:07.049 [http-nio-9335-exec-9] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756866547049.docx, 大小: 3845 bytes
10:29:11.171 [http-nio-9335-exec-10] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 67
10:29:11.175 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 67, 检验记录数量: 0
10:29:11.175 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
10:29:11.176 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第3部分
10:29:11.177 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
10:29:11.177 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
10:29:11.179 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
10:29:11.179 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
10:29:11.185 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
10:29:11.186 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 400px (6000twips)
10:29:11.187 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
10:29:11.188 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
10:29:11.189 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
10:29:11.190 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
10:29:11.191 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
10:29:11.191 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
10:29:11.191 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
10:29:11.193 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
10:29:11.195 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
10:29:11.196 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
10:29:11.206 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3642 bytes
10:29:11.213 [http-nio-9335-exec-10] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 67，文档大小: 3642 bytes
10:29:11.213 [http-nio-9335-exec-10] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756866551213.docx, 大小: 3642 bytes
10:42:48.033 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
10:42:48.039 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
10:42:52.758 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 253207 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
10:42:52.760 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:42:53.944 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
10:42:53.945 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:42:53.945 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:42:53.993 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:42:55.193 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:42:55.296 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:42:55.680 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:42:55.681 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:42:56.089 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:42:56.125 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:42:56.126 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:42:56.214 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:42:56.221 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:42:56.224 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:42:56.225 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:42:56.225 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:42:56.225 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:42:56.226 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:42:56.226 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:42:56.227 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:42:56.228 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:42:56.285 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
10:42:56.308 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.079 seconds (JVM running for 4.871)
10:42:59.917 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:43:45.569 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 69
10:43:45.573 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 69, 检验记录数量: 0
10:43:45.657 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 2 行
10:43:45.659 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
10:43:45.875 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
10:43:45.875 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 4, 总列数: 8
10:43:45.944 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
10:43:45.944 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
10:43:46.015 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
10:43:46.016 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 80px (1200twips)
10:43:46.019 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 80px (1200twips)
10:43:46.024 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
10:43:46.025 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
10:43:46.029 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
10:43:46.030 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
10:43:46.031 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
10:43:46.031 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
10:43:46.032 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
10:43:46.033 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
10:43:46.033 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
10:43:46.035 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
10:43:46.129 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3162 bytes
10:43:46.134 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 69，文档大小: 3162 bytes
10:43:46.136 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756867426134.docx, 大小: 3162 bytes
10:44:57.944 [http-nio-9335-exec-8] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 70
10:44:57.949 [http-nio-9335-exec-8] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 70, 检验记录数量: 0
10:44:57.950 [http-nio-9335-exec-8] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 3 行
10:44:57.951 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
10:44:57.953 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
10:44:57.954 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
10:44:57.957 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
10:44:57.958 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
10:44:57.964 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
10:44:57.964 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 80px (1200twips)
10:44:57.966 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 80px (1200twips)
10:44:57.967 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 350px (5250twips)
10:44:57.970 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
10:44:57.971 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
10:44:57.972 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
10:44:57.973 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
10:44:57.974 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
10:44:57.974 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
10:44:57.974 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
10:44:57.975 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
10:44:57.975 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
10:44:57.975 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
10:44:57.984 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3540 bytes
10:44:57.989 [http-nio-9335-exec-8] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 70，文档大小: 3540 bytes
10:44:57.990 [http-nio-9335-exec-8] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756867497990.docx, 大小: 3540 bytes
10:46:39.526 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
10:46:39.530 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
10:46:42.350 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 254192 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
10:46:42.353 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:46:43.589 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
10:46:43.590 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:46:43.590 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:46:43.639 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:46:44.954 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:46:45.085 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:46:45.489 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:46:45.490 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:46:45.862 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:46:45.891 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:46:45.892 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:46:45.983 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:46:45.989 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:46:45.993 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:46:45.994 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:46:45.994 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:46:45.994 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:46:45.994 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:46:45.995 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:46:45.996 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:46:45.998 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:46:46.057 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
10:46:46.081 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.521 seconds (JVM running for 5.386)
10:49:15.786 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
10:49:15.791 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
10:49:20.359 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 254966 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
10:49:20.361 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:49:21.644 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
10:49:21.645 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:49:21.645 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:49:21.691 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:49:23.044 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:49:23.186 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:49:23.667 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:49:23.669 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:49:24.060 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:49:24.091 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:49:24.092 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:49:24.189 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:49:24.198 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:49:24.204 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:49:24.205 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:49:24.205 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:49:24.205 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:49:24.205 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:49:24.206 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:49:24.206 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:49:24.208 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:49:24.259 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
10:49:24.282 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.435 seconds (JVM running for 5.155)
10:49:28.295 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:53:33.354 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 73
10:53:33.359 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 73, 检验记录数量: 0
10:53:33.447 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 3 行
10:53:33.449 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
10:53:33.656 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
10:53:33.657 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
10:53:33.710 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
10:53:33.711 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
10:53:33.766 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
10:53:33.767 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 80px (1200twips)
10:53:33.769 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 80px (1200twips)
10:53:33.772 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 350px (5250twips)
10:53:33.776 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
10:53:33.778 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
10:53:33.782 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
10:53:33.782 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
10:53:33.783 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
10:53:33.783 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
10:53:33.784 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
10:53:33.785 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
10:53:33.785 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
10:53:33.787 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
10:53:33.869 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3837 bytes
10:53:33.875 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 73，文档大小: 3837 bytes
10:53:33.879 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756868013876.docx, 大小: 3837 bytes
10:57:40.215 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
10:57:40.219 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
10:57:44.623 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 256950 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
10:57:44.625 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:57:45.782 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
10:57:45.783 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:57:45.783 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:57:45.832 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:57:47.039 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:57:47.145 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:57:47.619 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:57:47.620 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:57:48.001 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:57:48.033 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:57:48.034 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:57:48.123 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:57:48.129 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:57:48.132 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:57:48.133 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:57:48.133 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:57:48.133 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:57:48.133 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:57:48.133 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:57:48.134 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:57:48.136 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:57:48.186 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
10:57:48.203 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.091 seconds (JVM running for 4.796)
10:57:53.658 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:58:18.167 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 76
10:58:18.171 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 76, 检验记录数量: 0
10:58:18.257 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 3 行
10:58:18.259 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
10:58:18.480 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
10:58:18.481 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
10:58:18.536 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
10:58:18.536 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
10:58:18.592 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
10:58:18.593 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 90px (1350twips)
10:58:18.596 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 90px (1350twips)
10:58:18.599 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 330px (4950twips)
10:58:18.602 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
10:58:18.603 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
10:58:18.607 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
10:58:18.609 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
10:58:18.609 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
10:58:18.609 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
10:58:18.610 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
10:58:18.611 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
10:58:18.611 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
10:58:18.613 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
10:58:18.693 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3747 bytes
10:58:18.698 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 76，文档大小: 3747 bytes
10:58:18.700 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756868298698.docx, 大小: 3747 bytes
10:58:54.498 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
10:58:54.502 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
10:58:59.258 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 257483 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
10:58:59.259 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:59:00.507 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
10:59:00.508 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:59:00.509 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:59:00.559 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:59:01.741 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:59:01.852 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:59:02.242 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:59:02.243 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:59:02.625 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:59:02.656 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:59:02.657 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:59:02.745 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:59:02.752 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:59:02.755 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:59:02.756 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:59:02.756 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:59:02.756 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:59:02.756 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:59:02.756 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:59:02.757 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:59:02.759 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:59:02.809 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
10:59:02.827 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.15 seconds (JVM running for 4.967)
10:59:05.527 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:59:17.066 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 80
10:59:17.071 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 80, 检验记录数量: 0
10:59:17.159 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
10:59:17.162 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
10:59:17.377 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
10:59:17.377 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
10:59:17.434 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
10:59:17.434 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
10:59:17.500 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
10:59:17.500 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
10:59:17.508 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
10:59:17.509 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
10:59:17.512 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
10:59:17.513 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
10:59:17.515 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
10:59:17.515 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
10:59:17.516 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
10:59:17.518 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
10:59:17.520 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
10:59:17.522 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
10:59:17.611 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3665 bytes
10:59:17.616 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 80，文档大小: 3665 bytes
10:59:17.620 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756868357617.docx, 大小: 3665 bytes
10:59:35.332 [http-nio-9335-exec-3] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 79
10:59:35.337 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 79, 检验记录数量: 0
10:59:35.338 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 3 行
10:59:35.339 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
10:59:35.340 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
10:59:35.341 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
10:59:35.343 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
10:59:35.343 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
10:59:35.351 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
10:59:35.352 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 100px (1500twips)
10:59:35.353 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 100px (1500twips)
10:59:35.354 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 310px (4650twips)
10:59:35.356 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
10:59:35.357 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
10:59:35.358 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
10:59:35.359 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
10:59:35.360 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
10:59:35.360 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
10:59:35.361 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
10:59:35.361 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
10:59:35.363 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
10:59:35.364 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
10:59:35.373 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3669 bytes
10:59:35.377 [http-nio-9335-exec-3] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 79，文档大小: 3669 bytes
10:59:35.378 [http-nio-9335-exec-3] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756868375378.docx, 大小: 3669 bytes
11:07:36.077 [http-nio-9335-exec-9] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 84
11:07:36.082 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 84, 检验记录数量: 0
11:07:36.083 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
11:07:36.083 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
11:07:36.084 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
11:07:36.085 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
11:07:36.086 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
11:07:36.087 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
11:07:36.094 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
11:07:36.095 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
11:07:36.097 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
11:07:36.098 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:07:36.099 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:07:36.100 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:07:36.101 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:07:36.101 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
11:07:36.101 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
11:07:36.103 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:07:36.104 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:07:36.105 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:07:36.116 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3664 bytes
11:07:36.120 [http-nio-9335-exec-9] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 84，文档大小: 3664 bytes
11:07:36.121 [http-nio-9335-exec-9] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756868856121.docx, 大小: 3664 bytes
11:07:51.335 [http-nio-9335-exec-10] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 83
11:07:51.340 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 83, 检验记录数量: 0
11:07:51.340 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 3 行
11:07:51.341 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
11:07:51.342 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
11:07:51.342 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
11:07:51.344 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
11:07:51.344 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
11:07:51.349 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
11:07:51.350 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 100px (1500twips)
11:07:51.351 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 100px (1500twips)
11:07:51.353 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 310px (4650twips)
11:07:51.355 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
11:07:51.356 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:07:51.357 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:07:51.358 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:07:51.359 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:07:51.359 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
11:07:51.359 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
11:07:51.360 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:07:51.360 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:07:51.361 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:07:51.368 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3683 bytes
11:07:51.373 [http-nio-9335-exec-10] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 83，文档大小: 3683 bytes
11:07:51.373 [http-nio-9335-exec-10] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756868871373.docx, 大小: 3683 bytes
11:12:57.179 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
11:12:57.185 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
11:13:01.934 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 260473 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
11:13:01.936 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
11:13:03.152 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
11:13:03.153 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:13:03.154 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
11:13:03.200 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:13:04.556 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
11:13:04.688 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
11:13:05.067 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
11:13:05.068 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
11:13:05.452 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
11:13:05.481 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
11:13:05.483 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
11:13:05.573 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
11:13:05.580 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
11:13:05.583 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
11:13:05.584 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
11:13:05.584 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
11:13:05.584 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
11:13:05.584 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
11:13:05.585 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
11:13:05.586 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
11:13:05.588 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
11:13:05.647 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
11:13:05.668 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.238 seconds (JVM running for 4.983)
11:13:08.184 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:13:20.805 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 91
11:13:20.809 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 91, 检验记录数量: 0
11:13:20.898 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 3 行
11:13:20.901 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
11:13:21.138 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
11:13:21.138 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 5, 总列数: 8
11:13:21.194 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
11:13:21.195 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
11:13:21.252 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
11:13:21.253 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 100px (1500twips)
11:13:21.257 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 120px (1800twips)
11:13:21.261 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 290px (4350twips)
11:13:21.264 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
11:13:21.265 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:13:21.268 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:13:21.269 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:13:21.269 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:13:21.270 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
11:13:21.271 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
11:13:21.272 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:13:21.273 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:13:21.275 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:13:21.362 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3648 bytes
11:13:21.369 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 91，文档大小: 3648 bytes
11:13:21.372 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756869201370.docx, 大小: 3648 bytes
11:15:01.340 [http-nio-9335-exec-7] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 92
11:15:01.345 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 92, 检验记录数量: 0
11:15:01.345 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
11:15:01.346 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
11:15:01.349 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
11:15:01.349 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
11:15:01.351 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
11:15:01.352 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
11:15:01.359 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
11:15:01.360 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
11:15:01.362 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
11:15:01.363 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:15:01.365 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:15:01.367 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:15:01.369 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:15:01.369 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
11:15:01.369 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
11:15:01.370 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:15:01.371 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:15:01.373 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:15:01.383 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3695 bytes
11:15:01.387 [http-nio-9335-exec-7] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 92，文档大小: 3695 bytes
11:15:01.388 [http-nio-9335-exec-7] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756869301388.docx, 大小: 3695 bytes
11:15:27.649 [http-nio-9335-exec-8] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 93
11:15:27.653 [http-nio-9335-exec-8] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 93, 检验记录数量: 0
11:15:27.654 [http-nio-9335-exec-8] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
11:15:27.654 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第3部分
11:15:27.655 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
11:15:27.655 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
11:15:27.657 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
11:15:27.657 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
11:15:27.664 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
11:15:27.665 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 510px (7650twips)
11:15:27.667 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
11:15:27.667 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:15:27.668 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:15:27.669 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:15:27.669 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:15:27.670 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
11:15:27.670 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
11:15:27.670 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:15:27.671 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:15:27.672 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:15:27.681 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3598 bytes
11:15:27.685 [http-nio-9335-exec-8] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 93，文档大小: 3598 bytes
11:15:27.685 [http-nio-9335-exec-8] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756869327685.docx, 大小: 3598 bytes
11:15:31.044 [http-nio-9335-exec-9] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 94
11:15:31.049 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 94, 检验记录数量: 0
11:15:31.049 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 1 行
11:15:31.049 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
11:15:31.050 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
11:15:31.050 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 3, 总列数: 8
11:15:31.051 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
11:15:31.052 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
11:15:31.056 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
11:15:31.056 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 60px (900twips)
11:15:31.057 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
11:15:31.058 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:15:31.059 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:15:31.060 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:15:31.061 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:15:31.061 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
11:15:31.061 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
11:15:31.062 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:15:31.062 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:15:31.063 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:15:31.067 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 2976 bytes
11:15:31.070 [http-nio-9335-exec-9] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 94，文档大小: 2976 bytes
11:15:31.070 [http-nio-9335-exec-9] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756869331070.docx, 大小: 2976 bytes
12:26:14.249 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
12:26:14.256 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
12:26:24.756 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 275097 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
12:26:24.757 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
12:26:26.469 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
12:26:26.470 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:26:26.470 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
12:26:26.525 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:26:27.920 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
12:26:28.049 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
12:26:28.514 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
12:26:28.516 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
12:26:28.922 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
12:26:28.956 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
12:26:28.957 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
12:26:29.050 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
12:26:29.057 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
12:26:29.060 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
12:26:29.061 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
12:26:29.061 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
12:26:29.061 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
12:26:29.061 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
12:26:29.061 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
12:26:29.062 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
12:26:29.064 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
12:26:29.119 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
12:26:29.144 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 5.06 seconds (JVM running for 5.985)
12:27:37.980 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:27:48.065 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 95
12:27:48.070 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 95, 检验记录数量: 0
12:27:48.172 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 3 行
12:27:48.175 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1268] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
12:27:48.434 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
12:27:48.435 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1358] - 创建新JSON格式表格，总行数: 5, 总列数: 8
12:27:48.505 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1450] - 设置表格总宽度: 880px (13200twips)
12:27:48.506 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1382] - 开始处理表头，表头行数: 2
12:27:48.583 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1394] - 表头处理完成，当前行索引: 2
12:27:48.584 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1530] - 设置数据行高度: 100px (1500twips)
12:27:48.587 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1530] - 设置数据行高度: 120px (1800twips)
12:27:48.590 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1530] - 设置数据行高度: 290px (4350twips)
12:27:48.592 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1957] - 应用JSON格式表头合并单元格，数量: 7
12:27:48.593 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2000] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
12:27:48.598 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2000] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
12:27:48.599 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2000] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
12:27:48.601 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2000] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
12:27:48.601 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
12:27:48.603 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
12:27:48.604 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2000] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
12:27:48.605 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2000] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
12:27:48.607 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2000] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
12:27:48.729 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1289] - 新JSON格式Word文档导出完成，文件大小: 3656 bytes
12:27:48.736 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 95，文档大小: 3656 bytes
12:27:48.738 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756873668736.docx, 大小: 3656 bytes
12:28:30.001 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
12:28:30.008 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
12:28:35.798 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 275807 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
12:28:35.800 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
12:28:37.030 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
12:28:37.031 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:28:37.032 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
12:28:37.085 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:28:38.313 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
12:28:38.423 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
12:28:38.828 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
12:28:38.830 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
12:28:39.243 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
12:28:39.273 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
12:28:39.274 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
12:28:39.367 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
12:28:39.372 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
12:28:39.376 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
12:28:39.376 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
12:28:39.377 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
12:28:39.377 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
12:28:39.377 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
12:28:39.377 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
12:28:39.378 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
12:28:39.379 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
12:28:39.433 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
12:28:39.457 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.247 seconds (JVM running for 5.051)
12:28:45.140 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:28:53.315 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 99
12:28:53.320 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 99, 检验记录数量: 0
12:28:53.412 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 3 行
12:28:53.413 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1270] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
12:28:53.621 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
12:28:53.622 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1360] - 创建新JSON格式表格，总行数: 5, 总列数: 8
12:28:53.677 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1452] - 设置表格总宽度: 880px (13200twips)
12:28:53.677 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1384] - 开始处理表头，表头行数: 2
12:28:53.736 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1396] - 表头处理完成，当前行索引: 2
12:28:53.736 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1532] - 设置数据行高度: 100px (1500twips)
12:28:53.740 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1532] - 设置数据行高度: 120px (1800twips)
12:28:53.742 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1532] - 设置数据行高度: 290px (4350twips)
12:28:53.745 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1959] - 应用JSON格式表头合并单元格，数量: 7
12:28:53.747 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
12:28:53.750 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
12:28:53.752 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
12:28:53.753 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
12:28:53.753 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
12:28:53.754 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
12:28:53.756 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
12:28:53.756 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
12:28:53.758 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
12:28:53.842 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1291] - 新JSON格式Word文档导出完成，文件大小: 3660 bytes
12:28:53.848 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 99，文档大小: 3660 bytes
12:28:53.850 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756873733848.docx, 大小: 3660 bytes
13:39:32.781 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
13:39:32.788 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
14:23:23.852 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 311340 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
14:23:23.854 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:23:25.479 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
14:23:25.479 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:23:25.480 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:23:25.534 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:23:27.014 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:23:27.159 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:23:27.679 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
14:23:27.681 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
14:23:28.166 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
14:23:28.208 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
14:23:28.209 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
14:23:28.308 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
14:23:28.315 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
14:23:28.318 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
14:23:28.319 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
14:23:28.319 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
14:23:28.320 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
14:23:28.320 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
14:23:28.320 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
14:23:28.321 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
14:23:28.323 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
14:23:28.382 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
14:23:28.415 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 5.212 seconds (JVM running for 6.805)
14:23:46.313 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:25:28.171 [http-nio-9335-exec-6] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 103
14:25:28.177 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,360] - 开始使用JSON格式生成Word文档，设计表ID: 103, 检验记录数量: 0
14:25:28.182 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,621] - 构建数据行完成，共 3 行
14:25:28.184 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1270] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
14:25:28.471 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
14:25:28.471 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1360] - 创建新JSON格式表格，总行数: 5, 总列数: 8
14:25:28.550 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1452] - 设置表格总宽度: 880px (13200twips)
14:25:28.551 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1384] - 开始处理表头，表头行数: 2
14:25:28.630 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1396] - 表头处理完成，当前行索引: 2
14:25:28.630 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1532] - 设置数据行高度: 100px (1500twips)
14:25:28.634 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1532] - 设置数据行高度: 120px (1800twips)
14:25:28.638 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1532] - 设置数据行高度: 290px (4350twips)
14:25:28.642 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1959] - 应用JSON格式表头合并单元格，数量: 7
14:25:28.644 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:25:28.648 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:25:28.649 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:25:28.650 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:25:28.650 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
14:25:28.653 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
14:25:28.655 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:25:28.657 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:25:28.659 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:25:28.776 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1291] - 新JSON格式Word文档导出完成，文件大小: 3660 bytes
14:25:28.784 [http-nio-9335-exec-6] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 103，文档大小: 3660 bytes
14:25:28.786 [http-nio-9335-exec-6] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756880728784.docx, 大小: 3660 bytes
15:02:43.998 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:02:44.004 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:02:50.286 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 320359 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:02:50.288 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:02:52.150 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:02:52.151 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:02:52.152 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:02:52.213 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:02:53.594 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:02:53.729 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:02:54.235 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:02:54.236 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:02:54.680 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:02:54.712 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:02:54.714 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:02:54.820 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:02:54.829 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:02:54.832 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:02:54.833 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:02:54.833 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:02:54.833 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:02:54.833 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:02:54.833 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:02:54.834 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:02:54.836 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:02:54.901 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:02:54.932 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 5.324 seconds (JVM running for 6.212)
15:05:33.742 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:05:33.750 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:05:38.914 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 321231 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:05:38.916 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:05:40.207 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:05:40.208 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:05:40.208 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:05:40.257 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:05:41.498 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:05:41.620 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:05:42.167 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:05:42.169 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:05:42.599 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:05:42.633 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:05:42.634 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:05:42.728 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:05:42.734 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:05:42.737 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:05:42.738 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:05:42.738 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:05:42.738 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:05:42.739 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:05:42.739 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:05:42.739 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:05:42.741 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:05:42.797 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:05:42.820 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.483 seconds (JVM running for 5.297)
15:06:45.556 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:12:26.003 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:12:26.008 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:12:30.873 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 322945 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:12:30.875 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:12:32.212 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:12:32.213 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:12:32.213 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:12:32.266 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:12:33.552 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:12:33.660 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:12:34.086 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:12:34.088 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:12:34.532 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:12:34.566 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:12:34.567 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:12:34.680 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:12:34.687 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:12:34.691 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:12:34.692 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:12:34.692 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:12:34.693 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:12:34.693 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:12:34.693 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:12:34.694 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:12:34.697 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:12:34.759 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:12:34.785 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.467 seconds (JVM running for 5.275)
15:12:37.321 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:17:46.586 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:17:46.591 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:17:51.752 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 324342 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:17:51.754 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:17:53.079 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:17:53.080 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:17:53.081 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:17:53.130 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:17:54.394 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:17:54.510 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:17:54.942 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:17:54.943 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:17:55.340 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:17:55.372 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:17:55.373 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:17:55.470 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:17:55.476 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:17:55.479 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:17:55.480 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:17:55.481 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:17:55.481 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:17:55.481 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:17:55.481 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:17:55.482 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:17:55.484 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:17:55.537 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:17:55.561 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.369 seconds (JVM running for 5.227)
15:17:59.167 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:18:50.318 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:18:50.324 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:18:55.286 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 324832 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
15:18:55.288 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:18:56.580 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
15:18:56.581 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:18:56.582 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:18:56.630 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:18:57.915 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:18:58.026 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:18:58.431 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:18:58.433 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:18:58.841 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:18:58.873 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:18:58.874 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:18:58.971 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:18:58.977 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:18:58.980 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:18:58.981 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:18:58.981 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:18:58.982 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:18:58.982 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:18:58.982 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:18:58.983 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:18:58.986 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:18:59.041 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
15:18:59.065 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.328 seconds (JVM running for 5.189)
15:19:02.673 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:03:48.751 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
19:03:48.754 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
19:03:56.314 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 361007 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
19:03:56.315 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
19:03:57.444 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
19:03:57.445 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:03:57.445 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
19:03:57.487 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:03:58.549 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
19:03:58.653 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
19:03:58.999 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
19:03:59.000 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
19:03:59.354 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
19:03:59.383 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
19:03:59.384 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
19:03:59.464 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
19:03:59.469 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
19:03:59.472 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
19:03:59.472 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
19:03:59.472 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
19:03:59.472 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
19:03:59.473 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
19:03:59.473 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
19:03:59.473 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
19:03:59.475 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
19:03:59.518 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
19:03:59.534 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.711 seconds (JVM running for 4.516)
19:12:45.635 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
19:12:45.639 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
19:12:50.748 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 363552 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
19:12:50.749 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
19:12:51.711 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
19:12:51.711 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:12:51.712 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
19:12:51.749 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:12:52.728 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
19:12:52.811 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
19:12:53.139 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
19:12:53.140 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
19:12:53.484 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
19:12:53.512 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
19:12:53.513 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
19:12:53.590 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
19:12:53.595 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
19:12:53.598 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
19:12:53.598 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
19:12:53.599 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
19:12:53.599 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
19:12:53.599 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
19:12:53.599 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
19:12:53.600 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
19:12:53.601 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
19:12:53.646 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
19:12:53.661 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.348 seconds (JVM running for 3.991)
19:13:15.881 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:13:15.917 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 119
19:13:15.929 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,123] - 查询到检验记录数据 4 条
19:13:16.008 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,173] - 解析行模板绑定关系成功，共 3 行绑定数据
19:13:16.008 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,460] - 开始使用JSON格式生成Word文档，设计表ID: 119, 检验记录数量: 4
19:13:16.016 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,710] - 构建数据行完成，共 3 行
19:13:16.019 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1270] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
19:13:16.253 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
19:13:16.254 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1360] - 创建新JSON格式表格，总行数: 5, 总列数: 8
19:13:16.322 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1452] - 设置表格总宽度: 880px (13200twips)
19:13:16.322 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1384] - 开始处理表头，表头行数: 2
19:13:16.389 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1396] - 表头处理完成，当前行索引: 2
19:13:16.389 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1532] - 设置数据行高度: 100px (1500twips)
19:13:16.396 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1532] - 设置数据行高度: 120px (1800twips)
19:13:16.398 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1532] - 设置数据行高度: 290px (4350twips)
19:13:16.401 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1959] - 应用JSON格式表头合并单元格，数量: 7
19:13:16.402 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
19:13:16.407 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
19:13:16.409 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
19:13:16.411 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
19:13:16.411 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
19:13:16.414 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
19:13:16.416 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
19:13:16.417 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
19:13:16.420 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
19:13:16.528 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1291] - 新JSON格式Word文档导出完成，文件大小: 3706 bytes
19:13:16.533 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 119，文档大小: 3706 bytes
19:13:16.535 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756897996533.docx, 大小: 3706 bytes
20:03:32.463 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
20:03:32.466 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
20:03:36.755 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 373787 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
20:03:36.756 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
20:03:37.719 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
20:03:37.720 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:03:37.720 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
20:03:37.758 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:03:38.825 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
20:03:38.939 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
20:03:39.289 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
20:03:39.290 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
20:03:39.630 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
20:03:39.656 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
20:03:39.657 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
20:03:39.734 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
20:03:39.740 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
20:03:39.743 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
20:03:39.744 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
20:03:39.744 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
20:03:39.744 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
20:03:39.744 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
20:03:39.744 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
20:03:39.745 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
20:03:39.747 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
20:03:39.798 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
20:03:39.815 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.474 seconds (JVM running for 4.146)
20:03:51.290 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:03:51.324 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,667] - 开始测试根据设计表ID导出Word文档，设计表ID: 119
20:03:51.335 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,123] - 查询到检验记录数据 4 条
20:03:51.410 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,173] - 解析行模板绑定关系成功，共 3 行绑定数据
20:03:51.410 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,460] - 开始使用JSON格式生成Word文档，设计表ID: 119, 检验记录数量: 4
20:04:00.293 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,710] - 构建数据行完成，共 3 行
20:04:00.295 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1270] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
20:04:00.499 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
20:04:00.499 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1360] - 创建新JSON格式表格，总行数: 5, 总列数: 8
20:04:00.554 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1452] - 设置表格总宽度: 880px (13200twips)
20:04:00.554 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1384] - 开始处理表头，表头行数: 2
20:04:00.611 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1396] - 表头处理完成，当前行索引: 2
20:04:00.612 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1532] - 设置数据行高度: 100px (1500twips)
20:04:00.619 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1532] - 设置数据行高度: 120px (1800twips)
20:04:00.621 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1532] - 设置数据行高度: 290px (4350twips)
20:04:00.623 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1959] - 应用JSON格式表头合并单元格，数量: 7
20:04:00.623 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
20:04:00.627 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
20:04:00.628 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
20:04:00.629 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
20:04:00.629 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
20:04:00.630 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
20:04:00.631 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
20:04:00.632 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
20:04:00.634 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
20:04:00.700 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1291] - 新JSON格式Word文档导出完成，文件大小: 3726 bytes
20:04:00.708 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,672] - Word文档生成成功，设计表ID: 119，文档大小: 3726 bytes
20:04:00.710 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,682] - 复杂表格Word文档导出成功，文件名: test1756901040708.docx, 大小: 3726 bytes
23:47:04.353 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:47:04.360 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:47:10.436 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 418265 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
23:47:10.438 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:47:11.538 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
23:47:11.539 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:47:11.539 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:47:11.580 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:47:12.673 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:47:12.791 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:47:13.190 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
23:47:13.192 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
23:47:13.554 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
23:47:13.583 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
23:47:13.584 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
23:47:13.661 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
23:47:13.667 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
23:47:13.670 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
23:47:13.670 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
23:47:13.670 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
23:47:13.670 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
23:47:13.670 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
23:47:13.671 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
23:47:13.671 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
23:47:13.673 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
23:47:13.718 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
23:47:13.734 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.761 seconds (JVM running for 4.403)
23:49:39.096 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:49:39.121 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,643] - 开始测试根据设计表ID导出Word文档，设计表ID: 119
23:49:39.132 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,125] - 查询到检验记录数据 6 条
23:49:39.209 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,175] - 解析行模板绑定关系成功，共 3 行绑定数据
23:49:39.209 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,462] - 开始使用JSON格式生成Word文档，设计表ID: 119, 检验记录数量: 6
23:49:39.217 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,712] - 构建数据行完成，共 3 行
23:49:39.219 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1270] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
23:49:39.450 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
23:49:39.450 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1360] - 创建新JSON格式表格，总行数: 5, 总列数: 8
23:49:39.503 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1452] - 设置表格总宽度: 880px (13200twips)
23:49:39.504 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1384] - 开始处理表头，表头行数: 2
23:49:39.556 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1396] - 表头处理完成，当前行索引: 2
23:49:39.557 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1532] - 设置数据行高度: 100px (1500twips)
23:49:39.561 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1532] - 设置数据行高度: 120px (1800twips)
23:49:39.564 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1532] - 设置数据行高度: 290px (4350twips)
23:49:39.566 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1959] - 应用JSON格式表头合并单元格，数量: 7
23:49:39.567 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:49:39.572 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:49:39.573 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:49:39.575 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:49:39.575 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
23:49:39.576 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
23:49:39.577 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:49:39.578 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:49:39.579 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:49:39.655 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1291] - 新JSON格式Word文档导出完成，文件大小: 3741 bytes
23:49:39.659 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,648] - Word文档生成成功，设计表ID: 119，文档大小: 3741 bytes
23:49:39.661 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,658] - 复杂表格Word文档导出成功，文件名: test1756914579660.docx, 大小: 3741 bytes
23:52:01.784 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:52:01.789 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:52:03.884 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 419501 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
23:52:03.886 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:52:04.902 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
23:52:04.903 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:52:04.903 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:52:04.941 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:52:05.964 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:52:06.056 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:52:06.417 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
23:52:06.418 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
23:52:06.759 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
23:52:06.789 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
23:52:06.790 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
23:52:06.864 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
23:52:06.869 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
23:52:06.872 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
23:52:06.873 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
23:52:06.873 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
23:52:06.873 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
23:52:06.873 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
23:52:06.873 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
23:52:06.874 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
23:52:06.875 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
23:52:06.923 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
23:52:06.939 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.507 seconds (JVM running for 4.144)
23:52:11.820 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:52:11.850 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,643] - 开始测试根据设计表ID导出Word文档，设计表ID: 119
23:52:11.863 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,125] - 查询到检验记录数据 6 条
23:52:11.933 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,175] - 解析行模板绑定关系成功，共 3 行绑定数据
23:52:11.933 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,462] - 开始使用JSON格式生成Word文档，设计表ID: 119, 检验记录数量: 6
23:52:11.940 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,712] - 构建数据行完成，共 3 行
23:52:11.942 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1270] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
23:52:12.134 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
23:52:12.134 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1360] - 创建新JSON格式表格，总行数: 5, 总列数: 8
23:52:12.183 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1452] - 设置表格总宽度: 880px (13200twips)
23:52:12.183 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1384] - 开始处理表头，表头行数: 2
23:52:12.233 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1396] - 表头处理完成，当前行索引: 2
23:52:12.233 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1532] - 设置数据行高度: 100px (1500twips)
23:52:12.239 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1532] - 设置数据行高度: 120px (1800twips)
23:52:12.241 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1532] - 设置数据行高度: 290px (4350twips)
23:52:12.243 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1959] - 应用JSON格式表头合并单元格，数量: 7
23:52:12.244 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:52:12.247 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:52:12.248 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:52:12.249 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:52:12.249 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
23:52:12.250 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
23:52:12.251 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:52:12.252 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:52:12.253 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2002] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:52:12.321 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1291] - 新JSON格式Word文档导出完成，文件大小: 3778 bytes
23:52:12.326 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,648] - Word文档生成成功，设计表ID: 119，文档大小: 3778 bytes
23:52:12.329 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [testExportByDesignId,658] - 复杂表格Word文档导出成功，文件名: test1756914732327.docx, 大小: 3778 bytes
