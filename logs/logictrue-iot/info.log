16:14:02.123 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 24676 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
16:14:02.129 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
16:14:03.489 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
16:14:03.490 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:14:03.490 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
16:14:03.537 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:14:04.731 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
16:14:36.644 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
16:16:32.268 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 25570 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
16:16:32.270 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
16:16:33.261 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
16:16:33.261 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:16:33.262 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
16:16:33.307 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:16:34.373 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
16:16:34.472 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
16:16:34.869 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
16:16:34.870 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
16:16:35.209 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
16:16:35.238 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
16:16:35.239 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
16:16:35.317 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
16:16:35.322 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
16:16:35.326 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
16:16:35.327 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
16:16:35.327 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
16:16:35.327 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
16:16:35.327 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
16:16:35.327 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
16:16:35.328 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
16:16:35.330 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
16:16:35.375 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
16:16:35.391 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.548 seconds (JVM running for 4.175)
16:19:36.663 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:22:09.658 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,845] - 批量插入剩余数据成功，数量: 7
16:22:46.629 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:22:46.633 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
16:22:46.642 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
16:22:46.760 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
16:22:46.761 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
16:22:46.761 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
16:22:46.775 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
16:22:46.882 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
16:22:46.967 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
16:22:46.967 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 4
16:22:46.980 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
16:22:46.981 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第2部分
16:22:46.982 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:22:46.983 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:22:46.983 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:22:47.061 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:22:47.062 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:22:47.112 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:22:47.113 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 387磅 (7740twips)
16:22:47.116 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
16:22:47.117 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
16:22:47.118 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 3行 x 3列
16:22:47.118 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
16:22:47.120 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 159px (3180twips)
16:22:47.127 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
16:22:47.127 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2069] - 开始处理嵌套表格合并单元格，合并数量: 3
16:22:47.127 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2075] - 应用嵌套表格数据行合并，数量: 3
16:22:47.127 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '123'
16:22:47.131 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
16:22:47.131 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
16:22:47.134 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
16:22:47.134 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
16:22:47.134 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
16:22:47.134 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
16:22:47.135 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
16:22:47.135 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
16:22:47.135 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2081] - 嵌套表格合并单元格处理完成
16:22:47.135 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
16:22:47.138 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:22:47.139 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:22:47.140 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:22:47.141 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:22:47.142 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:22:47.142 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:22:47.143 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:22:47.144 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:22:47.145 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:22:47.146 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:22:47.158 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3166 bytes
16:22:47.184 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
16:22:47.186 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
16:22:47.186 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
16:22:47.186 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
16:22:47.189 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
16:22:47.189 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
16:22:47.190 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
16:22:47.258 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
16:22:47.258 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
16:22:47.261 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
16:22:47.266 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
16:22:47.286 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 10 条
16:22:47.286 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 10
16:22:47.286 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 3 行
16:22:47.286 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第4部分
16:22:47.287 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:22:47.288 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:22:47.288 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 5, 总列数: 8
16:22:47.291 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:22:47.291 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:22:47.302 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:22:47.302 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 15磅 (300twips)
16:22:47.305 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 75磅 (1500twips)
16:22:47.309 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 277磅 (5540twips)
16:22:47.311 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:22:47.312 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:22:47.313 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:22:47.314 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:22:47.315 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:22:47.315 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:22:47.316 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:22:47.317 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:22:47.317 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:22:47.318 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:22:47.325 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 4077 bytes
16:22:47.342 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
16:22:47.343 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
16:22:47.343 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
16:22:47.343 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第5部分
16:22:47.344 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:22:47.344 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:22:47.344 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:22:47.345 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:22:47.345 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:22:47.354 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:22:47.354 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 397磅 (7940twips)
16:22:47.356 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:22:47.357 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:22:47.358 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:22:47.358 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:22:47.359 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:22:47.359 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:22:47.359 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:22:47.360 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:22:47.360 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:22:47.361 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:22:47.366 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3881 bytes
16:22:47.379 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
16:22:47.379 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
16:22:47.379 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
16:22:47.380 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第6部分
16:22:47.380 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:22:47.381 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:22:47.381 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:22:47.382 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:22:47.382 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:22:47.388 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:22:47.388 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 397磅 (7940twips)
16:22:47.390 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:22:47.391 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:22:47.391 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:22:47.391 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:22:47.391 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:22:47.392 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:22:47.392 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:22:47.392 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:22:47.393 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:22:47.393 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:22:47.398 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3805 bytes
16:22:47.413 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
16:22:47.413 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
16:22:47.414 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 4 行
16:22:47.414 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第7部分
16:22:47.415 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:22:47.415 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:22:47.416 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 6, 总列数: 8
16:22:47.416 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:22:47.416 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:22:47.423 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:22:47.423 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 176磅 (3520twips)
16:22:47.424 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 90磅 (1800twips)
16:22:47.426 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 59磅 (1180twips)
16:22:47.427 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 32磅 (640twips)
16:22:47.427 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:22:47.428 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:22:47.428 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:22:47.428 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:22:47.428 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:22:47.429 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:22:47.429 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:22:47.430 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:22:47.430 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:22:47.430 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:22:47.435 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3873 bytes
16:24:18.144 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:24:18.145 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
16:24:18.146 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
16:24:18.147 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
16:24:18.148 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
16:24:18.148 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
16:24:18.151 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
16:24:18.158 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3016 bytes
16:24:18.175 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
16:24:18.175 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 4
16:24:18.176 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
16:24:18.177 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第2部分
16:24:18.178 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:24:18.178 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:24:18.178 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:24:18.179 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:24:18.179 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:24:18.187 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:24:18.188 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 387磅 (7740twips)
16:24:18.188 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
16:24:18.188 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
16:24:18.188 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 3行 x 3列
16:24:18.188 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
16:24:18.189 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 159px (3180twips)
16:24:18.190 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
16:24:18.190 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2069] - 开始处理嵌套表格合并单元格，合并数量: 3
16:24:18.190 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2075] - 应用嵌套表格数据行合并，数量: 3
16:24:18.190 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '123'
16:24:18.190 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
16:24:18.190 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
16:24:18.190 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
16:24:18.191 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
16:24:18.191 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
16:24:18.191 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
16:24:18.191 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
16:24:18.191 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
16:24:18.191 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2081] - 嵌套表格合并单元格处理完成
16:24:18.192 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
16:24:18.193 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:24:18.194 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:24:18.194 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:24:18.194 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:24:18.195 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:24:18.195 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:24:18.195 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:24:18.196 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:24:18.196 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:24:18.197 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:24:18.202 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3165 bytes
16:24:18.210 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
16:24:18.212 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
16:24:18.212 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
16:24:18.212 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
16:24:18.213 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
16:24:18.214 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
16:24:18.214 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
16:24:18.216 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
16:24:18.216 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
16:24:18.218 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
16:24:18.221 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5436 bytes
16:24:18.237 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 10 条
16:24:18.237 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 10
16:24:18.237 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 3 行
16:24:18.238 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第4部分
16:24:18.238 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:24:18.239 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:24:18.239 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 5, 总列数: 8
16:24:18.239 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:24:18.239 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:24:18.244 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:24:18.244 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 15磅 (300twips)
16:24:18.245 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 75磅 (1500twips)
16:24:18.246 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 277磅 (5540twips)
16:24:18.249 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:24:18.249 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:24:18.250 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:24:18.250 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:24:18.251 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:24:18.251 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:24:18.251 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:24:18.251 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:24:18.252 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:24:18.252 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:24:18.256 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 4076 bytes
16:24:18.269 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
16:24:18.269 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
16:24:18.269 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
16:24:18.270 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第5部分
16:24:18.270 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:24:18.270 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:24:18.271 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:24:18.271 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:24:18.271 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:24:18.276 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:24:18.276 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 397磅 (7940twips)
16:24:18.277 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 轮胎检查与保养，包括胎压检测和轮胎换位
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。
这是一种战略性的生活选择。
在当今信息爆炸、节奏飞快的现代社会，高效的时间管理已不再是一种可选的技能，而是每个人必备的核心竞争力。
许多人看似终日忙碌，却被无数琐事缠身，仿佛陷入了一种“生产力陷阱”，即用战术上的勤奋掩盖战略上的懒惰。
16:24:18.277 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 轮胎检查与保养，包括胎压检测和轮胎换位
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。
这是一种战略性的生活选择。
在当今信息爆炸、节奏飞快的现代社会，高效的时间管理已不再是一种可选的技能，而是每个人必备的核心竞争力。
许多人看似终日忙碌，却被无数琐事缠身，仿佛陷入了一种“生产力陷阱”，即用战术上的勤奋掩盖战略上的懒惰。
16:24:18.277 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 8行 x 5列
16:24:18.277 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
16:24:18.278 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 160px (3200twips)
16:24:18.278 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 8, 实际列数: 5
16:24:18.278 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
16:24:18.279 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:24:18.280 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:24:18.280 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:24:18.280 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:24:18.281 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:24:18.281 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:24:18.281 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:24:18.282 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:24:18.282 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:24:18.283 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:24:18.288 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3963 bytes
16:24:18.301 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
16:24:18.302 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
16:24:18.303 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
16:24:18.303 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第6部分
16:24:18.305 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:24:18.305 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:24:18.305 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:24:18.306 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:24:18.306 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:24:18.310 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:24:18.310 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 397磅 (7940twips)
16:24:18.311 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 他们混淆了“忙碌”与“有效率”的本质区别，最终导致身心俱疲且成果寥寥。
真正的效率精髓在于“选择性忽略”，它要求我们建立一套清晰的决策框架，来明智地区分什么是至关重要的事务，什么只是看似紧急的干扰。

诸如“艾森豪威尔矩阵”这样的工具极具价值，它能帮助我们科学地将任务划分为四个象限：重要且紧急、重要但不紧急、紧急但不重要、以及不紧急也不重要。这套系统迫使我们进行战略性思考，将主要精力和时间投入到“重要但不紧急”的事务上，例如学习新技能、规划长期目标或维护健康，这些才是决定未来成败的关键。
同时，我们必须学会果断地拒绝或委托那些次要任务，保护自己有限的注意力。
培养“深度工作”的能力——在无干扰的状态下专注进行认知要求高的活动——能够将我们的智能产出最大化。
归根结底，卓越的时间管理其本质是自我管理，它是一种有意识的生活选择，确保我们每一天的行动都与内心最深处的价值观和长远的人生愿景保持一致。
16:24:18.311 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 他们混淆了“忙碌”与“有效率”的本质区别，最终导致身心俱疲且成果寥寥。
真正的效率精髓在于“选择性忽略”，它要求我们建立一套清晰的决策框架，来明智地区分什么是至关重要的事务，什么只是看似紧急的干扰。

诸如“艾森豪威尔矩阵”这样的工具极具价值，它能帮助我们科学地将任务划分为四个象限：重要且紧急、重要但不紧急、紧急但不重要、以及不紧急也不重要。这套系统迫使我们进行战略性思考，将主要精力和时间投入到“重要但不紧急”的事务上，例如学习新技能、规划长期目标或维护健康，这些才是决定未来成败的关键。
同时，我们必须学会果断地拒绝或委托那些次要任务，保护自己有限的注意力。
培养“深度工作”的能力——在无干扰的状态下专注进行认知要求高的活动——能够将我们的智能产出最大化。
归根结底，卓越的时间管理其本质是自我管理，它是一种有意识的生活选择，确保我们每一天的行动都与内心最深处的价值观和长远的人生愿景保持一致。
16:24:18.311 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 8行 x 5列
16:24:18.311 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
16:24:18.311 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 160px (3200twips)
16:24:18.312 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 8, 实际列数: 5
16:24:18.312 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
16:24:18.313 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:24:18.314 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:24:18.314 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:24:18.315 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:24:18.315 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:24:18.316 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:24:18.316 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:24:18.317 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:24:18.317 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:24:18.317 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:24:18.321 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3882 bytes
16:24:18.337 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
16:24:18.337 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
16:24:18.338 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 4 行
16:24:18.338 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第7部分
16:24:18.339 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:24:18.340 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:24:18.340 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 6, 总列数: 8
16:24:18.341 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:24:18.341 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:24:18.346 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:24:18.346 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 176磅 (3520twips)
16:24:18.348 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 90磅 (1800twips)
16:24:18.349 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 59磅 (1180twips)
16:24:18.349 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 32磅 (640twips)
16:24:18.350 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:24:18.350 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:24:18.351 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:24:18.351 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:24:18.351 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:24:18.351 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:24:18.352 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:24:18.352 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:24:18.352 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:24:18.352 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:24:18.356 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3872 bytes
16:26:56.787 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:26:56.788 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
16:26:56.789 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
16:26:56.791 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
16:26:56.791 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
16:26:56.791 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
16:26:56.795 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
16:26:56.800 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
16:26:56.813 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
16:26:56.814 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 4
16:26:56.814 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
16:26:56.815 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第2部分
16:26:56.815 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:26:56.816 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:26:56.816 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:26:56.817 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:26:56.817 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:26:56.823 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:26:56.823 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 387磅 (7740twips)
16:26:56.824 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
16:26:56.824 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
16:26:56.824 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 3行 x 3列
16:26:56.824 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
16:26:56.825 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 159px (3180twips)
16:26:56.826 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
16:26:56.826 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2069] - 开始处理嵌套表格合并单元格，合并数量: 3
16:26:56.826 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2075] - 应用嵌套表格数据行合并，数量: 3
16:26:56.827 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '123'
16:26:56.827 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
16:26:56.827 [http-nio-9335-exec-6] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
16:26:56.828 [http-nio-9335-exec-6] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
16:26:56.829 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
16:26:56.829 [http-nio-9335-exec-6] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
16:26:56.830 [http-nio-9335-exec-6] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
16:26:56.830 [http-nio-9335-exec-6] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
16:26:56.830 [http-nio-9335-exec-6] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
16:26:56.831 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2081] - 嵌套表格合并单元格处理完成
16:26:56.831 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
16:26:56.832 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:26:56.833 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:26:56.833 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:26:56.833 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:26:56.834 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:26:56.834 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:26:56.834 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:26:56.835 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:26:56.835 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:26:56.835 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:26:56.840 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3166 bytes
16:26:56.855 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
16:26:56.858 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
16:26:56.858 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
16:26:56.858 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
16:26:56.860 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
16:26:56.860 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
16:26:56.861 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
16:26:56.863 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
16:26:56.863 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
16:26:56.865 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
16:26:56.868 [http-nio-9335-exec-6] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
16:26:56.884 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 10 条
16:26:56.884 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 10
16:26:56.884 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 3 行
16:26:56.884 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第4部分
16:26:56.885 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:26:56.885 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:26:56.885 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 5, 总列数: 8
16:26:56.886 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:26:56.886 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:26:56.890 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:26:56.890 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 15磅 (300twips)
16:26:56.891 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 75磅 (1500twips)
16:26:56.893 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 277磅 (5540twips)
16:26:56.895 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:26:56.896 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:26:56.897 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:26:56.897 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:26:56.897 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:26:56.897 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:26:56.898 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:26:56.898 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:26:56.899 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:26:56.899 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:26:56.902 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 4077 bytes
16:26:56.917 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
16:26:56.917 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
16:26:56.918 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
16:26:56.918 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第5部分
16:26:56.919 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:26:56.919 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:26:56.920 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:26:56.920 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:26:56.920 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:26:56.924 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:26:56.925 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 397磅 (7940twips)
16:26:56.926 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:26:56.927 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:26:56.927 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:26:56.927 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:26:56.928 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:26:56.928 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:26:56.928 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:26:56.928 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:26:56.929 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:26:56.929 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:26:56.933 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3881 bytes
16:26:56.946 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
16:26:56.946 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
16:26:56.946 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
16:26:56.947 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第6部分
16:26:56.947 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:26:56.947 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:26:56.948 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:26:56.948 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:26:56.948 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:26:56.952 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:26:56.952 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 397磅 (7940twips)
16:26:56.953 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:26:56.954 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:26:56.954 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:26:56.954 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:26:56.954 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:26:56.954 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:26:56.955 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:26:56.955 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:26:56.955 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:26:56.955 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:26:56.959 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3805 bytes
16:26:56.970 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 12 条
16:26:56.970 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 12
16:26:56.970 [http-nio-9335-exec-6] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 4 行
16:26:56.970 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第7部分
16:26:56.971 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:26:56.971 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:26:56.971 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 6, 总列数: 8
16:26:56.972 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:26:56.972 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:26:56.979 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:26:56.979 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 176磅 (3520twips)
16:26:56.980 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 检查前大灯照明强度和角度，调整至标准范围
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。这是一种战略性的生活选择。

16:26:56.980 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 检查前大灯照明强度和角度，调整至标准范围
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。这是一种战略性的生活选择。

16:26:56.981 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 8行 x 5列
16:26:56.981 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
16:26:56.981 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 160px (3200twips)
16:26:56.982 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 8, 实际列数: 5
16:26:56.983 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
16:26:56.984 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 90磅 (1800twips)
16:26:56.985 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 59磅 (1180twips)
16:26:56.986 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 32磅 (640twips)
16:26:56.987 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:26:56.987 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:26:56.987 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:26:56.987 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:26:56.988 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:26:56.988 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:26:56.988 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:26:56.988 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:26:56.988 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:26:56.989 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:26:56.993 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 4004 bytes
16:27:46.012 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:27:46.013 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
16:27:46.014 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
16:27:46.024 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
16:27:46.024 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
16:27:46.025 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
16:27:46.037 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
16:27:46.045 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
16:27:46.079 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
16:27:46.080 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 4
16:27:46.080 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
16:27:46.081 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第2部分
16:27:46.081 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:27:46.087 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:27:46.087 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:27:46.099 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:27:46.100 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:27:46.117 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:27:46.117 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 387磅 (7740twips)
16:27:46.118 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
16:27:46.118 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
16:27:46.119 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 3行 x 3列
16:27:46.119 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
16:27:46.120 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 159px (3180twips)
16:27:46.121 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
16:27:46.121 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2069] - 开始处理嵌套表格合并单元格，合并数量: 3
16:27:46.121 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2075] - 应用嵌套表格数据行合并，数量: 3
16:27:46.122 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '123'
16:27:46.122 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
16:27:46.122 [http-nio-9335-exec-9] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
16:27:46.122 [http-nio-9335-exec-9] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
16:27:46.122 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
16:27:46.123 [http-nio-9335-exec-9] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
16:27:46.123 [http-nio-9335-exec-9] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
16:27:46.123 [http-nio-9335-exec-9] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
16:27:46.123 [http-nio-9335-exec-9] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
16:27:46.124 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2081] - 嵌套表格合并单元格处理完成
16:27:46.124 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
16:27:46.126 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:27:46.126 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:27:46.127 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:27:46.127 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:27:46.128 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:27:46.128 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:27:46.129 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:27:46.129 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:27:46.129 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:27:46.134 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:27:46.143 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3166 bytes
16:27:46.164 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
16:27:46.166 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
16:27:46.167 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
16:27:46.167 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
16:27:46.169 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
16:27:46.169 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
16:27:46.172 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
16:27:46.178 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
16:27:46.178 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
16:27:46.181 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
16:27:46.187 [http-nio-9335-exec-9] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
16:27:46.216 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 10 条
16:27:46.216 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 10
16:27:46.220 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 3 行
16:27:46.221 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第4部分
16:27:46.222 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:27:46.223 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:27:46.223 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 5, 总列数: 8
16:27:46.224 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:27:46.224 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:27:46.231 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:27:46.231 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 15磅 (300twips)
16:27:46.233 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 75磅 (1500twips)
16:27:46.237 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 277磅 (5540twips)
16:27:46.239 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:27:46.240 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:27:46.240 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:27:46.241 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:27:46.241 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:27:46.241 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:27:46.242 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:27:46.242 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:27:46.243 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:27:46.243 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:27:46.250 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 4077 bytes
16:27:46.277 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
16:27:46.278 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
16:27:46.279 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
16:27:46.280 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第5部分
16:27:46.280 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:27:46.281 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:27:46.281 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:27:46.282 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:27:46.282 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:27:46.287 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:27:46.288 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 397磅 (7940twips)
16:27:46.290 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:27:46.291 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:27:46.291 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:27:46.292 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:27:46.294 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:27:46.295 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:27:46.295 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:27:46.296 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:27:46.297 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:27:46.297 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:27:46.304 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3881 bytes
16:27:46.323 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
16:27:46.323 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
16:27:46.324 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
16:27:46.325 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第6部分
16:27:46.326 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:27:46.327 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:27:46.327 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:27:46.328 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:27:46.328 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:27:46.336 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:27:46.336 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 397磅 (7940twips)
16:27:46.338 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:27:46.338 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:27:46.339 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:27:46.339 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:27:46.340 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:27:46.340 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:27:46.341 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:27:46.342 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:27:46.344 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:27:46.344 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:27:46.350 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3805 bytes
16:27:46.376 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 12 条
16:27:46.377 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 12
16:27:46.379 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 4 行
16:27:46.379 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第7部分
16:27:46.380 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:27:46.381 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:27:46.381 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 6, 总列数: 8
16:27:46.382 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:27:46.382 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:27:46.389 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:27:46.390 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 176磅 (3520twips)
16:27:46.390 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 检查前大灯照明强度和角度，调整至标准范围
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。这是一种战略性的生活选择。

16:27:46.390 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 检查前大灯照明强度和角度，调整至标准范围
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。这是一种战略性的生活选择。

16:27:46.391 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 8行 x 5列
16:27:46.392 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
16:27:46.392 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 160px (3200twips)
16:27:46.394 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 8, 实际列数: 5
16:27:46.394 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
16:27:46.395 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 90磅 (1800twips)
16:27:46.397 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 59磅 (1180twips)
16:27:46.398 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 32磅 (640twips)
16:27:46.399 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:27:46.399 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:27:46.400 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:27:46.401 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:27:46.403 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:27:46.403 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:27:46.404 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:27:46.404 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:27:46.405 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:27:46.406 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:27:46.411 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 4004 bytes
16:30:35.360 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:30:35.361 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
16:30:35.361 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
16:30:35.365 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
16:30:35.365 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
16:30:35.365 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
16:30:35.367 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
16:30:35.371 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3016 bytes
16:30:35.384 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
16:30:35.384 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 4
16:30:35.385 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
16:30:35.385 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第2部分
16:30:35.386 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:30:35.386 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:30:35.386 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:30:35.387 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:30:35.387 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:30:35.393 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:30:35.393 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 387磅 (7740twips)
16:30:35.393 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
16:30:35.393 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
16:30:35.393 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 3行 x 3列
16:30:35.394 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
16:30:35.394 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 159px (3180twips)
16:30:35.395 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
16:30:35.395 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2069] - 开始处理嵌套表格合并单元格，合并数量: 3
16:30:35.395 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2075] - 应用嵌套表格数据行合并，数量: 3
16:30:35.395 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '123'
16:30:35.395 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
16:30:35.395 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
16:30:35.396 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
16:30:35.396 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
16:30:35.396 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
16:30:35.396 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
16:30:35.396 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
16:30:35.397 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
16:30:35.397 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2081] - 嵌套表格合并单元格处理完成
16:30:35.397 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
16:30:35.398 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:30:35.398 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:30:35.399 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:30:35.399 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:30:35.399 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:30:35.399 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:30:35.400 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:30:35.400 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:30:35.400 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:30:35.400 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:30:35.403 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3165 bytes
16:30:35.411 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
16:30:35.413 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
16:30:35.413 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
16:30:35.413 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
16:30:35.414 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
16:30:35.414 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
16:30:35.415 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
16:30:35.417 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
16:30:35.417 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
16:30:35.418 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
16:30:35.421 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5436 bytes
16:30:35.434 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 10 条
16:30:35.434 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 10
16:30:35.434 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 3 行
16:30:35.435 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第4部分
16:30:35.435 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:30:35.435 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:30:35.435 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 5, 总列数: 8
16:30:35.436 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:30:35.436 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:30:35.441 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:30:35.441 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 15磅 (300twips)
16:30:35.442 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 75磅 (1500twips)
16:30:35.443 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 277磅 (5540twips)
16:30:35.445 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:30:35.445 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:30:35.445 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:30:35.446 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:30:35.446 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:30:35.446 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:30:35.447 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:30:35.447 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:30:35.447 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:30:35.447 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:30:35.450 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 4076 bytes
16:30:35.462 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
16:30:35.462 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
16:30:35.463 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
16:30:35.463 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第5部分
16:30:35.463 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:30:35.464 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:30:35.464 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:30:35.464 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:30:35.464 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:30:35.469 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:30:35.469 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 397磅 (7940twips)
16:30:35.470 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:30:35.471 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:30:35.471 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:30:35.471 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:30:35.472 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:30:35.472 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:30:35.472 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:30:35.472 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:30:35.472 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:30:35.473 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:30:35.476 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3880 bytes
16:30:35.488 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
16:30:35.489 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
16:30:35.489 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
16:30:35.489 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第6部分
16:30:35.490 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:30:35.490 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:30:35.490 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:30:35.490 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:30:35.490 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:30:35.495 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:30:35.495 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 397磅 (7940twips)
16:30:35.496 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:30:35.497 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:30:35.497 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:30:35.497 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:30:35.497 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:30:35.497 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:30:35.498 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:30:35.498 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:30:35.498 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:30:35.498 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:30:35.501 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3804 bytes
16:30:35.511 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 12 条
16:30:35.511 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 12
16:30:35.512 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 4 行
16:30:35.513 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第7部分
16:30:35.513 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:30:35.514 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:30:35.514 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 6, 总列数: 8
16:30:35.514 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:30:35.515 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:30:35.519 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:30:35.519 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 176磅 (3520twips)
16:30:35.519 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 检查前大灯照明强度和角度，调整至标准范围
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。这是一种战略性的生活选择。

16:30:35.519 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 检查前大灯照明强度和角度，调整至标准范围
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。这是一种战略性的生活选择。

16:30:35.520 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 8行 x 5列
16:30:35.520 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
16:30:35.520 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 160px (3200twips)
16:30:35.520 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 8, 实际列数: 5
16:30:35.521 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
16:30:35.521 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 90磅 (1800twips)
16:30:35.522 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 59磅 (1180twips)
16:30:35.522 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 32磅 (640twips)
16:30:35.523 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:30:35.523 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:30:35.523 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:30:35.524 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:30:35.524 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:30:35.524 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:30:35.524 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:30:35.524 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:30:35.524 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:30:35.525 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:30:35.530 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 4003 bytes
16:49:01.735 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:49:01.736 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
16:49:01.737 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
16:49:01.739 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
16:49:01.740 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
16:49:01.740 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
16:49:01.746 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
16:49:01.751 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
16:49:01.770 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
16:49:01.770 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 4
16:49:01.772 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
16:49:01.772 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第2部分
16:49:01.773 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:49:01.773 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:49:01.774 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:49:01.774 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:49:01.774 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:49:01.779 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:49:01.779 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 387磅 (7740twips)
16:49:01.780 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
16:49:01.780 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
16:49:01.780 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 3行 x 3列
16:49:01.780 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
16:49:01.780 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 159px (3180twips)
16:49:01.781 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
16:49:01.781 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2069] - 开始处理嵌套表格合并单元格，合并数量: 3
16:49:01.781 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2075] - 应用嵌套表格数据行合并，数量: 3
16:49:01.781 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '123'
16:49:01.781 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
16:49:01.781 [http-nio-9335-exec-3] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
16:49:01.782 [http-nio-9335-exec-3] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
16:49:01.782 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
16:49:01.782 [http-nio-9335-exec-3] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
16:49:01.782 [http-nio-9335-exec-3] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
16:49:01.782 [http-nio-9335-exec-3] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
16:49:01.782 [http-nio-9335-exec-3] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
16:49:01.782 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2081] - 嵌套表格合并单元格处理完成
16:49:01.782 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
16:49:01.783 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:49:01.783 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:49:01.784 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:49:01.784 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:49:01.784 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:49:01.784 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:49:01.784 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:49:01.784 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:49:01.785 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:49:01.785 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:49:01.788 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3166 bytes
16:49:01.799 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
16:49:01.800 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
16:49:01.800 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
16:49:01.800 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
16:49:01.800 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
16:49:01.801 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
16:49:01.801 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
16:49:01.805 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
16:49:01.805 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
16:49:01.807 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
16:49:01.811 [http-nio-9335-exec-3] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
16:49:01.825 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 10 条
16:49:01.825 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 10
16:49:01.826 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 3 行
16:49:01.826 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第4部分
16:49:01.827 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:49:01.827 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:49:01.827 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 5, 总列数: 8
16:49:01.828 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:49:01.828 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:49:01.832 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:49:01.832 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 15磅 (300twips)
16:49:01.833 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 75磅 (1500twips)
16:49:01.834 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 277磅 (5540twips)
16:49:01.835 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:49:01.835 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:49:01.835 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:49:01.836 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:49:01.836 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:49:01.836 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:49:01.836 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:49:01.836 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:49:01.837 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:49:01.837 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:49:01.841 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 4077 bytes
16:49:01.855 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
16:49:01.855 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
16:49:01.856 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
16:49:01.856 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第5部分
16:49:01.857 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:49:01.857 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:49:01.858 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:49:01.858 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:49:01.858 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:49:01.864 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:49:01.865 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 397磅 (7940twips)
16:49:01.866 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:49:01.866 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:49:01.867 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:49:01.867 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:49:01.867 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:49:01.867 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:49:01.868 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:49:01.868 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:49:01.868 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:49:01.868 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:49:01.871 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3881 bytes
16:49:01.883 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
16:49:01.883 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
16:49:01.884 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
16:49:01.884 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第6部分
16:49:01.884 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:49:01.885 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:49:01.885 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
16:49:01.885 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:49:01.885 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:49:01.890 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:49:01.890 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 397磅 (7940twips)
16:49:01.891 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:49:01.891 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:49:01.892 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:49:01.892 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:49:01.892 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:49:01.892 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:49:01.892 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:49:01.893 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:49:01.893 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:49:01.893 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:49:01.896 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3805 bytes
16:49:01.910 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 12 条
16:49:01.910 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 12
16:49:01.911 [http-nio-9335-exec-3] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 4 行
16:49:01.911 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第7部分
16:49:01.912 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:49:01.912 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:49:01.912 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 6, 总列数: 8
16:49:01.913 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:49:01.913 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:49:01.919 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:49:01.919 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 176磅 (3520twips)
16:49:01.919 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 检查前大灯照明强度和角度，调整至标准范围
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。这是一种战略性的生活选择。

16:49:01.920 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 检查前大灯照明强度和角度，调整至标准范围
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。这是一种战略性的生活选择。

16:49:01.920 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 8行 x 5列
16:49:01.921 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
16:49:01.921 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 160px (3200twips)
16:49:01.923 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 8, 实际列数: 5
16:49:01.923 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
16:49:01.924 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 90磅 (1800twips)
16:49:01.925 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 59磅 (1180twips)
16:49:01.926 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 32磅 (640twips)
16:49:01.927 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:49:01.927 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:49:01.927 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:49:01.927 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:49:01.928 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:49:01.928 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:49:01.928 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:49:01.928 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:49:01.929 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:49:01.929 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:49:01.935 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 4004 bytes
16:52:01.482 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:52:01.482 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
16:52:01.486 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 1 条
16:52:01.486 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 1
16:52:01.487 [http-nio-9335-exec-7] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 4 行
16:52:01.487 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题: 
16:52:01.488 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
16:52:01.489 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
16:52:01.489 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 6, 总列数: 8
16:52:01.490 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
16:52:01.490 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
16:52:01.496 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
16:52:01.496 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 176磅 (3520twips)
16:52:01.497 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 检查前大灯照明强度和角度，调整至标准范围
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。这是一种战略性的生活选择。

16:52:01.497 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 检查前大灯照明强度和角度，调整至标准范围
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。这是一种战略性的生活选择。

16:52:01.498 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 8行 x 5列
16:52:01.498 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
16:52:01.499 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 160px (3200twips)
16:52:01.500 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 8, 实际列数: 5
16:52:01.501 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
16:52:01.502 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 90磅 (1800twips)
16:52:01.503 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 59磅 (1180twips)
16:52:01.504 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 32磅 (640twips)
16:52:01.505 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
16:52:01.505 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:52:01.505 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:52:01.506 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:52:01.506 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:52:01.506 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
16:52:01.507 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
16:52:01.507 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:52:01.507 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:52:01.507 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:52:01.516 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 4003 bytes
17:00:00.704 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:00:00.704 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
17:00:00.710 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 1 条
17:00:00.710 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 1
17:00:00.711 [http-nio-9335-exec-9] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 4 行
17:00:00.711 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题: 
17:00:00.712 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:00:00.712 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
17:00:00.713 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 6, 总列数: 8
17:00:00.713 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
17:00:00.714 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
17:00:00.720 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
17:00:00.720 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 176磅 (3520twips)
17:00:00.720 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 检查前大灯照明强度和角度，调整至标准范围
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。这是一种战略性的生活选择。

17:00:00.720 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 检查前大灯照明强度和角度，调整至标准范围
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。这是一种战略性的生活选择。

17:00:00.721 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 8行 x 5列
17:00:00.721 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
17:00:00.721 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 160px (3200twips)
17:00:00.722 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 8, 实际列数: 5
17:00:00.723 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
17:00:00.724 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 90磅 (1800twips)
17:00:00.724 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 59磅 (1180twips)
17:00:00.725 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 32磅 (640twips)
17:00:00.726 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
17:00:00.727 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:00:00.727 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:00:00.727 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:00:00.728 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:00:00.728 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
17:00:00.728 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
17:00:00.729 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:00:00.729 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:00:00.729 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:00:00.735 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 4003 bytes
17:04:14.933 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:04:14.934 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
17:04:14.935 [http-nio-9335-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
17:04:14.941 [http-nio-9335-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
17:04:14.942 [http-nio-9335-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
17:04:14.942 [http-nio-9335-exec-10] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
17:04:15.003 [http-nio-9335-exec-10] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
17:04:15.012 [http-nio-9335-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
17:04:15.043 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
17:04:15.044 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 4
17:04:15.044 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
17:04:15.045 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第2部分
17:04:15.046 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:04:15.046 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
17:04:15.046 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
17:04:15.047 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
17:04:15.047 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
17:04:15.058 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
17:04:15.059 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 387磅 (7740twips)
17:04:15.059 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
17:04:15.060 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
17:04:15.061 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 3行 x 3列
17:04:15.061 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
17:04:15.061 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 159px (3180twips)
17:04:15.063 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
17:04:15.063 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2069] - 开始处理嵌套表格合并单元格，合并数量: 3
17:04:15.063 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2075] - 应用嵌套表格数据行合并，数量: 3
17:04:15.063 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '123'
17:04:15.063 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
17:04:15.064 [http-nio-9335-exec-10] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
17:04:15.064 [http-nio-9335-exec-10] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
17:04:15.064 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
17:04:15.064 [http-nio-9335-exec-10] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
17:04:15.065 [http-nio-9335-exec-10] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
17:04:15.065 [http-nio-9335-exec-10] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
17:04:15.065 [http-nio-9335-exec-10] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
17:04:15.066 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2081] - 嵌套表格合并单元格处理完成
17:04:15.066 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
17:04:15.069 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
17:04:15.070 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:04:15.071 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:04:15.078 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:04:15.079 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:04:15.081 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
17:04:15.082 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
17:04:15.083 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:04:15.084 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:04:15.087 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:04:15.097 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3166 bytes
17:04:15.122 [http-nio-9335-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
17:04:15.126 [http-nio-9335-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
17:04:15.126 [http-nio-9335-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
17:04:15.126 [http-nio-9335-exec-10] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
17:04:15.129 [http-nio-9335-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
17:04:15.129 [http-nio-9335-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
17:04:15.131 [http-nio-9335-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
17:04:15.137 [http-nio-9335-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
17:04:15.137 [http-nio-9335-exec-10] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
17:04:15.141 [http-nio-9335-exec-10] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
17:04:15.152 [http-nio-9335-exec-10] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
17:04:15.198 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 10 条
17:04:15.199 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 10
17:04:15.199 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 3 行
17:04:15.200 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第4部分
17:04:15.201 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:04:15.202 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
17:04:15.203 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 5, 总列数: 8
17:04:15.204 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
17:04:15.204 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
17:04:15.216 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
17:04:15.217 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 15磅 (300twips)
17:04:15.219 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 75磅 (1500twips)
17:04:15.222 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 277磅 (5540twips)
17:04:15.224 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
17:04:15.225 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:04:15.225 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:04:15.226 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:04:15.226 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:04:15.226 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
17:04:15.227 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
17:04:15.228 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:04:15.229 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:04:15.230 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:04:15.239 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 4077 bytes
17:04:15.263 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
17:04:15.263 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
17:04:15.264 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
17:04:15.264 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第5部分
17:04:15.265 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:04:15.266 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
17:04:15.266 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
17:04:15.267 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
17:04:15.267 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
17:04:15.275 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
17:04:15.275 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 397磅 (7940twips)
17:04:15.277 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
17:04:15.278 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:04:15.278 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:04:15.279 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:04:15.279 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:04:15.279 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
17:04:15.280 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
17:04:15.280 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:04:15.280 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:04:15.281 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:04:15.288 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3881 bytes
17:04:15.317 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
17:04:15.318 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
17:04:15.318 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
17:04:15.319 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第6部分
17:04:15.321 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:04:15.321 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
17:04:15.321 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
17:04:15.322 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
17:04:15.323 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
17:04:15.339 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
17:04:15.339 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 397磅 (7940twips)
17:04:15.342 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
17:04:15.342 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:04:15.343 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:04:15.343 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:04:15.343 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:04:15.344 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
17:04:15.344 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
17:04:15.345 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:04:15.345 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:04:15.345 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:04:15.355 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3805 bytes
17:04:15.385 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 12 条
17:04:15.386 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 12
17:04:15.389 [http-nio-9335-exec-10] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 4 行
17:04:15.390 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第7部分
17:04:15.392 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:04:15.393 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
17:04:15.394 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 6, 总列数: 8
17:04:15.395 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
17:04:15.395 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
17:04:15.404 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
17:04:15.404 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 176磅 (3520twips)
17:04:15.405 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 检查前大灯照明强度和角度，调整至标准范围
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。这是一种战略性的生活选择。

17:04:15.405 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 检查前大灯照明强度和角度，调整至标准范围
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。这是一种战略性的生活选择。

17:04:15.406 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 8行 x 5列
17:04:15.406 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
17:04:15.406 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 160px (3200twips)
17:04:15.408 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 8, 实际列数: 5
17:04:15.409 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
17:04:15.411 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 90磅 (1800twips)
17:04:15.413 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 59磅 (1180twips)
17:04:15.415 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 32磅 (640twips)
17:04:15.416 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
17:04:15.417 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:04:15.418 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:04:15.418 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:04:15.419 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:04:15.419 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
17:04:15.419 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
17:04:15.420 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:04:15.420 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:04:15.420 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:04:15.430 [http-nio-9335-exec-10] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 4004 bytes
17:04:15.701 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
17:04:15.706 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
17:04:23.830 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 36099 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
17:04:23.831 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
17:04:25.385 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
17:04:25.386 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:04:25.387 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
17:04:25.449 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:04:27.202 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
17:04:27.335 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
17:04:27.873 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
17:04:27.874 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
17:04:28.374 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
17:04:28.417 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
17:04:28.418 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
17:04:28.539 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
17:04:28.548 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
17:04:28.552 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
17:04:28.553 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
17:04:28.553 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
17:04:28.553 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
17:04:28.554 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
17:04:28.554 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
17:04:28.554 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
17:04:28.556 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
17:04:28.631 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
17:04:28.661 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 5.523 seconds (JVM running for 6.371)
17:04:33.863 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:05:09.486 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:05:09.492 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
17:05:09.504 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
17:05:09.606 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
17:05:09.607 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
17:05:09.607 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
17:05:09.617 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
17:05:09.711 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3016 bytes
17:05:09.786 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
17:05:09.786 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 4
17:05:09.801 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
17:05:09.802 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第2部分
17:05:09.803 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:05:09.804 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
17:05:09.804 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
17:05:09.881 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
17:05:09.882 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
17:05:12.872 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
17:05:12.877 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 387磅 (7740twips)
17:05:12.884 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
17:05:12.885 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
17:05:12.887 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 3行 x 3列
17:05:12.888 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
17:05:12.892 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 159px (3180twips)
17:05:12.904 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
17:05:12.905 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2069] - 开始处理嵌套表格合并单元格，合并数量: 3
17:05:12.906 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2075] - 应用嵌套表格数据行合并，数量: 3
17:05:12.906 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '123'
17:05:12.913 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
17:05:12.914 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
17:05:12.919 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
17:05:12.919 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
17:05:12.920 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
17:05:12.921 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
17:05:12.922 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
17:05:12.923 [http-nio-9335-exec-1] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
17:05:12.924 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2081] - 嵌套表格合并单元格处理完成
17:05:12.924 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
17:05:12.933 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
17:05:12.934 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:05:12.936 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:05:12.943 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:05:12.947 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:05:12.948 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
17:05:12.949 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
17:05:12.951 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:05:12.953 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:05:12.957 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:05:12.982 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3186 bytes
17:05:13.043 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
17:05:13.048 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
17:05:13.049 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
17:05:13.049 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
17:05:13.053 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
17:05:13.056 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
17:05:13.058 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
17:05:13.173 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
17:05:13.174 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
17:05:13.181 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
17:05:13.192 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5436 bytes
17:05:13.230 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 10 条
17:05:13.230 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 10
17:05:13.231 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 3 行
17:05:13.231 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第4部分
17:05:13.232 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:05:13.233 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
17:05:13.233 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 5, 总列数: 8
17:05:13.235 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
17:05:13.235 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
17:05:13.257 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
17:05:13.257 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 15磅 (300twips)
17:05:13.262 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 75磅 (1500twips)
17:05:13.266 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 277磅 (5540twips)
17:05:13.269 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
17:05:13.271 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:05:13.272 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:05:13.274 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:05:13.275 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:05:13.275 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
17:05:13.276 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
17:05:13.277 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:05:13.278 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:05:13.279 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:05:13.286 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 4076 bytes
17:05:13.317 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
17:05:13.317 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
17:05:13.318 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
17:05:13.318 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第5部分
17:05:13.319 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:05:13.320 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
17:05:13.320 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
17:05:13.321 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
17:05:13.321 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
17:05:13.336 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
17:05:13.336 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 397磅 (7940twips)
17:05:13.339 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
17:05:13.340 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:05:13.342 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:05:13.343 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:05:13.344 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:05:13.344 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
17:05:13.344 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
17:05:13.345 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:05:13.345 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:05:13.346 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:05:13.353 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3880 bytes
17:05:13.375 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
17:05:13.376 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
17:05:13.377 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
17:05:13.377 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第6部分
17:05:13.378 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:05:13.379 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
17:05:13.379 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
17:05:13.380 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
17:05:13.380 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
17:05:13.392 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
17:05:13.393 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 397磅 (7940twips)
17:05:13.396 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
17:05:13.397 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:05:13.397 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:05:13.398 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:05:13.399 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:05:13.399 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
17:05:13.399 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
17:05:13.400 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:05:13.400 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:05:13.401 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:05:13.406 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3804 bytes
17:05:13.425 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 12 条
17:05:13.426 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 12
17:05:13.427 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 4 行
17:05:13.428 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第7部分
17:05:13.428 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:05:13.429 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
17:05:13.430 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 6, 总列数: 8
17:05:13.431 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
17:05:13.431 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
17:05:13.440 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
17:05:13.440 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 176磅 (3520twips)
17:05:13.441 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 检查前大灯照明强度和角度，调整至标准范围
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。这是一种战略性的生活选择。

17:05:13.441 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 检查前大灯照明强度和角度，调整至标准范围
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。这是一种战略性的生活选择。

17:05:13.442 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 8行 x 5列
17:05:13.442 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
17:05:13.444 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 160px (3200twips)
17:05:13.446 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 8, 实际列数: 5
17:05:13.446 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
17:05:13.448 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 90磅 (1800twips)
17:05:13.451 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 59磅 (1180twips)
17:05:13.452 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 32磅 (640twips)
17:05:13.454 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
17:05:13.455 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:05:13.455 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:05:13.456 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:05:13.457 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:05:13.457 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
17:05:13.458 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
17:05:13.459 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:05:13.459 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:05:13.460 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:05:13.468 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 4023 bytes
17:10:56.944 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:10:56.945 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
17:10:56.945 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
17:10:56.947 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
17:10:56.947 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
17:10:56.947 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
17:10:56.950 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
17:10:56.959 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3016 bytes
17:10:56.985 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
17:10:56.985 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 4
17:10:56.986 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
17:10:56.987 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第2部分
17:10:56.988 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:10:56.988 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
17:10:56.989 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
17:10:56.989 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
17:10:56.990 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
17:10:56.996 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
17:10:56.996 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 387磅 (7740twips)
17:10:56.997 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.清理
2.清洗
3.喷漆
整体测试
17:10:56.997 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 1.清理
2.清洗
3.喷漆
整体测试
17:10:56.997 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 3行 x 3列
17:10:56.997 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
17:10:56.998 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 159px (3180twips)
17:10:56.999 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 3, 实际列数: 3
17:10:57.000 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2069] - 开始处理嵌套表格合并单元格，合并数量: 3
17:10:57.000 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2075] - 应用嵌套表格数据行合并，数量: 3
17:10:57.000 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '123'
17:10:57.000 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[0-0], 列[1-2], 跨行: 1, 跨列: 2, 内容: '第一行第二三列合并'
17:10:57.000 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行0, 列1-2, 跨度2列
17:10:57.000 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行0, 列1-2
17:10:57.000 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableDataMerge,2097] - 应用嵌套表格数据行合并: 行[1-2], 列[1-2], 跨行: 2, 跨列: 2, 内容: '第二三行第二三列合并'
17:10:57.000 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行1, 列1-2, 跨度2列
17:10:57.001 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行1, 列1-2
17:10:57.001 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,31] - 应用水平合并: 行2, 列1-2, 跨度2列
17:10:57.001 [http-nio-9335-exec-4] INFO  c.l.w.u.MergeUtil - [applyHorizontalMerge,68] - 水平合并完成: 行2, 列1-2
17:10:57.001 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyNestedTableMerges,2081] - 嵌套表格合并单元格处理完成
17:10:57.001 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
17:10:57.003 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
17:10:57.003 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:10:57.004 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:10:57.004 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:10:57.004 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:10:57.004 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
17:10:57.005 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
17:10:57.005 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:10:57.005 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:10:57.006 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:10:57.011 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3186 bytes
17:10:57.021 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
17:10:57.023 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
17:10:57.023 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
17:10:57.023 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
17:10:57.024 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
17:10:57.025 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
17:10:57.025 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
17:10:57.027 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
17:10:57.027 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
17:10:57.029 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
17:10:57.032 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
17:10:57.050 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 10 条
17:10:57.051 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 10
17:10:57.052 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 3 行
17:10:57.052 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第4部分
17:10:57.053 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:10:57.053 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
17:10:57.054 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 5, 总列数: 8
17:10:57.054 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
17:10:57.054 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
17:10:57.060 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
17:10:57.061 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 15磅 (300twips)
17:10:57.062 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 75磅 (1500twips)
17:10:57.064 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 277磅 (5540twips)
17:10:57.065 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
17:10:57.065 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:10:57.065 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:10:57.066 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:10:57.066 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:10:57.067 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
17:10:57.067 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
17:10:57.068 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:10:57.068 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:10:57.068 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:10:57.073 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 4077 bytes
17:10:57.089 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
17:10:57.089 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
17:10:57.090 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
17:10:57.090 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第5部分
17:10:57.091 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:10:57.091 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
17:10:57.091 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
17:10:57.092 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
17:10:57.092 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
17:10:57.097 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
17:10:57.097 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 397磅 (7940twips)
17:10:57.099 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
17:10:57.100 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:10:57.100 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:10:57.101 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:10:57.101 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:10:57.101 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
17:10:57.102 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
17:10:57.102 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:10:57.102 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:10:57.102 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:10:57.106 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3881 bytes
17:10:57.145 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 11 条
17:10:57.145 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 11
17:10:57.145 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 1 行
17:10:57.146 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第6部分
17:10:57.158 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:10:57.159 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
17:10:57.159 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 3, 总列数: 8
17:10:57.160 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
17:10:57.160 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
17:10:57.166 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
17:10:57.166 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 397磅 (7940twips)
17:10:57.169 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
17:10:57.169 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:10:57.169 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:10:57.169 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:10:57.170 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:10:57.170 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
17:10:57.170 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
17:10:57.170 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:10:57.171 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:10:57.171 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:10:57.174 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 3805 bytes
17:10:57.187 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 12 条
17:10:57.187 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: null, 检验记录数量: 12
17:10:57.187 [http-nio-9335-exec-4] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,619] - 构建数据行完成，共 4 行
17:10:57.188 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1351] - 开始导出新JSON格式Word文档，表格标题:  - 第7部分
17:10:57.189 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,778] - 已设置文档为横向纸张
17:10:57.189 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,828] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
17:10:57.190 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1438] - 创建新JSON格式表格，总行数: 6, 总列数: 8
17:10:57.190 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1537] - 设置表格总宽度: 735px (14700twips)
17:10:57.190 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1462] - 开始处理表头，表头行数: 2
17:10:57.195 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1481] - 表头处理完成，当前行索引: 2
17:10:57.195 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 176磅 (3520twips)
17:10:57.196 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1640] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 检查前大灯照明强度和角度，调整至标准范围
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。这是一种战略性的生活选择。

17:10:57.196 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1677] - 开始处理嵌套表格，父单元格内容: 检查前大灯照明强度和角度，调整至标准范围
在当今快节奏的数字时代，有效管理时间已成为个人与职业成功的关键。
许多人陷入忙碌的陷阱，混淆了活动与产出，最终导致精疲力竭而成效甚微。
真正的效率不在于做更多的事，而在于专注于真正重要的事务。
这需要一套清晰的优先级系统，将任务分为紧急且重要、重要但不紧急等不同类别。
通过应用诸如“艾森豪威尔矩阵”等工具，我们可以果断地推迟或委托次要任务，从而为高影响力的目标释放出宝贵的认知空间和精力。
此外，培养深度工作的习惯，即在一段时间内无干扰地专注于一项复杂任务，可以极大地提升产出质量和创新思维。
最终，时间管理的艺术在于有意识地为我们的价值观和长期愿景而生活，而不是被动地对无数外部需求作出反应。这是一种战略性的生活选择。

17:10:57.197 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1761] - 创建嵌套表格: 8行 x 5列
17:10:57.197 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1767] - 开始创建嵌套表格
17:10:57.198 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1786] - 设置嵌套表格总宽度: 160px (3200twips)
17:10:57.200 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1838] - 嵌套表格创建完成，实际行数: 8, 实际列数: 5
17:10:57.201 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1739] - 嵌套表格处理完成
17:10:57.202 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 90磅 (1800twips)
17:10:57.203 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 59磅 (1180twips)
17:10:57.203 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1617] - 设置数据行高度: 32磅 (640twips)
17:10:57.204 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2169] - 应用JSON格式表头合并单元格，数量: 7
17:10:57.204 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:10:57.205 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:10:57.205 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:10:57.205 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:10:57.205 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1036] - 应用水平合并: 行0, 列3-4, 跨度2列
17:10:57.206 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1077] - 水平合并完成: 行0, 列3-4
17:10:57.206 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:10:57.206 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:10:57.206 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2212] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:10:57.211 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1369] - 新JSON格式Word文档导出完成，文件大小: 4024 bytes
17:12:37.052 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
17:12:37.059 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
