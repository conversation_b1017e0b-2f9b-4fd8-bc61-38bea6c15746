00:11:47.661 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 212314 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
00:11:47.663 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:11:48.802 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
00:11:48.803 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:11:48.803 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:11:48.848 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:11:49.950 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:11:50.056 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:11:50.403 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:11:50.404 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:11:50.800 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:11:50.830 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:11:50.831 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:11:50.914 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:11:50.919 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:11:50.922 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:11:50.922 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:11:50.922 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:11:50.922 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:11:50.922 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:11:50.923 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:11:50.923 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:11:50.925 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:11:50.970 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
00:11:51.089 [main] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:11:51.093 [main] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:11:51.096 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9335"]
00:11:51.097 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
00:11:51.100 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9335"]
00:11:51.100 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9335"]
00:12:01.344 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 212620 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
00:12:01.346 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:12:02.411 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
00:12:02.411 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:12:02.412 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:12:02.448 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:12:06.358 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:12:06.449 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:12:06.749 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:12:06.751 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:12:07.041 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:12:07.067 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:12:07.068 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:12:07.137 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:12:07.142 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:12:07.145 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:12:07.145 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:12:07.146 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:12:07.146 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:12:07.146 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:12:07.146 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:12:07.147 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:12:07.148 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:12:07.190 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
00:12:07.206 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.756 seconds (JVM running for 4.467)
00:12:34.505 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:12:34.861 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
00:12:34.866 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
00:12:35.001 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
00:12:35.105 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
00:12:35.105 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
00:12:35.106 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
00:12:35.109 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
00:12:35.110 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
00:12:35.111 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
00:12:35.191 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
00:12:35.191 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
00:12:35.197 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
00:12:35.278 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
00:12:35.371 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 2871 bytes
00:12:35.373 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757261555371.docx, 大小: 2871 bytes
00:14:36.859 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
00:14:36.860 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
00:14:36.861 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
00:14:36.866 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
00:14:36.866 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
00:14:36.867 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
00:14:36.868 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
00:14:36.869 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
00:14:36.870 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
00:14:36.872 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
00:14:36.873 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
00:14:36.879 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
00:14:36.888 [http-nio-9335-exec-4] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
00:14:36.920 [http-nio-9335-exec-4] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 2871 bytes
00:14:36.920 [http-nio-9335-exec-4] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757261676920.docx, 大小: 2871 bytes
00:15:17.259 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
00:15:17.259 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
00:15:17.260 [http-nio-9335-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
00:15:17.263 [http-nio-9335-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
00:15:17.263 [http-nio-9335-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
00:15:17.263 [http-nio-9335-exec-5] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
00:15:17.265 [http-nio-9335-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
00:15:17.266 [http-nio-9335-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
00:15:17.266 [http-nio-9335-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
00:15:17.268 [http-nio-9335-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
00:15:17.269 [http-nio-9335-exec-5] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
00:15:17.272 [http-nio-9335-exec-5] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
00:15:17.276 [http-nio-9335-exec-5] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
00:16:11.612 [http-nio-9335-exec-5] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 2871 bytes
00:16:11.612 [http-nio-9335-exec-5] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757261771612.docx, 大小: 2871 bytes
00:16:33.726 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
00:16:33.727 [http-nio-9335-exec-7] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
00:16:33.728 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
00:16:33.731 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
00:16:33.732 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
00:16:33.732 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
00:16:33.733 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
00:16:33.734 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
00:16:33.735 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
00:16:33.737 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
00:16:33.738 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
00:16:33.741 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
00:16:33.747 [http-nio-9335-exec-7] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5436 bytes
00:17:45.820 [http-nio-9335-exec-7] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 2870 bytes
00:17:45.821 [http-nio-9335-exec-7] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757261865821.docx, 大小: 2870 bytes
00:23:27.481 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:23:27.485 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:23:37.139 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 215601 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
00:23:37.142 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:23:38.716 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
00:23:38.717 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:23:38.717 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:23:38.782 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:23:40.211 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:23:40.352 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:23:40.821 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:23:40.823 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:23:41.276 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:23:41.320 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:23:41.321 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:23:41.438 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:23:41.445 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:23:41.449 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:23:41.450 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:23:41.450 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:23:41.450 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:23:41.450 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:23:41.451 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:23:41.452 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:23:41.455 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:23:41.527 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
00:23:41.554 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.991 seconds (JVM running for 5.836)
00:23:44.153 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:23:44.420 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
00:23:44.427 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
00:23:44.550 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
00:23:44.646 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
00:23:44.647 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
00:23:44.648 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
00:23:44.655 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
00:23:44.657 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
00:23:44.658 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
00:23:44.724 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
00:23:44.724 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
00:23:44.729 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
00:23:44.799 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
00:23:44.887 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 5055 bytes
00:23:44.889 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757262224887.docx, 大小: 5055 bytes
00:26:23.105 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:26:23.110 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:26:28.445 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 216481 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
00:26:28.446 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:26:29.767 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
00:26:29.768 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:26:29.769 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:26:29.827 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:26:31.166 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:26:31.282 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:26:31.714 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:26:31.715 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:26:32.171 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:26:32.207 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:26:32.209 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:26:32.315 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:26:32.320 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:26:32.326 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:26:32.327 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:26:32.327 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:26:32.327 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:26:32.328 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:26:32.328 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:26:32.328 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:26:32.330 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:26:32.392 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
00:26:32.416 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.497 seconds (JVM running for 5.391)
00:26:35.652 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:26:35.933 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
00:26:35.936 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
00:26:36.062 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
00:26:36.168 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
00:26:36.169 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
00:26:36.170 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
00:26:36.177 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
00:26:36.180 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
00:26:36.181 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
00:26:36.248 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
00:26:36.248 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
00:26:36.252 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
00:26:36.322 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5436 bytes
00:26:36.429 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 5064 bytes
00:26:36.433 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757262396430.docx, 大小: 5064 bytes
00:29:23.484 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:29:23.489 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:29:28.391 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 217361 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
00:29:28.392 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:29:29.522 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
00:29:29.522 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:29:29.523 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:29:29.566 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:29:30.858 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:29:30.956 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:29:31.344 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:29:31.345 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:29:31.729 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:29:31.761 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:29:31.762 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:29:31.862 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:29:31.867 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:29:31.871 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:29:31.872 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:29:31.872 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:29:31.873 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:29:31.873 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:29:31.873 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:29:31.874 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:29:31.875 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:29:31.925 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
00:29:31.944 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.011 seconds (JVM running for 4.684)
00:29:34.234 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:29:34.493 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
00:29:34.497 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
00:29:34.611 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
00:29:34.697 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
00:29:34.698 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
00:29:34.699 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
00:29:34.703 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
00:29:34.704 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
00:29:34.705 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
00:29:34.776 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
00:29:34.777 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
00:29:34.782 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
00:29:34.847 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
00:29:34.913 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 5027 bytes
00:29:34.916 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757262574914.docx, 大小: 5027 bytes
00:31:25.424 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:31:25.429 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:31:30.272 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 218021 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
00:31:30.273 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:31:31.446 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
00:31:31.448 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:31:31.448 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:31:31.504 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:31:32.745 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:31:32.854 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:31:33.225 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:31:33.226 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:31:33.648 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:31:33.685 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:31:33.686 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:31:33.774 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:31:33.780 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:31:33.783 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:31:33.784 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:31:33.785 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:31:33.785 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:31:33.785 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:31:33.785 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:31:33.786 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:31:33.788 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:31:33.841 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
00:31:33.872 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.072 seconds (JVM running for 4.831)
00:31:36.413 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:31:36.672 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
00:31:36.678 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
00:31:36.801 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
00:31:36.891 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
00:31:36.892 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
00:31:36.893 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
00:31:36.896 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
00:31:36.899 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
00:31:36.901 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
00:31:36.966 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
00:31:36.966 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
00:31:36.971 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
00:31:37.035 [http-nio-9335-exec-1] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5436 bytes
00:31:37.103 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 5026 bytes
00:31:37.105 [http-nio-9335-exec-1] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757262697103.docx, 大小: 5026 bytes
00:52:45.878 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 225686 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
00:52:45.880 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:52:46.852 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
00:52:46.852 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:52:46.853 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:52:46.892 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:52:47.924 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:52:48.008 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:52:48.337 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:52:48.339 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:52:48.689 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:52:48.719 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:52:48.721 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:52:48.813 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:52:48.817 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:52:48.820 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:52:48.820 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:52:48.820 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:52:48.820 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:52:48.820 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:52:48.821 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:52:48.821 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:52:48.823 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:52:48.864 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
00:52:48.879 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.406 seconds (JVM running for 4.493)
00:52:51.434 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
01:03:04.124 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,564] - 批量插入剩余数据成功，数量: 5
01:03:04.297 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
01:03:04.301 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
01:03:08.792 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 227988 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
01:03:08.793 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
01:03:09.700 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
01:03:09.701 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
01:03:09.701 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
01:03:09.740 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
01:03:10.726 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
01:03:10.817 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
01:03:13.805 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
01:03:13.806 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
01:03:14.105 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
01:03:14.128 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
01:03:14.130 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
01:03:14.198 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
01:03:14.202 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
01:03:14.205 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
01:03:14.205 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
01:03:14.206 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
01:03:14.206 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
01:03:14.206 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
01:03:14.206 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
01:03:14.207 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
01:03:14.208 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
01:03:14.250 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
01:03:14.265 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.2 seconds (JVM running for 3.824)
01:03:37.452 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
01:03:42.925 [http-nio-9335-exec-1] INFO  c.l.w.s.i.WorkCheckServiceImpl - [batchSaveWorkCheckTableList,571] - 批量插入剩余数据成功，数量: 5
01:04:00.050 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
01:04:00.053 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 50pt, 下: 50pt, 左: 50pt, 右: 50pt
01:04:00.062 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
01:04:00.140 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
01:04:00.141 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 0
01:04:00.141 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
01:04:00.149 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
01:04:00.204 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 3017 bytes
01:04:00.254 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 4 条
01:04:00.255 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
01:04:00.255 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 2, 检验记录数量: 4
01:04:00.257 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
01:04:00.258 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第2部分
01:04:00.259 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
01:04:00.259 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
01:04:00.260 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
01:04:00.293 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
01:04:00.293 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
01:04:00.329 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
01:04:00.329 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 410磅 (8200twips)
01:04:00.333 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
01:04:00.333 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
01:04:00.336 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
01:04:00.337 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
01:04:00.338 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
01:04:00.338 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
01:04:00.340 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
01:04:00.342 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
01:04:00.344 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
01:04:00.346 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
01:04:00.355 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 2987 bytes
01:04:00.377 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,37] - 开始导出简单Word文档，标题: 简单Word文档
01:04:00.379 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,51] - 使用JSON结构化数据导出，节点数量: 8
01:04:00.379 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,57] - 文档包含图片数量: 1
01:04:00.379 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,87] - 开始解析JSON结构化内容，节点数量: 8
01:04:00.381 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,750] - 开始处理图片 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
01:04:00.382 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,892] - 图片解码成功，大小: 2003 bytes (2.0KB)
01:04:00.383 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,903] - 图片尺寸计算完成 - 宽度: 52px, 高度: 42px, 来源: CSS样式(宽高)
01:04:00.419 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processBase64Image,909] - 图片插入成功 - 格式: PNG, 尺寸: 52x42
01:04:00.420 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [processImageNode,755] - 图片处理完成 - 大小: 2.0KB, 显示尺寸: 52pxx42px, 原始尺寸: 52x42, 宽高比: 1.2381, 格式: PNG
01:04:00.422 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [parseAndAddJsonContent,93] - JSON结构化内容解析完成
01:04:00.427 [http-nio-9335-exec-2] INFO  c.l.w.s.SimpleWordExportService - [exportSimpleWord,74] - 简单Word文档导出成功，大小: 5437 bytes
01:04:00.441 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 5 条
01:04:00.442 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 3 行绑定数据
01:04:00.442 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 4, 检验记录数量: 5
01:04:00.442 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 3 行
01:04:00.442 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第4部分
01:04:00.443 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
01:04:00.443 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
01:04:00.443 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 5, 总列数: 8
01:04:00.445 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
01:04:00.445 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
01:04:00.454 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
01:04:00.455 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 14磅 (280twips)
01:04:00.456 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 87磅 (1740twips)
01:04:00.458 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 308磅 (6160twips)
01:04:00.459 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
01:04:00.459 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
01:04:00.460 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
01:04:00.461 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
01:04:00.461 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
01:04:00.462 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
01:04:00.462 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
01:04:00.463 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
01:04:00.463 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
01:04:00.464 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
01:04:00.469 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3985 bytes
01:04:00.482 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [queryDataRecords,119] - 查询到检验记录数据 0 条
01:04:00.482 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [parseRowTemplateBindings,169] - 解析行模板绑定关系成功，共 1 行绑定数据
01:04:00.482 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [generateWordWithJsonFormat,367] - 开始使用JSON格式生成Word文档，设计表ID: 5, 检验记录数量: 0
01:04:00.483 [http-nio-9335-exec-2] INFO  c.l.w.s.i.WordExportServiceImpl - [buildCellRows,617] - 构建数据行完成，共 1 行
01:04:00.483 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: 检验记录表 - 第5部分
01:04:00.484 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
01:04:00.484 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
01:04:00.484 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 3, 总列数: 8
01:04:00.485 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
01:04:00.485 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
01:04:00.491 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
01:04:00.491 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 391磅 (7820twips)
01:04:00.492 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
01:04:00.492 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
01:04:00.493 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
01:04:00.493 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
01:04:00.494 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
01:04:00.494 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
01:04:00.494 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
01:04:00.495 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
01:04:00.496 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
01:04:00.496 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
01:04:00.500 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 4351 bytes
01:04:00.514 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportAll,165] - Word文档生成成功，文档大小: 7795 bytes
01:04:00.516 [http-nio-9335-exec-2] INFO  c.l.w.c.WorkCheckController - [exportAll,175] - 复杂表格Word文档导出成功，文件名: all_1757264640514.docx, 大小: 7795 bytes
01:07:23.406 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
01:07:23.411 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
18:57:37.324 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 372455 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
18:57:37.326 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
18:57:38.545 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
18:57:38.545 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:57:38.546 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
18:57:38.591 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:57:39.763 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
18:57:39.870 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
18:57:40.217 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
18:57:40.218 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
18:57:40.597 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
18:57:40.628 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
18:57:40.629 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
18:57:40.708 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
18:57:40.714 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
18:57:40.717 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
18:57:40.717 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
18:57:40.718 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
18:57:40.718 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
18:57:40.718 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
18:57:40.718 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
18:57:40.718 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
18:57:40.720 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
18:57:40.766 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
18:57:40.782 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.13 seconds (JVM running for 5.451)
18:59:12.060 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:02:17.327 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,84] - 接收到新JSON格式的表格导出请求，标题: null
19:02:17.328 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,86] - 表头合并数量: 7, 数据合并数量: 0
19:02:17.328 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 检测到嵌套表格，使用增强导出模式
19:02:17.329 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: null
19:02:17.578 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
19:02:17.582 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
19:02:17.583 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 4, 总列数: 8
19:02:17.639 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 960px (19200twips)
19:02:17.639 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
19:02:17.707 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
19:02:17.708 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 208磅 (4160twips)
19:02:17.709 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1621] - 检测到嵌套表格，行: 1, 列: 1, 主内容: 1.表面无划痕
2.颜色均匀
19:02:17.709 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1656] - 开始处理嵌套表格，父单元格内容: 1.表面无划痕
2.颜色均匀
19:02:17.709 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1736] - 创建嵌套表格: 3行 x 3列
19:02:17.709 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1742] - 开始创建嵌套表格
19:02:17.749 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1714] - 嵌套表格处理完成
19:02:17.749 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1621] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
19:02:17.750 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1656] - 开始处理嵌套表格，父单元格内容: 详细检查结果
19:02:17.750 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1736] - 创建嵌套表格: 3行 x 3列
19:02:17.750 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1742] - 开始创建嵌套表格
19:02:17.753 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1714] - 嵌套表格处理完成
19:02:17.755 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 208磅 (4160twips)
19:02:17.755 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1621] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
19:02:17.755 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1656] - 开始处理嵌套表格，父单元格内容: 测试数据
19:02:17.756 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1736] - 创建嵌套表格: 3行 x 3列
19:02:17.756 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1742] - 开始创建嵌套表格
19:02:17.759 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1714] - 嵌套表格处理完成
19:02:17.761 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
19:02:17.762 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
19:02:17.765 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
19:02:17.766 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
19:02:17.766 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
19:02:17.766 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
19:02:17.768 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
19:02:17.769 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
19:02:17.769 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
19:02:17.770 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
19:02:17.850 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3332 bytes
19:02:17.856 [http-nio-9335-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,109] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250908_190217.docx, 大小: 3332 bytes
22:58:10.038 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 421535 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
22:58:10.040 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
22:58:11.438 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
22:58:11.439 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:58:11.440 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
22:58:11.497 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:58:12.736 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
22:58:12.853 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
22:58:13.194 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
22:58:13.196 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
22:58:13.606 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
22:58:13.645 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
22:58:13.646 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
22:58:13.759 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
22:58:13.765 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
22:58:13.768 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
22:58:13.769 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
22:58:13.769 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
22:58:13.769 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
22:58:13.769 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
22:58:13.769 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
22:58:13.770 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
22:58:13.772 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
22:58:13.820 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
22:58:13.841 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.312 seconds (JVM running for 5.982)
23:26:12.054 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:26:12.103 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,84] - 接收到新JSON格式的表格导出请求，标题: null
23:26:12.103 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,86] - 表头合并数量: 7, 数据合并数量: 0
23:26:12.103 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 检测到嵌套表格，使用增强导出模式
23:26:12.103 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: null
23:26:12.322 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
23:26:12.327 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
23:26:12.327 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 4, 总列数: 8
23:26:12.383 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
23:26:12.383 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
23:26:12.460 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
23:26:12.460 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 208磅 (4160twips)
23:26:12.461 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1621] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
23:26:12.462 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1656] - 开始处理嵌套表格，父单元格内容: 详细检查结果
23:26:12.462 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1736] - 创建嵌套表格: 3行 x 3列
23:26:12.462 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1742] - 开始创建嵌套表格
23:26:12.500 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1714] - 嵌套表格处理完成
23:26:12.502 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 208磅 (4160twips)
23:26:12.503 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1621] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
23:26:12.503 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1656] - 开始处理嵌套表格，父单元格内容: 测试数据
23:26:12.503 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1736] - 创建嵌套表格: 3行 x 3列
23:26:12.504 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1742] - 开始创建嵌套表格
23:26:12.508 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1714] - 嵌套表格处理完成
23:26:12.510 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
23:26:12.510 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:26:12.514 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:26:12.514 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:26:12.515 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:26:12.515 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
23:26:12.517 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
23:26:12.518 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:26:12.518 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:26:12.519 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:26:12.619 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3259 bytes
23:26:12.627 [http-nio-9335-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,109] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250908_232612.docx, 大小: 3259 bytes
23:40:57.971 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,84] - 接收到新JSON格式的表格导出请求，标题: null
23:40:57.971 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,86] - 表头合并数量: 7, 数据合并数量: 0
23:40:57.971 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 检测到嵌套表格，使用增强导出模式
23:40:57.972 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: null
23:40:57.974 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
23:40:57.974 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
23:40:57.975 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 4, 总列数: 8
23:40:57.976 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
23:40:57.977 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
23:40:57.988 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
23:40:57.988 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 208磅 (4160twips)
23:40:57.989 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1621] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
23:40:57.990 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1656] - 开始处理嵌套表格，父单元格内容: 详细检查结果
23:40:57.990 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1736] - 创建嵌套表格: 3行 x 3列
23:40:57.990 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1742] - 开始创建嵌套表格
23:40:57.992 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1714] - 嵌套表格处理完成
23:40:57.994 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 208磅 (4160twips)
23:40:57.994 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1621] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
23:40:57.995 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1656] - 开始处理嵌套表格，父单元格内容: 测试数据
23:40:57.995 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1736] - 创建嵌套表格: 3行 x 3列
23:40:57.995 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createNestedTable,1742] - 开始创建嵌套表格
23:40:57.996 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1714] - 嵌套表格处理完成
23:40:57.997 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
23:40:57.998 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:40:57.999 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:40:58.000 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:40:58.000 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:40:58.001 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
23:40:58.001 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
23:40:58.001 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:40:58.002 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:40:58.003 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:40:58.009 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3253 bytes
23:40:58.013 [http-nio-9335-exec-4] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,109] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250908_234058.docx, 大小: 3253 bytes
23:56:04.158 [http-nio-9335-exec-8] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,84] - 接收到新JSON格式的表格导出请求，标题: null
23:56:04.158 [http-nio-9335-exec-8] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,86] - 表头合并数量: 7, 数据合并数量: 0
23:56:04.159 [http-nio-9335-exec-8] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 检测到嵌套表格，使用增强导出模式
23:56:04.159 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1347] - 开始导出新JSON格式Word文档，表格标题: null
23:56:04.160 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
23:56:04.160 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [setPageMargins,822] - 页边距设置完成 - 上: 72pt, 下: 72pt, 左: 72pt, 右: 72pt
23:56:04.161 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1434] - 创建新JSON格式表格，总行数: 4, 总列数: 8
23:56:04.162 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1526] - 设置表格总宽度: 715px (14300twips)
23:56:04.162 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1458] - 开始处理表头，表头行数: 2
23:56:04.174 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1470] - 表头处理完成，当前行索引: 2
23:56:04.174 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 208磅 (4160twips)
23:56:04.175 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1621] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
23:56:04.175 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1656] - 开始处理嵌套表格，父单元格内容: 详细检查结果
23:56:04.176 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [createNestedTable,1736] - 创建嵌套表格: 3行 x 3列
23:56:04.176 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [createNestedTable,1742] - 开始创建嵌套表格
23:56:04.178 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1714] - 嵌套表格处理完成
23:56:04.180 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1606] - 设置数据行高度: 208磅 (4160twips)
23:56:04.181 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1621] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
23:56:04.181 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1656] - 开始处理嵌套表格，父单元格内容: 测试数据
23:56:04.181 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [createNestedTable,1736] - 创建嵌套表格: 3行 x 3列
23:56:04.181 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [createNestedTable,1742] - 开始创建嵌套表格
23:56:04.183 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1714] - 嵌套表格处理完成
23:56:04.184 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2033] - 应用JSON格式表头合并单元格，数量: 7
23:56:04.185 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
23:56:04.186 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
23:56:04.186 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
23:56:04.187 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
23:56:04.187 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1030] - 应用水平合并: 行0, 列3-4, 跨度2列
23:56:04.188 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1071] - 水平合并完成: 行0, 列3-4
23:56:04.189 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
23:56:04.190 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
23:56:04.191 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2076] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
23:56:04.198 [http-nio-9335-exec-8] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1365] - 新JSON格式Word文档导出完成，文件大小: 3253 bytes
23:56:04.202 [http-nio-9335-exec-8] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,109] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250908_235604.docx, 大小: 3253 bytes
