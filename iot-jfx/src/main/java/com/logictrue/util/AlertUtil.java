package com.logictrue.util;

import javafx.scene.control.Alert;
import javafx.scene.control.ButtonType;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.stage.Window;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;

/**
 * Alert对话框工具类
 * 提供统一的对话框创建和显示方法
 */
public class AlertUtil {
    private static final Logger logger = LoggerFactory.getLogger(AlertUtil.class);

    /**
     * 显示错误对话框
     *
     * @param title   标题
     * @param message 消息内容
     */
    public static void showError(String title, String message) {
        showAlert(Alert.AlertType.ERROR, title, message, null);
    }

    /**
     * 显示错误对话框（带所有者窗口）
     *
     * @param title   标题
     * @param message 消息内容
     * @param owner   所有者窗口
     */
    public static void showError(String title, String message, Window owner) {
        showAlert(Alert.AlertType.ERROR, title, message, owner);
    }

    /**
     * 显示信息对话框
     *
     * @param title   标题
     * @param message 消息内容
     */
    public static void showInfo(String title, String message) {
        showAlert(Alert.AlertType.INFORMATION, title, message, null);
    }

    /**
     * 显示信息对话框（带所有者窗口）
     *
     * @param title   标题
     * @param message 消息内容
     * @param owner   所有者窗口
     */
    public static void showInfo(String title, String message, Window owner) {
        showAlert(Alert.AlertType.INFORMATION, title, message, owner);
    }

    /**
     * 显示警告对话框
     *
     * @param title   标题
     * @param message 消息内容
     */
    public static void showWarning(String title, String message) {
        showAlert(Alert.AlertType.WARNING, title, message, null);
    }

    /**
     * 显示警告对话框（带所有者窗口）
     *
     * @param title   标题
     * @param message 消息内容
     * @param owner   所有者窗口
     */
    public static void showWarning(String title, String message, Window owner) {
        showAlert(Alert.AlertType.WARNING, title, message, owner);
    }

    /**
     * 显示确认对话框
     *
     * @param title   标题
     * @param message 消息内容
     * @return 用户是否点击了确定按钮
     */
    public static boolean showConfirmation(String title, String message) {
        return showConfirmation(title, message, null);
    }

    /**
     * 显示确认对话框（带所有者窗口）
     *
     * @param title   标题
     * @param message 消息内容
     * @param owner   所有者窗口
     * @return 用户是否点击了确定按钮
     */
    public static boolean showConfirmation(String title, String message, Window owner) {
        Alert alert = createAlert(Alert.AlertType.CONFIRMATION, title, message, owner);
        Optional<ButtonType> result = alert.showAndWait();
        return result.isPresent() && result.get() == ButtonType.OK;
    }

    /**
     * 显示通用对话框
     *
     * @param alertType 对话框类型
     * @param title     标题
     * @param message   消息内容
     * @param owner     所有者窗口
     */
    public static void showAlert(Alert.AlertType alertType, String title, String message, Window owner) {
        try {
            Alert alert = createAlert(alertType, title, message, owner);
            alert.showAndWait();
        } catch (Exception e) {
            logger.error("显示对话框失败: title={}, message={}", title, message, e);
        }
    }

    /**
     * 创建Alert对话框
     *
     * @param alertType 对话框类型
     * @param title     标题
     * @param message   消息内容
     * @param owner     所有者窗口
     * @return Alert对象
     */
    public static Alert createAlert(Alert.AlertType alertType, String title, String message, Window owner) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);

        // 设置所有者窗口
        if (owner != null) {
            alert.initOwner(owner);
            alert.initModality(Modality.WINDOW_MODAL);
        }

        return alert;
    }

    /**
     * 显示详细信息对话框
     *
     * @param title        标题
     * @param message      消息内容
     * @param detailText   详细信息
     */
    public static void showDetailedInfo(String title, String message, String detailText) {
        showDetailedInfo(title, message, detailText, null);
    }

    /**
     * 显示详细信息对话框（带所有者窗口）
     *
     * @param title        标题
     * @param message      消息内容
     * @param detailText   详细信息
     * @param owner        所有者窗口
     */
    public static void showDetailedInfo(String title, String message, String detailText, Window owner) {
        try {
            Alert alert = createAlert(Alert.AlertType.INFORMATION, title, message, owner);
            
            // 设置详细信息
            if (detailText != null && !detailText.trim().isEmpty()) {
                alert.getDialogPane().setExpandableContent(new javafx.scene.control.TextArea(detailText));
            }
            
            // 设置对话框大小
            alert.getDialogPane().setPrefWidth(500);
            alert.getDialogPane().setPrefHeight(200);
            
            alert.showAndWait();
        } catch (Exception e) {
            logger.error("显示详细信息对话框失败: title={}, message={}", title, message, e);
        }
    }

    /**
     * 显示错误对话框（兼容旧的showAlert方法）
     *
     * @param title   标题
     * @param message 消息内容
     */
    public static void showAlert(String title, String message) {
        showError(title, message);
    }

    /**
     * 显示错误对话框（兼容旧的showAlert方法，带所有者窗口）
     *
     * @param title   标题
     * @param message 消息内容
     * @param owner   所有者窗口
     */
    public static void showAlert(String title, String message, Window owner) {
        showError(title, message, owner);
    }

    /**
     * 根据消息类型自动选择对话框类型
     *
     * @param title   标题
     * @param message 消息内容
     * @param owner   所有者窗口
     */
    public static void showAutoTypeAlert(String title, String message, Window owner) {
        Alert.AlertType alertType;
        
        // 根据标题判断对话框类型
        String lowerTitle = title.toLowerCase();
        if (lowerTitle.contains("错误") || lowerTitle.contains("失败") || lowerTitle.contains("error")) {
            alertType = Alert.AlertType.ERROR;
        } else if (lowerTitle.contains("警告") || lowerTitle.contains("注意") || lowerTitle.contains("warning")) {
            alertType = Alert.AlertType.WARNING;
        } else if (lowerTitle.contains("提示") || lowerTitle.contains("信息") || lowerTitle.contains("成功")) {
            alertType = Alert.AlertType.INFORMATION;
        } else {
            alertType = Alert.AlertType.INFORMATION; // 默认为信息类型
        }
        
        showAlert(alertType, title, message, owner);
    }

    /**
     * 显示自定义按钮的对话框
     *
     * @param alertType   对话框类型
     * @param title       标题
     * @param message     消息内容
     * @param owner       所有者窗口
     * @param buttonTypes 自定义按钮类型
     * @return 用户选择的按钮类型
     */
    public static Optional<ButtonType> showCustomButtonAlert(Alert.AlertType alertType, String title, String message, 
                                                           Window owner, ButtonType... buttonTypes) {
        try {
            Alert alert = createAlert(alertType, title, message, owner);
            
            // 清除默认按钮并添加自定义按钮
            alert.getButtonTypes().clear();
            alert.getButtonTypes().addAll(buttonTypes);
            
            return alert.showAndWait();
        } catch (Exception e) {
            logger.error("显示自定义按钮对话框失败: title={}, message={}", title, message, e);
            return Optional.empty();
        }
    }
}
