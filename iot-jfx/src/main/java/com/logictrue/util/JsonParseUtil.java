package com.logictrue.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 简单的JSON解析工具类
 * 用于解析表格数据中的JSON格式字符串
 */
public class JsonParseUtil {
    private static final Logger logger = LoggerFactory.getLogger(JsonParseUtil.class);
    
    // 匹配JSON键值对的正则表达式
    private static final Pattern JSON_PAIR_PATTERN = Pattern.compile("\"([^\"]+)\"\\s*:\\s*\"([^\"]*)\"|\"([^\"]+)\"\\s*:\\s*([^,}]+)");
    
    /**
     * 解析简单的JSON字符串为Map
     * 支持格式：{"key1":"value1","key2":"value2"}
     * 
     * @param jsonString JSON字符串
     * @return 解析后的Map，解析失败返回空Map
     */
    public static Map<String, String> parseSimpleJson(String jsonString) {
        Map<String, String> result = new HashMap<>();
        
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return result;
        }
        
        try {
            String trimmed = jsonString.trim();
            
            // 检查是否是有效的JSON格式
            if (!trimmed.startsWith("{") || !trimmed.endsWith("}")) {
                logger.warn("无效的JSON格式: {}", jsonString);
                return result;
            }
            
            // 使用正则表达式匹配键值对
            Matcher matcher = JSON_PAIR_PATTERN.matcher(trimmed);
            
            while (matcher.find()) {
                String key = null;
                String value = null;
                
                if (matcher.group(1) != null) {
                    // 匹配带引号的值: "key":"value"
                    key = matcher.group(1);
                    value = matcher.group(2);
                } else if (matcher.group(3) != null) {
                    // 匹配不带引号的值: "key":value
                    key = matcher.group(3);
                    value = matcher.group(4);
                    if (value != null) {
                        value = value.trim();
                        // 移除可能的尾部逗号
                        if (value.endsWith(",")) {
                            value = value.substring(0, value.length() - 1);
                        }
                    }
                }
                
                if (key != null && value != null) {
                    result.put(key, value);
                }
            }
            
            logger.debug("JSON解析成功，键值对数量: {}", result.size());
            
        } catch (Exception e) {
            logger.error("JSON解析失败: {}", jsonString, e);
        }
        
        return result;
    }
    
    /**
     * 解析JSON字符串的备用方法
     * 使用简单的字符串分割方式
     * 
     * @param jsonString JSON字符串
     * @return 解析后的Map
     */
    public static Map<String, String> parseSimpleJsonFallback(String jsonString) {
        Map<String, String> result = new HashMap<>();
        
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return result;
        }
        
        try {
            String content = jsonString.trim();
            
            // 移除大括号
            if (content.startsWith("{") && content.endsWith("}")) {
                content = content.substring(1, content.length() - 1);
            }
            
            // 按逗号分割，但要考虑引号内的逗号
            String[] pairs = splitJsonPairs(content);
            
            for (String pair : pairs) {
                if (pair.trim().isEmpty()) continue;
                
                // 找到第一个冒号的位置
                int colonIndex = pair.indexOf(':');
                if (colonIndex > 0) {
                    String key = pair.substring(0, colonIndex).trim();
                    String value = pair.substring(colonIndex + 1).trim();
                    
                    // 移除引号
                    key = removeQuotes(key);
                    value = removeQuotes(value);
                    
                    result.put(key, value);
                }
            }
            
        } catch (Exception e) {
            logger.error("JSON备用解析失败: {}", jsonString, e);
        }
        
        return result;
    }
    
    /**
     * 分割JSON键值对，考虑引号内的逗号
     */
    private static String[] splitJsonPairs(String content) {
        java.util.List<String> pairs = new java.util.ArrayList<>();
        StringBuilder current = new StringBuilder();
        boolean inQuotes = false;
        
        for (int i = 0; i < content.length(); i++) {
            char c = content.charAt(i);
            
            if (c == '"' && (i == 0 || content.charAt(i - 1) != '\\')) {
                inQuotes = !inQuotes;
            }
            
            if (c == ',' && !inQuotes) {
                pairs.add(current.toString());
                current = new StringBuilder();
            } else {
                current.append(c);
            }
        }
        
        if (current.length() > 0) {
            pairs.add(current.toString());
        }
        
        return pairs.toArray(new String[0]);
    }
    
    /**
     * 移除字符串两端的引号
     */
    private static String removeQuotes(String str) {
        if (str == null) return null;
        
        str = str.trim();
        if (str.length() >= 2 && str.startsWith("\"") && str.endsWith("\"")) {
            return str.substring(1, str.length() - 1);
        }
        
        return str;
    }
    
    /**
     * 格式化显示Map内容（用于调试）
     */
    public static String formatMap(Map<String, String> map) {
        if (map == null || map.isEmpty()) {
            return "{}";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("{");
        
        boolean first = true;
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (!first) {
                sb.append(", ");
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue());
            first = false;
        }
        
        sb.append("}");
        return sb.toString();
    }
    
    /**
     * 验证JSON字符串格式是否正确
     */
    public static boolean isValidJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return false;
        }
        
        String trimmed = jsonString.trim();
        return trimmed.startsWith("{") && trimmed.endsWith("}") && trimmed.length() > 2;
    }
}
