package com.logictrue.util;

import com.logictrue.model.FormField;
import javafx.application.Platform;
import javafx.scene.control.ScrollPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 表单布局工具类
 * 提供动态表单高度计算和调整功能
 */
public class FormLayoutUtil {
    private static final Logger logger = LoggerFactory.getLogger(FormLayoutUtil.class);

    // 高度计算常量
    private static final double BASE_ROW_HEIGHT = 40.0;
    private static final double TEXTAREA_EXTRA_HEIGHT = 60.0;
    private static final double PADDING = 40.0;
    private static final double MIN_HEIGHT = 200.0;
    private static final double MAX_HEIGHT_MAIN = 600.0;
    private static final double MAX_HEIGHT_DIALOG = 500.0;

    /**
     * 计算表单高度配置
     */
    public static class FormHeightConfig {
        private final int fieldCount;
        private final int textAreaCount;
        private final int totalRows;
        private final double calculatedHeight;
        private final double finalHeight;
        private final boolean needsScrollbar;

        public FormHeightConfig(int fieldCount, int textAreaCount, int totalRows,
                               double maxHeight) {
            this.fieldCount = fieldCount;
            this.textAreaCount = textAreaCount;
            this.totalRows = totalRows;
            this.calculatedHeight = (totalRows * BASE_ROW_HEIGHT) +
                                   (textAreaCount * TEXTAREA_EXTRA_HEIGHT) + PADDING;
            this.finalHeight = Math.max(MIN_HEIGHT, Math.min(maxHeight, calculatedHeight));
            this.needsScrollbar = calculatedHeight > finalHeight;
        }

        public int getFieldCount() { return fieldCount; }
        public int getTextAreaCount() { return textAreaCount; }
        public int getTotalRows() { return totalRows; }
        public double getCalculatedHeight() { return calculatedHeight; }
        public double getFinalHeight() { return finalHeight; }
        public boolean needsScrollbar() { return needsScrollbar; }
    }

    /**
     * 计算表单布局信息
     */
    public static FormLayoutInfo calculateFormLayout(List<FormField> formFields, int maxColumns) {
        int row = 0;
        int col = 0;
        int textAreaCount = 0;

        for (FormField field : formFields) {
            col++;
            if (col >= maxColumns || field.getType() == FormField.FieldType.TEXTAREA) {
                col = 0;
                row++;

                if (field.getType() == FormField.FieldType.TEXTAREA) {
                    textAreaCount++;
                }
            }
        }

        return new FormLayoutInfo(formFields.size(), textAreaCount, row);
    }

    /**
     * 表单布局信息
     */
    public static class FormLayoutInfo {
        private final int fieldCount;
        private final int textAreaCount;
        private final int totalRows;

        public FormLayoutInfo(int fieldCount, int textAreaCount, int totalRows) {
            this.fieldCount = fieldCount;
            this.textAreaCount = textAreaCount;
            this.totalRows = totalRows;
        }

        public int getFieldCount() { return fieldCount; }
        public int getTextAreaCount() { return textAreaCount; }
        public int getTotalRows() { return totalRows; }
    }

    /**
     * 为主页面表单调整高度
     */
    public static void adjustMainFormHeight(ScrollPane scrollPane, FormLayoutInfo layoutInfo) {
        adjustFormHeight(scrollPane, layoutInfo, MAX_HEIGHT_MAIN, false);
    }

    /**
     * 为对话框表单调整高度
     */
    public static void adjustDialogFormHeight(ScrollPane scrollPane, FormLayoutInfo layoutInfo) {
        adjustFormHeight(scrollPane, layoutInfo, MAX_HEIGHT_DIALOG, true);
    }

    /**
     * 调整表单高度的通用方法
     */
    private static void adjustFormHeight(ScrollPane scrollPane, FormLayoutInfo layoutInfo,
                                       double maxHeight, boolean adjustWindow) {
        if (scrollPane == null) {
            logger.warn("ScrollPane为null，无法调整高度");
            return;
        }

        FormHeightConfig config = new FormHeightConfig(
            layoutInfo.getFieldCount(),
            layoutInfo.getTextAreaCount(),
            layoutInfo.getTotalRows(),
            maxHeight
        );

        logger.info("动态调整表单高度: 字段数={}, 文本区域数={}, 总行数={}, 计算高度={}, 最终高度={}",
                   config.getFieldCount(), config.getTextAreaCount(), config.getTotalRows(),
                   config.getCalculatedHeight(), config.getFinalHeight());

        Platform.runLater(() -> {
            // 设置精确的高度控制
            double finalHeight = config.getFinalHeight();

            scrollPane.setMaxHeight(finalHeight);
            scrollPane.setPrefHeight(finalHeight);
            scrollPane.setMinHeight(finalHeight);

            // 确保ScrollPane不会自动扩展
            scrollPane.setFitToHeight(false);

            // 设置滚动条策略
            if (config.needsScrollbar()) {
                scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
            } else {
                scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
            }

            // 不设置父容器高度，让它自然适应内容
            // 这样可以避免在StackPane中出现垂直居中的问题

            logger.info("ScrollPane高度设置完成: prefHeight={}, minHeight={}, maxHeight={}, 计算高度={}",
                        scrollPane.getPrefHeight(), scrollPane.getMinHeight(), scrollPane.getMaxHeight(), finalHeight);

            // 如果需要调整窗口大小
            if (adjustWindow) {
                adjustWindowSize(scrollPane, finalHeight);
            }
        });
    }

    /**
     * 调整窗口大小以适应表单内容
     */
    private static void adjustWindowSize(ScrollPane scrollPane, double formHeight) {
        try {
            Stage stage = (Stage) scrollPane.getScene().getWindow();
            if (stage != null) {
                // 计算窗口总高度（表单高度 + 标题 + 按钮区域 + 边距）
                double titleHeight = 50;
                double buttonAreaHeight = 80;
                double margins = 60;
                double totalHeight = formHeight + titleHeight + buttonAreaHeight + margins;

                // 限制窗口高度
                double maxWindowHeight = 700;
                double finalWindowHeight = Math.min(totalHeight, maxWindowHeight);

                stage.setHeight(finalWindowHeight);
                logger.info("调整窗口高度: {}", finalWindowHeight);
            }
        } catch (Exception e) {
            logger.warn("调整窗口大小失败", e);
        }
    }

}
