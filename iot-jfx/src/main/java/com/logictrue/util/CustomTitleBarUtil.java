package com.logictrue.util;

import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Parent;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 自定义标题栏工具类
 * 提供统一的无最小化按钮的自定义标题栏
 */
public class CustomTitleBarUtil {
    private static final Logger logger = LoggerFactory.getLogger(CustomTitleBarUtil.class);

    // 标题栏高度
    private static final double TITLE_BAR_HEIGHT = 30.0;
    
    // 标题栏样式
    private static final String TITLE_BAR_STYLE = 
        "-fx-background-color: #f0f0f0; " +
        "-fx-border-color: #cccccc; " +
        "-fx-border-width: 0 0 1 0;";
    
    // 标题文字样式
    private static final String TITLE_LABEL_STYLE = 
        "-fx-font-weight: bold; " +
        "-fx-text-fill: #333333;";
    
    // 按钮默认样式
    private static final String BUTTON_DEFAULT_STYLE = 
        "-fx-background-color: transparent; " +
        "-fx-border-color: transparent; " +
        "-fx-text-fill: #666666; " +
        "-fx-font-weight: bold; " +
        "-fx-padding: 2 8 2 8;";

    /**
     * 创建带自定义标题栏的窗口容器（包含最大化按钮）
     *
     * @param content 窗口内容
     * @param title   窗口标题
     * @param stage   窗口Stage
     * @return 包含自定义标题栏的VBox容器
     */
    public static VBox createWindowWithTitleBar(Parent content, String title, Stage stage) {
        return createWindowWithTitleBar(content, title, stage, true);
    }

    /**
     * 创建带自定义标题栏的窗口容器
     *
     * @param content        窗口内容
     * @param title          窗口标题
     * @param stage          窗口Stage
     * @param showMaximize   是否显示最大化按钮
     * @return 包含自定义标题栏的VBox容器
     */
    public static VBox createWindowWithTitleBar(Parent content, String title, Stage stage, boolean showMaximize) {
        VBox windowContainer = new VBox();
        windowContainer.setSpacing(0);

        // 创建标题栏
        HBox titleBar = createTitleBar(title, stage, showMaximize);

        // 组装整个窗口
        windowContainer.getChildren().addAll(titleBar, content);
        VBox.setVgrow(content, Priority.ALWAYS);

        return windowContainer;
    }

    /**
     * 创建标题栏
     *
     * @param title        标题文字
     * @param stage        窗口Stage
     * @param showMaximize 是否显示最大化按钮
     * @return 标题栏HBox
     */
    private static HBox createTitleBar(String title, Stage stage, boolean showMaximize) {
        HBox titleBar = new HBox();
        titleBar.setPrefHeight(TITLE_BAR_HEIGHT);
        titleBar.setStyle(TITLE_BAR_STYLE);
        titleBar.setAlignment(Pos.CENTER_LEFT);
        titleBar.setPadding(new Insets(5, 10, 5, 10));

        // 标题标签
        Label titleLabel = new Label(title);
        titleLabel.setStyle(TITLE_LABEL_STYLE);

        // 占位区域，用于推送按钮到右边
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);

        // 创建按钮容器
        HBox buttonContainer = new HBox();
        buttonContainer.setSpacing(0);

        // 最大化按钮（可选）
        if (showMaximize) {
            Button maximizeButton = createMaximizeButton(stage);
            buttonContainer.getChildren().add(maximizeButton);
        }

        // 关闭按钮
        Button closeButton = createCloseButton(stage);
        buttonContainer.getChildren().add(closeButton);

        // 添加标题栏拖拽功能
        addDragFunctionality(titleBar, stage);

        // 添加双击最大化功能（如果显示最大化按钮）
        if (showMaximize) {
            addDoubleClickMaximize(titleBar, stage);
        }

        // 组装标题栏
        titleBar.getChildren().addAll(titleLabel, spacer, buttonContainer);

        return titleBar;
    }

    /**
     * 创建最大化按钮
     */
    private static Button createMaximizeButton(Stage stage) {
        Button maximizeButton = new Button("□");
        maximizeButton.setStyle(BUTTON_DEFAULT_STYLE + "-fx-font-size: 14px;");

        // 最大化按钮悬停效果
        maximizeButton.setOnMouseEntered(e -> maximizeButton.setStyle(
            "-fx-background-color: #e0e0e0; " +
            "-fx-border-color: transparent; " +
            "-fx-text-fill: #333333; " +
            "-fx-font-size: 14px; " +
            "-fx-font-weight: bold; " +
            "-fx-padding: 2 8 2 8;"
        ));

        maximizeButton.setOnMouseExited(e -> maximizeButton.setStyle(
            BUTTON_DEFAULT_STYLE + "-fx-font-size: 14px;"
        ));

        // 最大化按钮点击事件
        maximizeButton.setOnAction(e -> {
            toggleMaximize(stage, maximizeButton);
        });

        return maximizeButton;
    }

    /**
     * 创建关闭按钮
     */
    private static Button createCloseButton(Stage stage) {
        Button closeButton = new Button("×");
        closeButton.setStyle(BUTTON_DEFAULT_STYLE + "-fx-font-size: 16px;");

        // 关闭按钮悬停效果
        closeButton.setOnMouseEntered(e -> closeButton.setStyle(
            "-fx-background-color: #ff4444; " +
            "-fx-border-color: transparent; " +
            "-fx-text-fill: white; " +
            "-fx-font-size: 16px; " +
            "-fx-font-weight: bold; " +
            "-fx-padding: 2 8 2 8;"
        ));

        closeButton.setOnMouseExited(e -> closeButton.setStyle(
            BUTTON_DEFAULT_STYLE + "-fx-font-size: 16px;"
        ));

        // 关闭按钮点击事件
        closeButton.setOnAction(e -> stage.close());

        return closeButton;
    }

    /**
     * 添加标题栏拖拽功能
     */
    private static void addDragFunctionality(HBox titleBar, Stage stage) {
        final double[] xOffset = {0};
        final double[] yOffset = {0};

        titleBar.setOnMousePressed(event -> {
            xOffset[0] = event.getSceneX();
            yOffset[0] = event.getSceneY();
        });

        titleBar.setOnMouseDragged(event -> {
            // 只有在窗口未最大化时才允许拖拽
            if (!stage.isMaximized()) {
                stage.setX(event.getScreenX() - xOffset[0]);
                stage.setY(event.getScreenY() - yOffset[0]);
            }
        });
    }

    /**
     * 添加双击标题栏最大化功能
     */
    private static void addDoubleClickMaximize(HBox titleBar, Stage stage) {
        titleBar.setOnMouseClicked(event -> {
            if (event.getClickCount() == 2) {
                // 查找最大化按钮并更新其状态
                Button maximizeButton = findMaximizeButton(titleBar);
                toggleMaximize(stage, maximizeButton);
            }
        });
    }

    /**
     * 切换最大化状态
     */
    private static void toggleMaximize(Stage stage, Button maximizeButton) {
        if (stage.isMaximized()) {
            stage.setMaximized(false);
            if (maximizeButton != null) {
                maximizeButton.setText("□");
            }
            logger.debug("窗口已还原");
        } else {
            stage.setMaximized(true);
            if (maximizeButton != null) {
                maximizeButton.setText("❐");
            }
            logger.debug("窗口已最大化");
        }
    }

    /**
     * 查找标题栏中的最大化按钮
     */
    private static Button findMaximizeButton(HBox titleBar) {
        return titleBar.getChildren().stream()
                .filter(node -> node instanceof HBox) // 按钮容器
                .map(node -> (HBox) node)
                .flatMap(container -> container.getChildren().stream())
                .filter(node -> node instanceof Button)
                .map(node -> (Button) node)
                .filter(button -> "□".equals(button.getText()) || "❐".equals(button.getText()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取标题栏高度
     */
    public static double getTitleBarHeight() {
        return TITLE_BAR_HEIGHT;
    }

    /**
     * 创建简单的只有关闭按钮的标题栏（用于小弹窗）
     *
     * @param content 窗口内容
     * @param title   窗口标题
     * @param stage   窗口Stage
     * @return 包含自定义标题栏的VBox容器
     */
    public static VBox createSimpleWindowWithTitleBar(Parent content, String title, Stage stage) {
        return createWindowWithTitleBar(content, title, stage, false);
    }

    /**
     * 为现有Stage应用自定义标题栏样式
     *
     * @param stage        目标Stage
     * @param content      窗口内容
     * @param title        窗口标题
     * @param showMaximize 是否显示最大化按钮
     * @param width        窗口宽度
     * @param height       窗口高度（会自动增加标题栏高度）
     */
    public static void applyCustomTitleBar(Stage stage, Parent content, String title, 
                                         boolean showMaximize, double width, double height) {
        try {
            // 创建自定义标题栏容器
            VBox windowContainer = createWindowWithTitleBar(content, title, stage, showMaximize);
            
            // 设置场景
            javafx.scene.Scene scene = new javafx.scene.Scene(windowContainer, width, height + TITLE_BAR_HEIGHT);
            stage.setScene(scene);
            
            // 设置窗口样式
            stage.initStyle(javafx.stage.StageStyle.UNDECORATED);
            
            // 如果显示最大化按钮，允许调整大小
            stage.setResizable(showMaximize);
            
            logger.info("已为窗口应用自定义标题栏: {}", title);
            
        } catch (Exception e) {
            logger.error("应用自定义标题栏失败: {}", title, e);
        }
    }
}
