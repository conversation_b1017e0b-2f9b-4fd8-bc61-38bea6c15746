package com.logictrue;

import com.logictrue.controller.MainController;
import com.logictrue.util.PathUtils;
import javafx.application.Application;
import javafx.application.Platform;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.input.KeyCombination;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;

/**
 * IoT数据采集JavaFX应用程序
 */
public class App extends Application {
    private static final Logger logger = LoggerFactory.getLogger(App.class);

    private MainController mainController;

    @Override
    public void start(Stage primaryStage) {
        try {
            logger.info("启动IoT数据采集应用程序");

            // 加载主界面FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/main.fxml"));
            Parent root = loader.load();
            primaryStage.setFullScreenExitHint("");
            primaryStage.setFullScreenExitKeyCombination(KeyCombination.NO_MATCH);
            primaryStage.setAlwaysOnTop(true);
            primaryStage.setFullScreen(true);
            // 获取控制器引用
            mainController = loader.getController();

            // 创建场景并应用样式
            Scene scene = new Scene(root, 800, 600);
            scene.getStylesheets().add(getClass().getResource("/css/application.css").toExternalForm());

            // 配置主窗口
            primaryStage.setTitle("IoT数据采集系统");
            primaryStage.setScene(scene);
            primaryStage.setMinWidth(800);
            primaryStage.setMinHeight(600);
            primaryStage.setResizable(true);

            // 将主舞台传递给控制器
            mainController.setPrimaryStage(primaryStage);

            // 设置关闭事件处理
            primaryStage.setOnCloseRequest(event -> {
                logger.info("应用程序正在关闭");
                if (mainController != null) {
                    mainController.shutdown();
                }
                Platform.exit();
                System.exit(0);
            });

            primaryStage.show();
            logger.info("应用程序启动成功");

        } catch (Exception e) {
            logger.error("启动应用程序失败", e);
            Platform.exit();
            System.exit(1);
        }
    }

    public static void main(String[] args) {
        // 在启动应用程序之前设置系统属性
        setupSystemProperties();
        launch(args);
    }

    /**
     * 设置系统属性，包括日志目录和数据库目录
     */
    private static void setupSystemProperties() {
        try {
            // 获取应用程序目录
            String appDir = PathUtils.getApplicationDirectory();

            // 设置日志目录
            String logDir = appDir + File.separator + "logs";
            System.setProperty("iot.log.dir", logDir);

            // 设置数据库目录
            System.setProperty("iot.data.dir", appDir);

            // 确保目录存在
            PathUtils.ensureDirectoryExists(logDir);

        } catch (Exception e) {
            System.err.println("设置系统属性失败: " + e.getMessage());
            e.printStackTrace();
        }
    }


}
