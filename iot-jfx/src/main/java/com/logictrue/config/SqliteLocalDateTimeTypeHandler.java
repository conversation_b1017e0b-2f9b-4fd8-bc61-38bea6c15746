package com.logictrue.config;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * SQLite LocalDateTime类型处理器
 * 解决SQLite时间戳格式与MyBatis LocalDateTime类型处理器不兼容的问题
 */
@MappedTypes(LocalDateTime.class)
@MappedJdbcTypes({JdbcType.TIMESTAMP, JdbcType.VARCHAR})
public class SqliteLocalDateTimeTypeHandler extends BaseTypeHandler<LocalDateTime> {

    // 支持多种时间格式
    private static final DateTimeFormatter[] FORMATTERS = {
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
        DateTimeFormatter.ISO_LOCAL_DATE_TIME
    };

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, LocalDateTime parameter, JdbcType jdbcType) throws SQLException {
        // 存储时使用标准格式
        ps.setString(i, parameter.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")));
    }

    @Override
    public LocalDateTime getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        return parseLocalDateTime(value);
    }

    @Override
    public LocalDateTime getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        return parseLocalDateTime(value);
    }

    @Override
    public LocalDateTime getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        return parseLocalDateTime(value);
    }

    /**
     * 解析LocalDateTime，支持多种格式
     */
    private LocalDateTime parseLocalDateTime(String value) throws SQLException {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }

        // 清理时间戳字符串，移除可能的额外精度
        String cleanValue = cleanTimestamp(value.trim());

        // 尝试使用不同的格式解析
        for (DateTimeFormatter formatter : FORMATTERS) {
            try {
                return LocalDateTime.parse(cleanValue, formatter);
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
            }
        }

        // 如果所有格式都失败，尝试处理特殊情况
        try {
            // 处理SQLite的CURRENT_TIMESTAMP格式
            if (cleanValue.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                return LocalDateTime.parse(cleanValue, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }

            // 处理带有纳秒的ISO格式
            if (cleanValue.contains("T") && cleanValue.length() > 19) {
                // 截取到毫秒精度
                int dotIndex = cleanValue.lastIndexOf('.');
                if (dotIndex > 0) {
                    String beforeDot = cleanValue.substring(0, dotIndex);
                    String afterDot = cleanValue.substring(dotIndex + 1);
                    
                    // 只保留前3位毫秒
                    if (afterDot.length() > 3) {
                        afterDot = afterDot.substring(0, 3);
                    }
                    
                    cleanValue = beforeDot + "." + afterDot;
                    return LocalDateTime.parse(cleanValue, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"));
                }
            }

        } catch (Exception e) {
            // 最后的尝试失败
        }

        throw new SQLException("无法解析时间戳: " + value + " (清理后: " + cleanValue + ")");
    }

    /**
     * 清理时间戳字符串
     */
    private String cleanTimestamp(String timestamp) {
        if (timestamp == null) {
            return null;
        }

        // 移除可能的时区信息
        timestamp = timestamp.replaceAll("[+-]\\d{2}:?\\d{2}$", "");
        timestamp = timestamp.replaceAll("Z$", "");

        // 处理纳秒精度，只保留毫秒
        if (timestamp.contains(".")) {
            int dotIndex = timestamp.lastIndexOf('.');
            String beforeDot = timestamp.substring(0, dotIndex);
            String afterDot = timestamp.substring(dotIndex + 1);

            // 如果小数部分超过6位，截取前6位
            if (afterDot.length() > 6) {
                afterDot = afterDot.substring(0, 6);
            }
            // 如果小数部分超过3位但不超过6位，截取前3位
            else if (afterDot.length() > 3) {
                afterDot = afterDot.substring(0, 3);
            }

            timestamp = beforeDot + "." + afterDot;
        }

        return timestamp;
    }
}
