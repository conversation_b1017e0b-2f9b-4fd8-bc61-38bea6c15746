package com.logictrue.service;

import com.logictrue.iot.entity.DeviceDetectionBasicField;
import com.logictrue.mapper.DeviceDetectionBasicFieldMapper;
import org.apache.ibatis.session.SqlSession;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备检测基础字段Service
 */
public class DeviceDetectionBasicFieldService extends BaseService<DeviceDetectionBasicField, DeviceDetectionBasicFieldMapper> {

    public DeviceDetectionBasicFieldService() {
        initService();
    }

    @Override
    protected DeviceDetectionBasicFieldMapper getMapper(SqlSession sqlSession) {
        return sqlSession.getMapper(DeviceDetectionBasicFieldMapper.class);
    }

    /**
     * 批量插入基础字段数据
     * 使用XML配置的foreach标签实现真正的SQL批量插入
     *
     * @param detectionDataId 检测数据ID
     * @param basicFields 基础字段列表
     * @return 插入是否成功
     */
    public boolean batchInsertBasicFields(Long detectionDataId, List<DeviceDetectionBasicField> basicFields) {
        if (basicFields == null || basicFields.isEmpty()) {
            logger.warn("基础字段列表为空，跳过插入");
            return true;
        }

        try {

            // 使用XML配置的批量插入，一次SQL执行插入所有数据
            int insertCount = executeWithSession(mapper -> mapper.batchInsert(basicFields));

            if (insertCount == basicFields.size()) {
                logger.info("成功批量插入基础字段数据，检测数据ID: {}, 插入数量: {}", detectionDataId, insertCount);
                return true;
            } else {
                logger.error("批量插入基础字段数据部分失败，检测数据ID: {}, 期望: {}, 实际: {}",
                        detectionDataId, basicFields.size(), insertCount);
                return false;
            }

        } catch (Exception e) {
            logger.error("批量插入基础字段数据失败，检测数据ID: {}", detectionDataId, e);
            return false;
        }
    }

    /**
     * 分批批量插入基础字段数据（适用于大数据量）
     *
     * @param detectionDataId 检测数据ID
     * @param basicFields 基础字段列表
     * @param batchSize 每批次大小
     * @return 插入是否成功
     */
    public boolean batchInsertBasicFieldsInChunks(Long detectionDataId, List<DeviceDetectionBasicField> basicFields, int batchSize) {
        if (basicFields == null || basicFields.isEmpty()) {
            logger.warn("基础字段列表为空，跳过插入");
            return true;
        }

        try {
            int totalSize = basicFields.size();
            int totalInserted = 0;

            // 分批处理
            for (int i = 0; i < totalSize; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalSize);
                List<DeviceDetectionBasicField> chunk = basicFields.subList(i, endIndex);

                int insertCount = mapper.batchInsert(chunk);
                totalInserted += insertCount;

                logger.debug("批量插入进度: {}/{}, 当前批次: {}", totalInserted, totalSize, insertCount);
            }

            if (totalInserted == totalSize) {
                logger.info("成功分批批量插入基础字段数据，检测数据ID: {}, 总插入数量: {}", detectionDataId, totalInserted);
                return true;
            } else {
                logger.error("分批批量插入基础字段数据部分失败，检测数据ID: {}, 期望: {}, 实际: {}",
                        detectionDataId, totalSize, totalInserted);
                return false;
            }

        } catch (Exception e) {
            logger.error("分批批量插入基础字段数据失败，检测数据ID: {}", detectionDataId, e);
            return false;
        }
    }

    /**
     * 根据检测数据ID查询基础字段
     */
    public List<DeviceDetectionBasicField> getByDetectionDataId(Long detectionDataId) {
        try {
            return executeWithSession(mapper -> mapper.selectByDetectionDataId(detectionDataId));
        } catch (Exception e) {
            logger.error("根据检测数据ID查询基础字段失败，ID: {}", detectionDataId, e);
            return null;
        }
    }

    /**
     * 根据检测数据ID和工作表ID查询基础字段
     */
    public List<DeviceDetectionBasicField> getByDetectionDataIdAndSheetId(Long detectionDataId, String sheetId) {
        try {
            return executeWithSession(mapper -> mapper.selectByDetectionDataIdAndSheetId(detectionDataId, sheetId));
        } catch (Exception e) {
            logger.error("根据检测数据ID和工作表ID查询基础字段失败，检测数据ID: {}, 工作表ID: {}", detectionDataId, sheetId, e);
            return null;
        }
    }

    /**
     * 根据检测数据ID统计基础字段数量
     */
    public long countByDetectionDataId(Long detectionDataId) {
        try {
            return executeWithSession(mapper -> mapper.countByDetectionDataId(detectionDataId));
        } catch (Exception e) {
            logger.error("根据检测数据ID统计基础字段数量失败，ID: {}", detectionDataId, e);
            return 0;
        }
    }

    /**
     * 根据检测数据ID删除基础字段
     */
    public boolean deleteByDetectionDataId(Long detectionDataId) {
        try {
            int result = executeWithSession(mapper -> mapper.deleteByDetectionDataId(detectionDataId));
            logger.info("删除基础字段数据，检测数据ID: {}, 删除数量: {}", detectionDataId, result);
            return result > 0;
        } catch (Exception e) {
            logger.error("根据检测数据ID删除基础字段失败，ID: {}", detectionDataId, e);
            return false;
        }
    }
}
