package com.logictrue.service;

import com.logictrue.config.ConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 心跳监控服务
 */
public class HeartbeatService {
    private static final Logger logger = LoggerFactory.getLogger(HeartbeatService.class);

    private ScheduledExecutorService scheduler;
    private NetworkService networkService;
    private ConfigManager configManager;
    private AutoPushService autoPushService;
    private AutoCollectionService autoCollectionService;
    private boolean running = false;

    public HeartbeatService() {
        this.networkService = new NetworkService();
        this.configManager = ConfigManager.getInstance();
        this.autoPushService = new AutoPushService();
        this.autoCollectionService = new AutoCollectionService();
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "HeartbeatService");
            thread.setDaemon(true);
            return thread;
        });
    }

    /**
     * 启动心跳监控
     */
    public void start() {
        if (running) {
            logger.warn("心跳服务已经在运行中");
            return;
        }

        running = true;
        int heartbeatInterval = configManager.getHeartbeatInterval();
        logger.info("启动心跳监控服务，间隔: {}秒", heartbeatInterval);

        scheduler.scheduleAtFixedRate(this::performHeartbeat, 0, heartbeatInterval, TimeUnit.SECONDS);
    }

    /**
     * 停止心跳监控
     */
    public void stop() {
        if (!running) {
            return;
        }

        running = false;
        logger.info("停止心跳监控服务");

        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 重启心跳监控服务（用于应用新的配置）
     */
    public void restart() {
        logger.info("重启心跳监控服务以应用新配置");
        stop();

        // 重新创建调度器
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "HeartbeatService");
            thread.setDaemon(true);
            return thread;
        });

        start();
    }

    /**
     * 执行心跳检测
     */
    private void performHeartbeat() {
        try {
            // 执行自动采集前 检查自动采集是否开启 检查是否超过自动采集间隔
            triggerAutoCollectionBeforeHeartbeat();

            networkService.sendHeartbeat().thenAccept(result -> {
                if (result.isSuccess()) {
                    logger.info("心跳检测成功 - 状态码: {}, 响应时间: {}ms",
                            result.getStatusCode(), result.getResponseTime());

                    // 心跳成功后触发自动推送
                    triggerAutoPushAfterHeartbeat();
                } else {
                    logger.warn("心跳检测失败 - 状态码: {}, 响应时间: {}ms, 错误信息: {}",
                            result.getStatusCode(), result.getResponseTime(), result.getMessage());
                }
            }).exceptionally(throwable -> {
                logger.error("心跳检测异常", throwable);
                return null;
            });
        } catch (Exception e) {
            logger.error("执行心跳检测时发生异常", e);
        }
    }

    /**
     * 检查服务是否正在运行
     */
    public boolean isRunning() {
        return running;
    }


    /**
     * 心跳检测前触发自动采集
     */
    private void triggerAutoCollectionBeforeHeartbeat() {
        try {
            // 检查是否启用自动采集
            if (!configManager.isAutoExcelCollection()) {
                logger.debug("自动采集功能已禁用，跳过采集");
                return;
            }

            logger.debug("开始检查自动采集条件");

            // 异步执行自动采集，避免阻塞心跳线程
            autoCollectionService.performAutoCollection().thenAccept(collectionResult -> {
                if (collectionResult.isSuccess()) {
                    if (collectionResult.getProcessedCount() > 0) {
                        logger.info("自动采集完成，成功处理{}个文件", collectionResult.getProcessedCount());
                    } else {
                        logger.debug("自动采集完成，{}", collectionResult.getMessage());
                    }
                } else {
                    logger.warn("自动采集失败: {}", collectionResult.getMessage());
                }
            }).exceptionally(throwable -> {
                logger.error("自动采集异常", throwable);
                return null;
            });

        } catch (Exception e) {
            logger.error("触发自动采集时发生异常", e);
        }
    }

    /**
     * 心跳成功后触发自动推送
     */
    private void triggerAutoPushAfterHeartbeat() {
        try {
            // 检查是否启用自动推送
            if (!configManager.isAutoPushEnabled()) {
                logger.debug("自动推送功能已禁用，跳过推送");
                return;
            }

            logger.debug("心跳成功，开始执行自动推送");

            // 异步执行自动推送，避免阻塞心跳线程
            autoPushService.performAutoPush().thenAccept(pushResult -> {
                if (pushResult.isSuccess()) {
                    if (pushResult.getSuccessCount() > 0) {
                        logger.info("自动推送完成，成功推送{}条记录", pushResult.getSuccessCount());
                    } else {
                        logger.debug("自动推送完成，{}", pushResult.getMessage());
                    }
                } else {
                    logger.warn("自动推送失败: {}", pushResult.getMessage());
                }
            }).exceptionally(throwable -> {
                logger.error("自动推送异常", throwable);
                return null;
            });

        } catch (Exception e) {
            logger.error("触发自动推送时发生异常", e);
        }
    }
}
