package com.logictrue.service;

import com.logictrue.iot.entity.DeviceDetectionTableData;
import com.logictrue.mapper.DeviceDetectionTableDataMapper;
import org.apache.ibatis.session.SqlSession;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备检测表格数据Service
 */
public class DeviceDetectionTableDataService extends BaseService<DeviceDetectionTableData, DeviceDetectionTableDataMapper> {

    public DeviceDetectionTableDataService() {
        initService();
    }

    @Override
    protected DeviceDetectionTableDataMapper getMapper(SqlSession sqlSession) {
        return sqlSession.getMapper(DeviceDetectionTableDataMapper.class);
    }


    /**
     * 根据检测数据ID查询表格数据
     */
    public List<DeviceDetectionTableData> getByDetectionDataId(Long detectionDataId) {
        try {
            return executeWithSession(mapper -> mapper.selectByDetectionDataId(detectionDataId));
        } catch (Exception e) {
            logger.error("根据检测数据ID查询表格数据失败，ID: {}", detectionDataId, e);
            return null;
        }
    }

}
