package com.logictrue.service;

import com.logictrue.config.ConfigManager;
import com.logictrue.model.FormField;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 表单验证服务
 * 负责校验必填字段是否已填写
 */
public class FormValidationService {
    private static final Logger logger = LoggerFactory.getLogger(FormValidationService.class);
    
    private ConfigManager configManager;
    
    public FormValidationService() {
        this.configManager = ConfigManager.getInstance();
    }
    
    /**
     * 校验必填字段
     * @param formData 表单数据
     * @return 校验结果
     */
    public ValidationResult validateRequiredFields(Map<String, Object> formData) {
        List<FormField> formFields = configManager.getFormFields();
        List<String> missingFields = new ArrayList<>();
        List<String> emptyFields = new ArrayList<>();
        
        logger.info("开始校验必填字段，共{}个字段", formFields.size());
        
        for (FormField field : formFields) {
            if (field.isRequired()) {
                String fieldName = field.getName();
                String fieldLabel = field.getLabel();
                
                logger.debug("校验必填字段: {} ({})", fieldLabel, fieldName);
                
                if (!formData.containsKey(fieldName)) {
                    missingFields.add(fieldLabel);
                    logger.warn("必填字段缺失: {} ({})", fieldLabel, fieldName);
                } else {
                    Object value = formData.get(fieldName);
                    if (isEmptyValue(value)) {
                        emptyFields.add(fieldLabel);
                        logger.warn("必填字段为空: {} ({}), 值: {}", fieldLabel, fieldName, value);
                    } else {
                        logger.debug("必填字段校验通过: {} ({}), 值: {}", fieldLabel, fieldName, value);
                    }
                }
            }
        }
        
        boolean isValid = missingFields.isEmpty() && emptyFields.isEmpty();
        ValidationResult result = new ValidationResult(isValid, missingFields, emptyFields);
        
        logger.info("必填字段校验完成，结果: {}, 缺失字段: {}, 空值字段: {}", 
                   isValid ? "通过" : "失败", missingFields.size(), emptyFields.size());
        
        return result;
    }
    
    /**
     * 判断值是否为空
     */
    private boolean isEmptyValue(Object value) {
        if (value == null) {
            return true;
        }
        
        if (value instanceof String) {
            return ((String) value).trim().isEmpty();
        }
        
        if (value instanceof Number) {
            return false; // 数字类型不为空
        }
        
        return value.toString().trim().isEmpty();
    }
    
    /**
     * 获取所有必填字段
     */
    public List<FormField> getRequiredFields() {
        List<FormField> formFields = configManager.getFormFields();
        List<FormField> requiredFields = new ArrayList<>();
        
        for (FormField field : formFields) {
            if (field.isRequired()) {
                requiredFields.add(field);
            }
        }
        
        logger.info("获取必填字段，共{}个", requiredFields.size());
        return requiredFields;
    }
    
    /**
     * 检查是否有必填字段
     */
    public boolean hasRequiredFields() {
        List<FormField> formFields = configManager.getFormFields();
        for (FormField field : formFields) {
            if (field.isRequired()) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 校验结果类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final List<String> missingFields;
        private final List<String> emptyFields;
        
        public ValidationResult(boolean valid, List<String> missingFields, List<String> emptyFields) {
            this.valid = valid;
            this.missingFields = missingFields != null ? missingFields : new ArrayList<>();
            this.emptyFields = emptyFields != null ? emptyFields : new ArrayList<>();
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public List<String> getMissingFields() {
            return missingFields;
        }
        
        public List<String> getEmptyFields() {
            return emptyFields;
        }
        
        public List<String> getAllInvalidFields() {
            List<String> allInvalid = new ArrayList<>();
            allInvalid.addAll(missingFields);
            allInvalid.addAll(emptyFields);
            return allInvalid;
        }
        
        public String getErrorMessage() {
            if (valid) {
                return "";
            }
            
            StringBuilder message = new StringBuilder();
            
            if (!missingFields.isEmpty()) {
                message.append("以下必填字段缺失：\n");
                for (String field : missingFields) {
                    message.append("• ").append(field).append("\n");
                }
            }
            
            if (!emptyFields.isEmpty()) {
                if (message.length() > 0) {
                    message.append("\n");
                }
                message.append("以下必填字段为空：\n");
                for (String field : emptyFields) {
                    message.append("• ").append(field).append("\n");
                }
            }
            
            return message.toString().trim();
        }
        
        @Override
        public String toString() {
            return "ValidationResult{" +
                    "valid=" + valid +
                    ", missingFields=" + missingFields +
                    ", emptyFields=" + emptyFields +
                    '}';
        }
    }
}
