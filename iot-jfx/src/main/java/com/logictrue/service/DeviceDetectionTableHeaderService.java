package com.logictrue.service;

import com.logictrue.iot.entity.DeviceDetectionTableHeader;
import com.logictrue.mapper.DeviceDetectionTableHeaderMapper;
import org.apache.ibatis.session.SqlSession;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备检测表头Service
 */
public class DeviceDetectionTableHeaderService extends BaseService<DeviceDetectionTableHeader, DeviceDetectionTableHeaderMapper> {

    public DeviceDetectionTableHeaderService() {
        initService();
    }

    @Override
    protected DeviceDetectionTableHeaderMapper getMapper(SqlSession sqlSession) {
        return sqlSession.getMapper(DeviceDetectionTableHeaderMapper.class);
    }

    /**
     * 根据检测数据ID查询表头
     */
    public List<DeviceDetectionTableHeader> getByDetectionDataId(Long detectionDataId) {
        try {
            return executeWithSession(mapper -> mapper.selectByDetectionDataId(detectionDataId));
        } catch (Exception e) {
            logger.error("根据检测数据ID查询表头失败，ID: {}", detectionDataId, e);
            return null;
        }
    }

}
