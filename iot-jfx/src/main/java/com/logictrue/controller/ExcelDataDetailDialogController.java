package com.logictrue.controller;

import com.logictrue.iot.entity.DeviceDetectionData;
import com.logictrue.service.DatabaseService;
import com.logictrue.service.AutoPushService;
import javafx.application.Platform;
import javafx.beans.property.SimpleStringProperty;
import javafx.concurrent.Task;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备检测数据详情对话框控制器
 */
public class ExcelDataDetailDialogController implements Initializable {
    private static final Logger logger = LoggerFactory.getLogger(ExcelDataDetailDialogController.class);

    @FXML
    private Button closeButton;

    // 基本信息控件
    @FXML
    private Label recordIdLabel;
    @FXML
    private Label deviceCodeLabel;
    @FXML
    private Label templateNameLabel;
    @FXML
    private Label fileNameLabel;
    @FXML
    private Label filePathLabel;
    @FXML
    private Label createTimeLabel;
    @FXML
    private Label totalSheetsLabel;
    @FXML
    private Label parsedSheetsLabel;
    @FXML
    private Label basicFieldsCountLabel;
    @FXML
    private Label tableRowsCountLabel;

    // 推送信息控件
    @FXML
    private Label pushStatusLabel;
    @FXML
    private Label pushTimeLabel;
    @FXML
    private Label pushUrlLabel;
    @FXML
    private Label pushRetryCountLabel;
    @FXML
    private Label pushErrorMessageLabel;
    @FXML
    private Button retryPushButton;
    @FXML
    private Label retryPushStatusLabel;

    // 详情数据控件
    @FXML
    private ComboBox<String> detailDataSheetComboBox;
    @FXML
    private VBox basicFieldsSection;
    @FXML
    private VBox basicFieldsContent;
    @FXML
    private VBox tableDataSection;
    @FXML
    private TableView<Map<String, String>> detailTableDataTable;
    @FXML
    private Label detailTableDataCountLabel;

    private DatabaseService databaseService;
    private DatabaseService.DeviceDetectionDataDetail dataDetail;
    private ObservableList<DatabaseService.BasicFieldData> basicFieldsList;
    private ObservableList<Map<String, String>> tableDataList;
    private ObservableList<Map<String, String>> detailTableDataList;
    private AutoPushService autoPushService;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        databaseService = DatabaseService.getInstance();
        autoPushService = new AutoPushService();
        basicFieldsList = FXCollections.observableArrayList();
        tableDataList = FXCollections.observableArrayList();
        detailTableDataList = FXCollections.observableArrayList();

        // 只初始化事件处理器，不再初始化已移除的基础字段表格
        initializeEventHandlers();

        logger.info("Excel数据详情对话框控制器初始化完成");
    }

    /**
     * 初始化事件处理器
     */
    private void initializeEventHandlers() {
        closeButton.setOnAction(event -> closeDialog());

        // 详情数据Sheet选择事件
        detailDataSheetComboBox.setOnAction(event -> filterDetailDataBySheet());

        // 重新推送按钮事件
        retryPushButton.setOnAction(event -> handleRetryPush());
    }

    /**
     * 创建居中对齐的单元格
     */
    private <T> TableCell<T, String> createCenteredCell() {
        return new TableCell<T, String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(item);
                }
                setAlignment(Pos.CENTER);
            }
        };
    }

    /**
     * 设置数据详情
     */
    public void setDataDetail(DatabaseService.DeviceDetectionDataDetail detail) {
        this.dataDetail = detail;
        if (detail != null) {
            displayBasicInfo(detail.getMainRecord());
            // 只设置详情数据，不再设置已移除的基础字段和表格数据tab
            setupDetailData(detail.getBasicFields(), detail.getTableData());
        }
    }

    /**
     * 显示基本信息
     */
    private void displayBasicInfo(DeviceDetectionData record) {
        if (record == null) return;

        recordIdLabel.setText(record.getId() != null ? record.getId().toString() : "");
        deviceCodeLabel.setText(record.getDeviceCode() != null ? record.getDeviceCode() : "");
        templateNameLabel.setText(record.getTemplateName() != null ? record.getTemplateName() : "");
        fileNameLabel.setText(record.getFileName() != null ? record.getFileName() : "");
        filePathLabel.setText(record.getFilePath() != null ? record.getFilePath() : "");

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        createTimeLabel.setText(record.getCreateTime() != null ? record.getCreateTime().format(formatter) : "");

        totalSheetsLabel.setText(record.getTotalSheets() != null ? record.getTotalSheets().toString() : "0");
        parsedSheetsLabel.setText(record.getParsedSheets() != null ? record.getParsedSheets().toString() : "0");
        basicFieldsCountLabel.setText(record.getBasicFieldsCount() != null ? record.getBasicFieldsCount().toString() : "0");
        tableRowsCountLabel.setText(record.getTableRowsCount() != null ? record.getTableRowsCount().toString() : "0");

        // 显示推送信息
        displayPushInfo(record);
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 获取解析状态文本
     */
    private String getParseStatusText(Integer parseStatus) {
        if (parseStatus == null) return "未知";
        switch (parseStatus) {
            case 0:
                return "待解析";
            case 1:
                return "解析成功";
            case 2:
                return "解析失败";
            default:
                return "未知";
        }
    }

    /**
     * 设置详情数据
     */
    private void setupDetailData(List<DatabaseService.BasicFieldData> basicFields, List<DatabaseService.TableDataInfo> tableData) {
        // 获取所有Sheet
        Set<String> sheets = new LinkedHashSet<>();

        if (basicFields != null) {
            basicFields.stream()
                    .map(DatabaseService.BasicFieldData::getSheetName)
                    .filter(Objects::nonNull)
                    .forEach(sheets::add);
        }

        if (tableData != null) {
            tableData.stream()
                    .map(DatabaseService.TableDataInfo::getSheetName)
                    .filter(Objects::nonNull)
                    .forEach(sheets::add);
        }

        detailDataSheetComboBox.getItems().clear();
        detailDataSheetComboBox.getItems().addAll(sheets);

        if (!sheets.isEmpty()) {
            detailDataSheetComboBox.setValue(sheets.iterator().next());
            filterDetailDataBySheet();
        }
    }

    /**
     * 按Sheet筛选详情数据
     */
    private void filterDetailDataBySheet() {
        if (dataDetail == null) {
            return;
        }

        String selectedSheet = detailDataSheetComboBox.getValue();
        if (selectedSheet == null) {
            return;
        }

        // 显示基础字段
        displayDetailBasicFields(selectedSheet);

        // 显示表格数据
        displayDetailTableData(selectedSheet);
    }

    /**
     * 显示详情基础字段
     */
    private void displayDetailBasicFields(String sheetName) {
        basicFieldsContent.getChildren().clear();

        if (dataDetail.getBasicFields() == null || dataDetail.getBasicFields().isEmpty()) {
            basicFieldsSection.setVisible(false);
            basicFieldsSection.setManaged(false);
            return;
        }

        // 筛选当前Sheet的基础字段
        List<DatabaseService.BasicFieldData> sheetBasicFields = dataDetail.getBasicFields().stream()
                .filter(field -> sheetName.equals(field.getSheetName()))
                .collect(Collectors.toList());

        if (sheetBasicFields.isEmpty()) {
            basicFieldsSection.setVisible(false);
            basicFieldsSection.setManaged(false);
            return;
        }

        basicFieldsSection.setVisible(true);
        basicFieldsSection.setManaged(true);

        // 创建基础字段显示
        for (DatabaseService.BasicFieldData field : sheetBasicFields) {
            HBox fieldBox = new HBox(10);
            fieldBox.setAlignment(Pos.CENTER_LEFT);

            // 字段名称（加粗）
            Label nameLabel = new Label("• " + (field.getFieldName() != null ? field.getFieldName() : field.getFieldCode()) + ":");
            nameLabel.setStyle("-fx-font-weight: bold;");
            nameLabel.setPrefWidth(150);

            // 字段值
            Label valueLabel = new Label(field.getFieldValue() != null ? field.getFieldValue() : "");
            valueLabel.setStyle("-fx-text-fill: #333333;");

            // 位置信息
            String position = "";
            if (field.getRowIndex() != null && field.getColIndex() != null) {
                position = " (" + convertToExcelPosition(field.getValueRowIndex(), field.getValueColIndex()) + ")";
            }
            Label positionLabel = new Label(position);
            positionLabel.setStyle("-fx-text-fill: #666666; -fx-font-size: 11px;");

            fieldBox.getChildren().addAll(nameLabel, valueLabel, positionLabel);
            basicFieldsContent.getChildren().add(fieldBox);
        }
    }

    /**
     * 显示详情表格数据
     */
    private void displayDetailTableData(String sheetName) {
        if (dataDetail.getTableData() == null || dataDetail.getTableData().isEmpty()) {
            tableDataSection.setVisible(false);
            tableDataSection.setManaged(false);
            return;
        }

        // 筛选当前Sheet的数据
        List<DatabaseService.TableDataInfo> sheetData = dataDetail.getTableData().stream()
                .filter(data -> sheetName.equals(data.getSheetName()))
                .collect(Collectors.toList());

        if (sheetData.isEmpty()) {
            tableDataSection.setVisible(false);
            tableDataSection.setManaged(false);
            detailTableDataCountLabel.setText("共 0 行数据");
            return;
        }

        tableDataSection.setVisible(true);
        tableDataSection.setManaged(true);

        // 获取所有字段信息（表头）
        Map<String, String> fieldCodeToNameMap = new LinkedHashMap<>();
        Set<String> fieldCodes = new LinkedHashSet<>();

        for (DatabaseService.TableDataInfo data : sheetData) {
            String fieldCode = data.getFieldCode();
            String fieldName = data.getFieldName();
            if (fieldCode != null) {
                fieldCodes.add(fieldCode);
                // 使用fieldName作为显示名称，如果没有则使用fieldCode
                fieldCodeToNameMap.put(fieldCode, fieldName != null ? fieldName : fieldCode);
            }
        }

        // 按行索引分组数据
        Map<Integer, List<DatabaseService.TableDataInfo>> rowDataMap = sheetData.stream()
                .collect(Collectors.groupingBy(DatabaseService.TableDataInfo::getRowIndex));

        // 清空现有列
        detailTableDataTable.getColumns().clear();

        // 添加序号列
        TableColumn<Map<String, String>, String> rowNumberColumn = new TableColumn<>("序号");
        rowNumberColumn.setPrefWidth(60);
        rowNumberColumn.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().get("_rowNumber")));
        rowNumberColumn.setCellFactory(column -> createCenteredCell());
        detailTableDataTable.getColumns().add(rowNumberColumn);

        // 动态创建列，使用fieldName作为列标题
        for (String fieldCode : fieldCodes) {
            String displayName = fieldCodeToNameMap.get(fieldCode);
            TableColumn<Map<String, String>, String> column = new TableColumn<>(displayName);
            column.setPrefWidth(120);
            column.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().get(fieldCode)));
            column.setCellFactory(col -> createCenteredCell());
            detailTableDataTable.getColumns().add(column);
        }

        // 构建表格数据
        detailTableDataList.clear();
        List<Integer> sortedRows = new ArrayList<>(rowDataMap.keySet());
        sortedRows.sort(Integer::compareTo);

        for (int i = 0; i < sortedRows.size(); i++) {
            Integer rowIndex = sortedRows.get(i);
            List<DatabaseService.TableDataInfo> rowData = rowDataMap.get(rowIndex);

            Map<String, String> rowMap = new HashMap<>();
            rowMap.put("_rowNumber", String.valueOf(i + 1)); // 显示序号从1开始

            for (DatabaseService.TableDataInfo data : rowData) {
                rowMap.put(data.getFieldCode(), data.getFieldValue() != null ? data.getFieldValue() : "");
            }

            detailTableDataList.add(rowMap);
        }

        detailTableDataTable.setItems(detailTableDataList);
        detailTableDataCountLabel.setText("共 " + detailTableDataList.size() + " 行数据");
    }

    /**
     * 将行列索引转换为Excel位置格式（如A1、B1等）
     * @param rowIndex 行索引（从0开始）
     * @param colIndex 列索引（从0开始）
     * @return Excel格式的位置字符串
     */
    private String convertToExcelPosition(int rowIndex, int colIndex) {
        StringBuilder columnName = new StringBuilder();
        int col = colIndex;

        // 将列索引转换为字母
        while (col >= 0) {
            columnName.insert(0, (char) ('A' + (col % 26)));
            col = col / 26 - 1;
        }

        // 行号从1开始
        return columnName.toString() + (rowIndex + 1);
    }

    /**
     * 关闭对话框
     */
    private void closeDialog() {
        Stage stage = (Stage) closeButton.getScene().getWindow();
        stage.close();
    }

    /**
     * 显示推送信息
     */
    private void displayPushInfo(DeviceDetectionData record) {
        if (record == null) return;

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 推送状态
        Integer pushStatus = record.getPushStatus();
        String statusText = getPushStatusText(pushStatus);
        pushStatusLabel.setText(statusText);

        // 根据推送状态设置样式
        pushStatusLabel.getStyleClass().removeAll("push-status-pending", "push-status-success", "push-status-failed");
        if (pushStatus != null) {
            switch (pushStatus) {
                case 0:
                    pushStatusLabel.getStyleClass().add("push-status-pending");
                    break;
                case 1:
                    pushStatusLabel.getStyleClass().add("push-status-success");
                    break;
                case 2:
                    pushStatusLabel.getStyleClass().add("push-status-failed");
                    break;
            }
        }

        // 推送时间
        pushTimeLabel.setText(record.getPushTime() != null ? record.getPushTime().format(formatter) : "未推送");

        // 推送地址
        pushUrlLabel.setText(record.getPushUrl() != null ? record.getPushUrl() : "");

        // 重试次数
        pushRetryCountLabel.setText(record.getPushRetryCount() != null ? record.getPushRetryCount().toString() : "0");

        // 失败原因
        pushErrorMessageLabel.setText(record.getPushErrorMessage() != null ? record.getPushErrorMessage() : "");

        // 重新推送按钮显示逻辑
        boolean showRetryButton = pushStatus != null && pushStatus == 2; // 推送失败时显示
        retryPushButton.setVisible(showRetryButton);
        retryPushButton.setManaged(showRetryButton);

        // 清空状态标签
        retryPushStatusLabel.setText("");
    }

    /**
     * 获取推送状态文本
     */
    private String getPushStatusText(Integer pushStatus) {
        if (pushStatus == null) {
            return "待推送";
        }
        switch (pushStatus) {
            case 0:
                return "待推送";
            case 1:
                return "推送成功";
            case 2:
                return "推送失败";
            default:
                return "未知状态";
        }
    }

    /**
     * 处理重新推送
     */
    private void handleRetryPush() {
        if (dataDetail == null || dataDetail.getMainRecord() == null) {
            return;
        }

        Long recordId = dataDetail.getMainRecord().getId();
        if (recordId == null) {
            return;
        }

        // 禁用按钮，显示处理中状态
        retryPushButton.setDisable(true);
        retryPushStatusLabel.setText("正在重新推送...");

        // 异步执行重新推送
        Task<Boolean> retryTask = new Task<Boolean>() {
            @Override
            protected Boolean call() throws Exception {
                return autoPushService.retryPushRecord(recordId).get();
            }
        };

        retryTask.setOnSucceeded(event -> {
            Platform.runLater(() -> {
                Boolean success = retryTask.getValue();
                if (success) {
                    retryPushStatusLabel.setText("重新推送成功");
                    // 刷新推送信息显示
                    refreshPushInfo();
                } else {
                    retryPushStatusLabel.setText("重新推送失败");
                }
                retryPushButton.setDisable(false);
            });
        });

        retryTask.setOnFailed(event -> {
            Platform.runLater(() -> {
                retryPushStatusLabel.setText("重新推送异常");
                retryPushButton.setDisable(false);
                logger.error("重新推送异常", retryTask.getException());
            });
        });

        Thread retryThread = new Thread(retryTask);
        retryThread.setDaemon(true);
        retryThread.start();
    }

    /**
     * 刷新推送信息
     */
    private void refreshPushInfo() {
        if (dataDetail == null || dataDetail.getMainRecord() == null) {
            return;
        }

        Long recordId = dataDetail.getMainRecord().getId();
        if (recordId == null) {
            return;
        }

        // 重新查询记录信息
        Task<DatabaseService.DeviceDetectionDataDetail> refreshTask = new Task<DatabaseService.DeviceDetectionDataDetail>() {
            @Override
            protected DatabaseService.DeviceDetectionDataDetail call() throws Exception {
                return databaseService.getExcelDataDetail(recordId);
            }
        };

        refreshTask.setOnSucceeded(event -> {
            Platform.runLater(() -> {
                DatabaseService.DeviceDetectionDataDetail refreshedDetail = refreshTask.getValue();
                if (refreshedDetail != null) {
                    displayPushInfo(refreshedDetail.getMainRecord());
                }
            });
        });

        refreshTask.setOnFailed(event -> {
            logger.error("刷新推送信息失败", refreshTask.getException());
        });

        Thread refreshThread = new Thread(refreshTask);
        refreshThread.setDaemon(true);
        refreshThread.start();
    }
}
