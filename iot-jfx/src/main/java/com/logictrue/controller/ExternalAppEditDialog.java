package com.logictrue.controller;

import com.logictrue.model.ExternalApp;
import com.logictrue.service.ExternalAppService;
import com.logictrue.util.CustomTitleBarUtil;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.FileChooser;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.stage.StageStyle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.UUID;

/**
 * 外部应用程序编辑对话框
 */
public class ExternalAppEditDialog {
    private static final Logger logger = LoggerFactory.getLogger(ExternalAppEditDialog.class);

    private Stage dialogStage;
    private ExternalApp externalApp;
    private boolean confirmed = false;
    private ExternalAppService appService;

    // UI组件
    private TextField nameField;
    private TextField pathField;
    private Button browseButton;
    private TextField argumentsField;
    private TextField workingDirField;
    private Button browseDirButton;
    private TextArea descriptionArea;
    private Button testButton;

    public ExternalAppEditDialog(ExternalApp externalApp) {
        this.externalApp = externalApp != null ? externalApp : createNewExternalApp();
        this.appService = new ExternalAppService();
        initializeDialog();
    }

    public ExternalAppEditDialog(ExternalApp externalApp, Stage owner) {
        this.externalApp = externalApp != null ? externalApp : createNewExternalApp();
        this.appService = new ExternalAppService();
        initializeDialog();
        if (owner != null) {
            dialogStage.initOwner(owner);
        }
    }

    /**
     * 创建新的外部应用程序
     */
    private ExternalApp createNewExternalApp() {
        ExternalApp app = new ExternalApp();
        app.setId(UUID.randomUUID().toString());
        return app;
    }

    /**
     * 初始化对话框
     */
    private void initializeDialog() {
        dialogStage = new Stage();
        String title = externalApp.getName() == null || externalApp.getName().isEmpty() ? "新增应用程序" : "编辑应用程序";

        VBox root = new VBox(15);
        root.setPadding(new Insets(20));

        // 创建表单
        GridPane formGrid = createFormGrid();

        // 创建按钮区域
        HBox buttonBox = createButtonBox();

        root.getChildren().addAll(
            new Label("应用程序配置"),
            formGrid,
            buttonBox
        );

        // 使用自定义标题栏（不显示最大化按钮，因为是小弹窗）
        CustomTitleBarUtil.applyCustomTitleBar(dialogStage, root, title, false, 500, 400);

        dialogStage.initModality(Modality.WINDOW_MODAL);
        dialogStage.setAlwaysOnTop(true);

        // 加载现有数据
        loadExternalAppData();
    }

    /**
     * 创建表单网格
     */
    private GridPane createFormGrid() {
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(10));

        // 设置列约束，确保标签列有足够的宽度
        javafx.scene.layout.ColumnConstraints labelColumn = new javafx.scene.layout.ColumnConstraints();
        labelColumn.setMinWidth(80);
        labelColumn.setPrefWidth(100);
        labelColumn.setHgrow(javafx.scene.layout.Priority.NEVER);

        javafx.scene.layout.ColumnConstraints fieldColumn = new javafx.scene.layout.ColumnConstraints();
        fieldColumn.setHgrow(javafx.scene.layout.Priority.ALWAYS);

        grid.getColumnConstraints().addAll(labelColumn, fieldColumn);

        int row = 0;

        // 应用程序名称
        Label nameLabel = new Label("应用名称:");
        nameLabel.setMinWidth(80);
        grid.add(nameLabel, 0, row);
        nameField = new TextField();
        nameField.setPromptText("请输入应用程序显示名称");
        grid.add(nameField, 1, row++);

        // 应用程序路径
        Label pathLabel = new Label("程序路径:");
        pathLabel.setMinWidth(80);
        grid.add(pathLabel, 0, row);
        HBox pathBox = new HBox(5);
        pathField = new TextField();
        pathField.setPromptText("请选择应用程序可执行文件");
        pathField.setPrefWidth(250);
        browseButton = new Button("浏览...");
        browseButton.setOnAction(event -> browseForApplication());
        pathBox.getChildren().addAll(pathField, browseButton);
        grid.add(pathBox, 1, row++);

        // 启动参数
        Label argsLabel = new Label("启动参数:");
        argsLabel.setMinWidth(80);
        grid.add(argsLabel, 0, row);
        argumentsField = new TextField();
        argumentsField.setPromptText("可选，应用程序启动参数");
        grid.add(argumentsField, 1, row++);

        // 工作目录
        Label workDirLabel = new Label("工作目录:");
        workDirLabel.setMinWidth(80);
        grid.add(workDirLabel, 0, row);
        HBox dirBox = new HBox(5);
        workingDirField = new TextField();
        workingDirField.setPromptText("可选，应用程序工作目录");
        workingDirField.setPrefWidth(250);
        browseDirButton = new Button("浏览...");
        browseDirButton.setOnAction(event -> browseForDirectory());
        dirBox.getChildren().addAll(workingDirField, browseDirButton);
        grid.add(dirBox, 1, row++);

        // 应用描述
        Label descLabel = new Label("应用描述:");
        descLabel.setMinWidth(80);
        descLabel.setAlignment(javafx.geometry.Pos.TOP_LEFT);
        grid.add(descLabel, 0, row);
        descriptionArea = new TextArea();
        descriptionArea.setPromptText("可选，应用程序描述信息");
        descriptionArea.setPrefRowCount(3);
        descriptionArea.setWrapText(true);
        grid.add(descriptionArea, 1, row++);

        // 测试按钮
        testButton = new Button("测试启动");
        testButton.setOnAction(event -> testApplication());
        grid.add(testButton, 1, row++);

        return grid;
    }

    /**
     * 创建按钮区域
     */
    private HBox createButtonBox() {
        HBox buttonBox = new HBox(10);
        buttonBox.setStyle("-fx-alignment: center-right;");

        Button confirmButton = new Button("确定");
        confirmButton.setOnAction(event -> {
            if (validateAndSaveExternalApp()) {
                confirmed = true;
                dialogStage.close();
            }
        });

        Button cancelButton = new Button("取消");
        cancelButton.setOnAction(event -> {
            confirmed = false;
            dialogStage.close();
        });

        buttonBox.getChildren().addAll(confirmButton, cancelButton);
        return buttonBox;
    }

    /**
     * 浏览应用程序文件
     */
    private void browseForApplication() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("选择应用程序");

        // 设置文件过滤器
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            fileChooser.getExtensionFilters().addAll(
                new FileChooser.ExtensionFilter("可执行文件", "*.exe", "*.bat", "*.cmd"),
                new FileChooser.ExtensionFilter("所有文件", "*.*")
            );
        } else {
            fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("所有文件", "*.*")
            );
        }

        File selectedFile = fileChooser.showOpenDialog(dialogStage);
        if (selectedFile != null) {
            pathField.setText(selectedFile.getAbsolutePath());

            // 自动填充应用名称（如果为空）
            if (nameField.getText().trim().isEmpty()) {
                String fileName = selectedFile.getName();
                int dotIndex = fileName.lastIndexOf('.');
                if (dotIndex > 0) {
                    fileName = fileName.substring(0, dotIndex);
                }
                nameField.setText(fileName);
            }

            // 自动设置工作目录（如果为空）
            if (workingDirField.getText().trim().isEmpty()) {
                workingDirField.setText(selectedFile.getParent());
            }
        }
    }

    /**
     * 浏览工作目录
     */
    private void browseForDirectory() {
        javafx.stage.DirectoryChooser directoryChooser = new javafx.stage.DirectoryChooser();
        directoryChooser.setTitle("选择工作目录");

        File selectedDir = directoryChooser.showDialog(dialogStage);
        if (selectedDir != null) {
            workingDirField.setText(selectedDir.getAbsolutePath());
        }
    }

    /**
     * 测试应用程序启动
     */
    private void testApplication() {
        if (!validateFields()) {
            return;
        }

        // 创建临时应用程序对象进行测试
        ExternalApp testApp = new ExternalApp();
        testApp.setName(nameField.getText().trim());
        testApp.setPath(pathField.getText().trim());
        testApp.setArguments(argumentsField.getText() != null ? argumentsField.getText().trim() : null);
        testApp.setWorkingDir(workingDirField.getText() != null ? workingDirField.getText().trim() : null);

        testButton.setDisable(true);
        testButton.setText("测试中...");

        appService.launchApp(testApp).thenAccept(success -> {
            javafx.application.Platform.runLater(() -> {
                testButton.setDisable(false);
                testButton.setText("测试启动");

                Alert alert = new Alert(success ? Alert.AlertType.INFORMATION : Alert.AlertType.ERROR);
                alert.setTitle("测试结果");
                alert.setHeaderText(null);
                alert.setContentText(success ? "应用程序启动成功！" : "应用程序启动失败，请检查路径和参数。");
                alert.initOwner(dialogStage);
                alert.initModality(Modality.WINDOW_MODAL);
                alert.showAndWait();
            });
        });
    }

    /**
     * 加载外部应用程序数据
     */
    private void loadExternalAppData() {
        if (externalApp != null) {
            nameField.setText(externalApp.getName());
            pathField.setText(externalApp.getPath());
            argumentsField.setText(externalApp.getArguments());
            workingDirField.setText(externalApp.getWorkingDir());
            descriptionArea.setText(externalApp.getDescription());
        }
    }

    /**
     * 验证字段
     */
    private boolean validateFields() {
        if (nameField.getText().trim().isEmpty()) {
            showAlert("应用程序名称不能为空");
            return false;
        }

        if (pathField.getText().trim().isEmpty()) {
            showAlert("应用程序路径不能为空");
            return false;
        }

        if (!appService.validateAppPath(pathField.getText().trim())) {
            showAlert("应用程序路径无效或文件不存在");
            return false;
        }

        return true;
    }

    /**
     * 验证并保存外部应用程序数据
     */
    private boolean validateAndSaveExternalApp() {
        if (!validateFields()) {
            return false;
        }

        // 保存数据
        externalApp.setName(nameField.getText().trim());
        externalApp.setPath(pathField.getText().trim());
        externalApp.setArguments(argumentsField.getText() != null ? argumentsField.getText().trim() : null);
        externalApp.setWorkingDir(workingDirField.getText() != null ? workingDirField.getText().trim() : null);
        externalApp.setDescription(descriptionArea.getText() != null ? descriptionArea.getText().trim() : null);

        logger.info("外部应用程序保存成功: {}", externalApp);
        return true;
    }

    /**
     * 显示警告对话框
     */
    private void showAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("输入验证");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.initOwner(dialogStage);
        alert.initModality(Modality.WINDOW_MODAL);
        alert.showAndWait();
    }

    /**
     * 显示对话框并等待结果
     */
    public boolean showAndWait() {
        dialogStage.showAndWait();
        return confirmed;
    }

    /**
     * 获取编辑后的外部应用程序
     */
    public ExternalApp getExternalApp() {
        return externalApp;
    }
}
