<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.mapper.DeviceDetectionBasicFieldMapper">

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into device_detection_basic_field
        (detection_data_id, sheet_id, sheet_name, sheet_index, field_code, field_name, field_type,
        field_value, label_position, value_position, label_row_index, label_col_index,
        value_row_index, value_col_index, sort_order, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.detectionDataId,jdbcType=INTEGER}, #{item.sheetId,jdbcType=LONGVARCHAR},
            #{item.sheetName,jdbcType=LONGVARCHAR}, #{item.sheetIndex,jdbcType=INTEGER}, #{item.fieldCode,jdbcType=LONGVARCHAR},
            #{item.fieldName,jdbcType=LONGVARCHAR}, #{item.fieldType,jdbcType=LONGVARCHAR},
            #{item.fieldValue,jdbcType=LONGVARCHAR}, #{item.labelPosition,jdbcType=LONGVARCHAR},
            #{item.valuePosition,jdbcType=LONGVARCHAR}, #{item.labelRowIndex,jdbcType=INTEGER},
            #{item.labelColIndex,jdbcType=INTEGER}, #{item.valueRowIndex,jdbcType=INTEGER},
            #{item.valueColIndex,jdbcType=INTEGER}, #{item.sortOrder,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

</mapper>
