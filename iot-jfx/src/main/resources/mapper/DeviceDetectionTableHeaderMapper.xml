<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.mapper.DeviceDetectionTableHeaderMapper">

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into device_detection_table_header
        (detection_data_id, sheet_id, sheet_name, sheet_index, header_name, header_code,
        header_position, header_row_index, header_col_index, data_type, column_order, create_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.detectionDataId,jdbcType=INTEGER}, #{item.sheetId,jdbcType=LONGVARCHAR},
            #{item.sheetName,jdbcType=LONGVARCHAR}, #{item.sheetIndex,jdbcType=INTEGER}, #{item.headerName,jdbcType=LONGVARCHAR},
            #{item.headerCode,jdbcType=LONGVARCHAR}, #{item.headerPosition,jdbcType=LONGVARCHAR},
            #{item.headerRowIndex,jdbcType=INTEGER}, #{item.headerColIndex,jdbcType=INTEGER},
            #{item.dataType,jdbcType=LONGVARCHAR}, #{item.columnOrder,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
</mapper>
