<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.mapper.DeviceDetectionTableDataMapper">

    <!-- 批量插入表格数据 -->
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into device_detection_table_data
        (detection_data_id, sheet_id, sheet_name, sheet_index, row_index, row_data, row_order,
        create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.detectionDataId,jdbcType=INTEGER}, #{item.sheetId,jdbcType=LONGVARCHAR},
            #{item.sheetName,jdbcType=LONGVARCHAR}, #{item.sheetIndex,jdbcType=INTEGER}, #{item.rowIndex,jdbcType=INTEGER},
            #{item.rowData,jdbcType=LONGVARCHAR}, #{item.rowOrder,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

</mapper>
