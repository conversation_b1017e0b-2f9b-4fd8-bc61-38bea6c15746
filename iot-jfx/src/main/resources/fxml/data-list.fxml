<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.logictrue.controller.DataListController">
   <children>
      <!-- 标题和操作栏 -->
      <HBox alignment="CENTER_LEFT" spacing="10.0">
         <children>
            <Button fx:id="backButton" mnemonicParsing="false" text="← 返回" />
            <Region HBox.hgrow="ALWAYS" />
            <Label style="-fx-font-size: 18px; -fx-font-weight: bold;" text="Excel数据采集记录" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="refreshButton" mnemonicParsing="false" text="刷新" />
         </children>
         <VBox.margin>
            <Insets bottom="10.0" left="20.0" right="20.0" top="20.0" />
         </VBox.margin>
      </HBox>

      <!-- 搜索和筛选区域 -->
      <HBox alignment="CENTER_LEFT" spacing="10.0">
         <children>
            <Label text="文件名:" />
            <TextField fx:id="fileNameSearchField" promptText="输入文件名搜索" prefWidth="150.0" />
            <Label text="解析状态:" />
            <ComboBox fx:id="parseStatusComboBox" prefWidth="120.0" />
            <Label text="设备编码:" />
            <TextField fx:id="deviceCodeSearchField" promptText="输入设备编码" prefWidth="120.0" />
            <Button fx:id="searchButton" mnemonicParsing="false" text="搜索" />
            <Button fx:id="resetButton" mnemonicParsing="false" text="重置" />
         </children>
         <VBox.margin>
            <Insets bottom="10.0" left="20.0" right="20.0" />
         </VBox.margin>
      </HBox>

      <!-- 表格容器 -->
      <VBox VBox.vgrow="ALWAYS">
         <children>
            <!-- 数据表格 -->
            <TableView fx:id="dataTable" VBox.vgrow="ALWAYS">
               <columns>
                  <TableColumn fx:id="idColumn" prefWidth="60.0" text="ID" style="-fx-alignment: CENTER;" />
                  <TableColumn fx:id="deviceCodeColumn" prefWidth="100.0" text="设备编码" style="-fx-alignment: CENTER;" />
                  <TableColumn fx:id="templateNameColumn" prefWidth="120.0" text="模板名称" style="-fx-alignment: CENTER;" />
                  <TableColumn fx:id="fileNameColumn" prefWidth="150.0" text="文件名" style="-fx-alignment: CENTER;" />
                  <TableColumn fx:id="parseStatusColumn" prefWidth="80.0" text="解析状态" style="-fx-alignment: CENTER;" />
                  <TableColumn fx:id="parseTimeColumn" prefWidth="130.0" text="解析时间" style="-fx-alignment: CENTER;" />
                  <TableColumn fx:id="totalSheetsColumn" prefWidth="80.0" text="总Sheet数" style="-fx-alignment: CENTER;" />
                  <TableColumn fx:id="parsedSheetsColumn" prefWidth="80.0" text="已解析Sheet" style="-fx-alignment: CENTER;" />
                  <TableColumn fx:id="basicFieldsCountColumn" prefWidth="90.0" text="基础字段数" style="-fx-alignment: CENTER;" />
                  <TableColumn fx:id="tableRowsCountColumn" prefWidth="90.0" text="表格行数" style="-fx-alignment: CENTER;" />
                  <TableColumn fx:id="createTimeColumn" prefWidth="130.0" text="创建时间" style="-fx-alignment: CENTER;" />
                  <TableColumn fx:id="actionColumn" prefWidth="100.0" text="操作" style="-fx-alignment: CENTER;" />
               </columns>
               <columnResizePolicy>
                  <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
               </columnResizePolicy>
            </TableView>

            <!-- 分页控件 -->
            <HBox alignment="CENTER" spacing="10.0">
               <children>
                  <Button fx:id="firstPageButton" mnemonicParsing="false" text="首页" />
                  <Button fx:id="prevPageButton" mnemonicParsing="false" text="上一页" />
                  <Label fx:id="pageInfoLabel" text="第 1 页，共 1 页" />
                  <Button fx:id="nextPageButton" mnemonicParsing="false" text="下一页" />
                  <Button fx:id="lastPageButton" mnemonicParsing="false" text="末页" />
                  <Separator orientation="VERTICAL" />
                  <Label text="每页显示:" />
                  <ComboBox fx:id="pageSizeComboBox" prefWidth="80.0" />
                  <Label text="条" />
                  <Separator orientation="VERTICAL" />
                  <Label fx:id="totalCountLabel" text="共 0 条记录" />
               </children>
               <VBox.margin>
                  <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
               </VBox.margin>
            </HBox>
         </children>
         <VBox.margin>
            <Insets left="20.0" right="20.0" />
         </VBox.margin>
      </VBox>

      <!-- 状态栏 -->
      <HBox alignment="CENTER_LEFT" spacing="10.0">
         <children>
            <ProgressIndicator fx:id="progressIndicator" prefHeight="20.0" prefWidth="20.0" visible="false" />
            <Label fx:id="statusLabel" text="就绪" />
         </children>
         <VBox.margin>
            <Insets bottom="10.0" left="20.0" right="20.0" />
         </VBox.margin>
      </HBox>
   </children>
</VBox>
