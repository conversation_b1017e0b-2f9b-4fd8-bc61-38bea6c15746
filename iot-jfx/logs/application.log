2025-08-14 17:44:35.766 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-14 17:44:36.223 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件保存成功: /home/<USER>/nl-mes/iot-nl/iot-jfx/build/libs/config.json
2025-08-14 17:44:36.225 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 创建默认配置文件
2025-08-14 17:44:36.426 [JavaFX Application Thread] INFO  c.logictrue.service.NetworkService - 创建图片目录成功: /home/<USER>/nl-mes/iot-nl/iot-jfx/build/libs/images
2025-08-14 17:44:36.431 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - 配置SQLite数据源，路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/build/libs/data_iot.db
2025-08-14 17:44:36.443 [JavaFX Application Thread] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-14 17:44:36.600 [JavaFX Application Thread] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.sqlite.jdbc4.JDBC4Connection@635c47d8
2025-08-14 17:44:36.602 [JavaFX Application Thread] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-14 17:44:36.925 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - 成功加载XML映射文件: mapper/DeviceDetectionBasicFieldMapper.xml
2025-08-14 17:44:36.932 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - 成功加载XML映射文件: mapper/DeviceDetectionTableHeaderMapper.xml
2025-08-14 17:44:36.938 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - 成功加载XML映射文件: mapper/DeviceDetectionTableDataMapper.xml
2025-08-14 17:44:36.946 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - MyBatis-Plus初始化成功
2025-08-14 17:44:36.953 [JavaFX Application Thread] INFO  c.logictrue.service.TemplateService - 模板存储目录路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/build/libs/templates
2025-08-14 17:44:36.954 [JavaFX Application Thread] INFO  c.logictrue.service.TemplateService - 模板存储目录不存在，开始创建: /home/<USER>/nl-mes/iot-nl/iot-jfx/build/libs/templates
2025-08-14 17:44:36.954 [JavaFX Application Thread] INFO  c.logictrue.service.TemplateService - 模板存储目录创建成功: /home/<USER>/nl-mes/iot-nl/iot-jfx/build/libs/templates
2025-08-14 17:44:36.956 [JavaFX Application Thread] INFO  c.logictrue.service.TemplateService - 模板存储目录路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/build/libs/templates
2025-08-14 17:44:36.962 [JavaFX Application Thread] INFO  c.l.controller.MainController - 背景图片路径为空，使用默认背景图片
2025-08-14 17:44:37.011 [ForkJoinPool.commonPool-worker-1] ERROR c.logictrue.service.NetworkService - 心跳请求异常
2025-08-14 17:44:37.045 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载默认背景图片成功
2025-08-14 17:44:37.045 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载外部应用程序按钮完成，共0个应用
2025-08-14 17:44:37.045 [JavaFX Application Thread] INFO  c.l.controller.MainController - 设置表单页面顶部对齐
2025-08-14 17:44:37.046 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共6个
2025-08-14 17:44:37.069 [JavaFX Application Thread] INFO  c.l.controller.MainController - 生成动态表单完成: 字段数=6, 文本区域数=1, 总行数=3
2025-08-14 17:44:37.069 [JavaFX Application Thread] INFO  c.l.controller.MainController - 调用adjustMainFormHeight，当前ScrollPane: ScrollPane[id=formScrollPane, styleClass=scroll-pane]
2025-08-14 17:44:37.071 [JavaFX Application Thread] INFO  com.logictrue.util.FormLayoutUtil - 动态调整表单高度: 字段数=6, 文本区域数=1, 总行数=3, 计算高度=220.0, 最终高度=220.0
2025-08-14 17:44:37.073 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主页标题初始化完成: 欢迎使用IoT数据采集系统, 字体大小: 24.0, 颜色: #333333, 顶部边距: 50.0
2025-08-14 17:44:37.074 [JavaFX Application Thread] INFO  c.l.controller.MainController - 已注册背景图片更新事件监听器
2025-08-14 17:44:37.075 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-14 17:44:37.077 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始动态创建数据库表结构
2025-08-14 17:44:37.079 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionData
2025-08-14 17:44:37.080 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionData
2025-08-14 17:44:37.081 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_data, 字段数=27
2025-08-14 17:44:37.087 [JavaFX Application Thread] INFO  c.l.service.DynamicSqlGenerator - 开始生成CREATE TABLE语句，表名: device_detection_data
2025-08-14 17:44:37.088 [JavaFX Application Thread] INFO  c.l.service.DynamicSqlGenerator - CREATE TABLE语句生成完成: CREATE TABLE IF NOT EXISTS device_detection_data (id INTEGER PRIMARY KEY AUTOINCREMENT, device_code TEXT, template_id INTEGER, template_code TEXT, template_name TEXT, file_name TEXT, file_path TEXT, collect_path TEXT, file_size INTEGER, parse_status INTEGER, parse_message TEXT, parse_time TIMESTAMP, total_sheets INTEGER, parsed_sheets INTEGER, basic_fields_count INTEGER, table_rows_count INTEGER, create_by TEXT, create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, update_by TEXT, update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, remark TEXT, collect_data TEXT, push_time TIMESTAMP, push_url TEXT, push_retry_count INTEGER, push_status INTEGER, push_error_message TEXT)
2025-08-14 17:44:37.098 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_data 创建成功
2025-08-14 17:44:37.100 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 为表 device_detection_data 创建了 11 个索引
2025-08-14 17:44:37.100 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionBasicField
2025-08-14 17:44:37.100 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionBasicField
2025-08-14 17:44:37.100 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_basic_field, 字段数=17
2025-08-14 17:44:37.101 [JavaFX Application Thread] INFO  c.l.service.DynamicSqlGenerator - 开始生成CREATE TABLE语句，表名: device_detection_basic_field
2025-08-14 17:44:37.101 [JavaFX Application Thread] INFO  c.l.service.DynamicSqlGenerator - CREATE TABLE语句生成完成: CREATE TABLE IF NOT EXISTS device_detection_basic_field (id INTEGER PRIMARY KEY AUTOINCREMENT, detection_data_id INTEGER, sheet_id TEXT, sheet_name TEXT, sheet_index INTEGER, field_code TEXT, field_name TEXT, field_type TEXT, field_value TEXT, label_position TEXT, value_position TEXT, label_row_index INTEGER, label_col_index INTEGER, value_row_index INTEGER, value_col_index INTEGER, sort_order INTEGER, create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
2025-08-14 17:44:37.101 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_basic_field 创建成功
2025-08-14 17:44:37.103 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 为表 device_detection_basic_field 创建了 7 个索引
2025-08-14 17:44:37.103 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionTableHeader
2025-08-14 17:44:37.103 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionTableHeader
2025-08-14 17:44:37.103 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_table_header, 字段数=13
2025-08-14 17:44:37.103 [JavaFX Application Thread] INFO  c.l.service.DynamicSqlGenerator - 开始生成CREATE TABLE语句，表名: device_detection_table_header
2025-08-14 17:44:37.104 [JavaFX Application Thread] INFO  c.l.service.DynamicSqlGenerator - CREATE TABLE语句生成完成: CREATE TABLE IF NOT EXISTS device_detection_table_header (id INTEGER PRIMARY KEY AUTOINCREMENT, detection_data_id INTEGER, sheet_id TEXT, sheet_name TEXT, sheet_index INTEGER, header_name TEXT, header_code TEXT, header_position TEXT, header_row_index INTEGER, header_col_index INTEGER, data_type TEXT, column_order INTEGER, create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
2025-08-14 17:44:37.104 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_table_header 创建成功
2025-08-14 17:44:37.105 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 为表 device_detection_table_header 创建了 7 个索引
2025-08-14 17:44:37.105 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionTableData
2025-08-14 17:44:37.105 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionTableData
2025-08-14 17:44:37.106 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_table_data, 字段数=9
2025-08-14 17:44:37.106 [JavaFX Application Thread] INFO  c.l.service.DynamicSqlGenerator - 开始生成CREATE TABLE语句，表名: device_detection_table_data
2025-08-14 17:44:37.106 [JavaFX Application Thread] INFO  c.l.service.DynamicSqlGenerator - CREATE TABLE语句生成完成: CREATE TABLE IF NOT EXISTS device_detection_table_data (id INTEGER PRIMARY KEY AUTOINCREMENT, detection_data_id INTEGER, sheet_id TEXT, sheet_name TEXT, sheet_index INTEGER, row_index INTEGER, row_data TEXT, row_order INTEGER, create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
2025-08-14 17:44:37.106 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_table_data 创建成功
2025-08-14 17:44:37.107 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 为表 device_detection_table_data 创建了 4 个索引
2025-08-14 17:44:37.108 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 动态表创建完成，共处理 4 个实体类
2025-08-14 17:44:37.115 [JavaFX Application Thread] INFO  c.l.s.DatabaseInitializationService - 使用动态表创建服务初始化数据库表结构成功
2025-08-14 17:44:37.116 [JavaFX Application Thread] INFO  c.logictrue.service.DatabaseService - 数据库服务基础初始化完成，数据库路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/build/libs/data_iot.db
2025-08-14 17:44:37.573 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-14 17:44:37.599 [JavaFX Application Thread] INFO  com.logictrue.util.FormLayoutUtil - ScrollPane高度设置完成: prefHeight=220.0, minHeight=220.0, maxHeight=220.0, 计算高度=220.0
2025-08-14 17:44:37.600 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共6个
2025-08-14 17:44:37.600 [JavaFX Application Thread] INFO  c.l.controller.MainController - 刷新表单布局: 字段数量=6
2025-08-14 17:44:37.600 [JavaFX Application Thread] INFO  com.logictrue.util.FormLayoutUtil - 动态调整表单高度: 字段数=6, 文本区域数=1, 总行数=3, 计算高度=220.0, 最终高度=220.0
2025-08-14 17:44:37.600 [JavaFX Application Thread] INFO  c.l.controller.MainController - 刷新表单布局完成
2025-08-14 17:44:37.607 [JavaFX Application Thread] INFO  c.l.controller.MainController - 已设置窗口大小变化监听器
2025-08-14 17:44:37.645 [JavaFX Application Thread] INFO  com.logictrue.util.FormLayoutUtil - ScrollPane高度设置完成: prefHeight=220.0, minHeight=220.0, maxHeight=220.0, 计算高度=220.0
2025-08-14 17:44:37.646 [JavaFX Application Thread] INFO  c.l.controller.MainController - 当前ScrollPane高度设置: prefHeight=220.0, minHeight=220.0, maxHeight=220.0
2025-08-14 17:44:39.588 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏按钮Action事件被触发
2025-08-14 17:44:39.589 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏模式
2025-08-14 17:44:39.604 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共6个
2025-08-14 17:44:39.604 [JavaFX Application Thread] INFO  c.l.controller.MainController - 刷新表单布局: 字段数量=6
2025-08-14 17:44:39.604 [JavaFX Application Thread] INFO  com.logictrue.util.FormLayoutUtil - 动态调整表单高度: 字段数=6, 文本区域数=1, 总行数=3, 计算高度=220.0, 最终高度=220.0
2025-08-14 17:44:39.604 [JavaFX Application Thread] INFO  c.l.controller.MainController - 刷新表单布局完成
2025-08-14 17:44:39.605 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共6个
2025-08-14 17:44:39.605 [JavaFX Application Thread] INFO  c.l.controller.MainController - 刷新表单布局: 字段数量=6
2025-08-14 17:44:39.605 [JavaFX Application Thread] INFO  com.logictrue.util.FormLayoutUtil - 动态调整表单高度: 字段数=6, 文本区域数=1, 总行数=3, 计算高度=220.0, 最终高度=220.0
2025-08-14 17:44:39.605 [JavaFX Application Thread] INFO  c.l.controller.MainController - 刷新表单布局完成
2025-08-14 17:44:39.609 [JavaFX Application Thread] INFO  com.logictrue.util.FormLayoutUtil - ScrollPane高度设置完成: prefHeight=220.0, minHeight=220.0, maxHeight=220.0, 计算高度=220.0
2025-08-14 17:44:39.610 [JavaFX Application Thread] INFO  c.l.controller.MainController - 当前ScrollPane高度设置: prefHeight=220.0, minHeight=220.0, maxHeight=220.0
2025-08-14 17:44:39.610 [JavaFX Application Thread] INFO  com.logictrue.util.FormLayoutUtil - ScrollPane高度设置完成: prefHeight=220.0, minHeight=220.0, maxHeight=220.0, 计算高度=220.0
2025-08-14 17:44:39.610 [JavaFX Application Thread] INFO  c.l.controller.MainController - 当前ScrollPane高度设置: prefHeight=220.0, minHeight=220.0, maxHeight=220.0
2025-08-14 17:44:55.596 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-14 17:44:55.597 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
