2025-08-12 13:53:27.454 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-12 13:53:27.623 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 应用程序目录 (开发模式): /home/<USER>/nl-mes/iot-nl/iot-jfx
2025-08-12 13:53:27.759 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件加载成功
2025-08-12 13:53:28.008 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载默认背景图片成功
2025-08-12 13:53:28.009 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载外部应用程序按钮完成，共1个应用
2025-08-12 13:53:28.010 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-12 13:53:28.011 [JavaFX Application Thread] INFO  c.logictrue.service.DatabaseService - 数据库服务基础初始化完成，数据库路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/data_iot.db
2025-08-12 13:53:28.013 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - 配置SQLite数据源，路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/data_iot.db
2025-08-12 13:53:28.015 [JavaFX Application Thread] ERROR com.zaxxer.hikari.HikariConfig - Failed to load driver class org.sqlite.JDBC from HikariConfig class classloader jdk.internal.loader.ClassLoaders$AppClassLoader@1bc6a36e
2025-08-12 13:53:28.015 [JavaFX Application Thread] ERROR c.logictrue.config.MyBatisPlusConfig - MyBatis-Plus初始化失败
java.lang.RuntimeException: Failed to load driver class org.sqlite.JDBC in either of HikariConfig class loader or Thread context classloader
	at com.logictrue.merged.module@1.0/com.zaxxer.hikari.HikariConfig.setDriverClassName(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.createDataSource(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.initializeMyBatisPlus(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseService.initDatabase(Unknown Source)
	at com.logictrue@1.0/com.logictrue.controller.MainController.initialize(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.load(Unknown Source)
	at com.logictrue@1.0/com.logictrue.App.start(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(Unknown Source)
	at java.base/java.security.AccessController.doPrivileged(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
2025-08-12 13:53:28.016 [JavaFX Application Thread] ERROR com.logictrue.App - 启动应用程序失败
javafx.fxml.LoadException: 
/com.logictrue/fxml/main.fxml

	at javafx.fxml@21/javafx.fxml.FXMLLoader.constructLoadException(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.load(Unknown Source)
	at com.logictrue@1.0/com.logictrue.App.start(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(Unknown Source)
	at java.base/java.security.AccessController.doPrivileged(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
Caused by: java.lang.RuntimeException: MyBatis-Plus初始化失败
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.initializeMyBatisPlus(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseService.initDatabase(Unknown Source)
	at com.logictrue@1.0/com.logictrue.controller.MainController.initialize(Unknown Source)
	... 13 common frames omitted
Caused by: java.lang.RuntimeException: Failed to load driver class org.sqlite.JDBC in either of HikariConfig class loader or Thread context classloader
	at com.logictrue.merged.module@1.0/com.zaxxer.hikari.HikariConfig.setDriverClassName(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.createDataSource(Unknown Source)
	... 20 common frames omitted
2025-08-12 13:55:21.791 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-12 13:55:21.968 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 应用程序目录 (开发模式): /home/<USER>/nl-mes/iot-nl/iot-jfx
2025-08-12 13:55:22.098 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件加载成功
2025-08-12 13:55:22.329 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载默认背景图片成功
2025-08-12 13:55:22.330 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载外部应用程序按钮完成，共1个应用
2025-08-12 13:55:22.331 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-12 13:55:22.332 [JavaFX Application Thread] INFO  c.logictrue.service.DatabaseService - 数据库服务基础初始化完成，数据库路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/data_iot.db
2025-08-12 13:55:22.335 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - 配置SQLite数据源，路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/data_iot.db
2025-08-12 13:55:22.347 [JavaFX Application Thread] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-12 13:55:22.470 [JavaFX Application Thread] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.sqlite.jdbc4.JDBC4Connection@552af74b
2025-08-12 13:55:22.471 [JavaFX Application Thread] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-12 13:55:22.715 [JavaFX Application Thread] ERROR c.logictrue.config.MyBatisPlusConfig - 加载XML映射文件失败: mapper/DeviceDetectionBasicFieldMapper.xml
java.io.IOException: Could not find resource mapper/DeviceDetectionBasicFieldMapper.xml
	at com.logictrue.merged.module@1.0/org.apache.ibatis.io.Resources.getResourceAsStream(Unknown Source)
	at com.logictrue.merged.module@1.0/org.apache.ibatis.io.Resources.getResourceAsStream(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.loadXmlMappers(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.createSqlSessionFactory(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.initializeMyBatisPlus(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseService.initDatabase(Unknown Source)
	at com.logictrue@1.0/com.logictrue.controller.MainController.initialize(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.load(Unknown Source)
	at com.logictrue@1.0/com.logictrue.App.start(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(Unknown Source)
	at java.base/java.security.AccessController.doPrivileged(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
2025-08-12 13:55:22.717 [JavaFX Application Thread] ERROR c.logictrue.config.MyBatisPlusConfig - 加载XML映射文件失败: mapper/DeviceDetectionTableHeaderMapper.xml
java.io.IOException: Could not find resource mapper/DeviceDetectionTableHeaderMapper.xml
	at com.logictrue.merged.module@1.0/org.apache.ibatis.io.Resources.getResourceAsStream(Unknown Source)
	at com.logictrue.merged.module@1.0/org.apache.ibatis.io.Resources.getResourceAsStream(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.loadXmlMappers(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.createSqlSessionFactory(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.initializeMyBatisPlus(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseService.initDatabase(Unknown Source)
	at com.logictrue@1.0/com.logictrue.controller.MainController.initialize(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.load(Unknown Source)
	at com.logictrue@1.0/com.logictrue.App.start(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(Unknown Source)
	at java.base/java.security.AccessController.doPrivileged(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
2025-08-12 13:55:22.718 [JavaFX Application Thread] ERROR c.logictrue.config.MyBatisPlusConfig - 加载XML映射文件失败: mapper/DeviceDetectionTableDataMapper.xml
java.io.IOException: Could not find resource mapper/DeviceDetectionTableDataMapper.xml
	at com.logictrue.merged.module@1.0/org.apache.ibatis.io.Resources.getResourceAsStream(Unknown Source)
	at com.logictrue.merged.module@1.0/org.apache.ibatis.io.Resources.getResourceAsStream(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.loadXmlMappers(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.createSqlSessionFactory(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.initializeMyBatisPlus(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.<init>(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseInitializationService.getInstance(Unknown Source)
	at com.logictrue@1.0/com.logictrue.service.DatabaseService.initDatabase(Unknown Source)
	at com.logictrue@1.0/com.logictrue.controller.MainController.initialize(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.loadImpl(Unknown Source)
	at javafx.fxml@21/javafx.fxml.FXMLLoader.load(Unknown Source)
	at com.logictrue@1.0/com.logictrue.App.start(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(Unknown Source)
	at java.base/java.security.AccessController.doPrivileged(Unknown Source)
	at javafx.graphics@21/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(Unknown Source)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
2025-08-12 13:55:22.729 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - MyBatis-Plus初始化成功
2025-08-12 13:55:22.730 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始动态创建数据库表结构
2025-08-12 13:55:22.734 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionData
2025-08-12 13:55:22.735 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionData
2025-08-12 13:55:22.737 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_data, 字段数=20
2025-08-12 13:55:22.742 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_data 已存在，跳过创建
2025-08-12 13:55:22.742 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionBasicField
2025-08-12 13:55:22.742 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionBasicField
2025-08-12 13:55:22.742 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_basic_field, 字段数=17
2025-08-12 13:55:22.742 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_basic_field 已存在，跳过创建
2025-08-12 13:55:22.742 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionTableHeader
2025-08-12 13:55:22.743 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionTableHeader
2025-08-12 13:55:22.743 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_table_header, 字段数=13
2025-08-12 13:55:22.743 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_table_header 已存在，跳过创建
2025-08-12 13:55:22.743 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionTableData
2025-08-12 13:55:22.743 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionTableData
2025-08-12 13:55:22.743 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_table_data, 字段数=9
2025-08-12 13:55:22.743 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_table_data 已存在，跳过创建
2025-08-12 13:55:22.744 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 动态表创建完成，共处理 4 个实体类
2025-08-12 13:55:22.750 [JavaFX Application Thread] INFO  c.l.s.DatabaseInitializationService - 使用动态表创建服务初始化数据库表结构成功
2025-08-12 13:55:22.751 [JavaFX Application Thread] INFO  c.logictrue.service.DatabaseService - 数据库表结构初始化完成
2025-08-12 13:55:23.029 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-12 13:55:24.784 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏按钮Action事件被触发
2025-08-12 13:55:24.785 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏模式
2025-08-12 13:55:35.745 [JavaFX Application Thread] INFO  c.l.controller.MainController - 快速开始按钮Action事件被触发
2025-08-12 13:55:35.745 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到表单页面
2025-08-12 13:55:35.746 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共2个
2025-08-12 13:55:37.334 [JavaFX Application Thread] INFO  c.l.controller.MainController - 数据采集按钮Action事件被触发
2025-08-12 13:55:37.335 [JavaFX Application Thread] INFO  c.l.controller.MainController - 开始执行数据采集
2025-08-12 13:55:40.544 [JavaFX Application Thread] INFO  c.l.controller.MainController - 返回主页面按钮Action事件被触发
2025-08-12 13:55:40.544 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到主页面
2025-08-12 13:55:43.693 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-12 13:55:43.693 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
2025-08-12 14:04:15.833 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-12 14:04:16.005 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 应用程序目录 (开发模式): /home/<USER>/nl-mes/iot-nl/iot-jfx
2025-08-12 14:04:16.134 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件加载成功
2025-08-12 14:04:16.377 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载默认背景图片成功
2025-08-12 14:04:16.378 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载外部应用程序按钮完成，共1个应用
2025-08-12 14:04:16.379 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-12 14:04:16.381 [JavaFX Application Thread] INFO  c.logictrue.service.DatabaseService - 数据库服务基础初始化完成，数据库路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/data_iot.db
2025-08-12 14:04:16.385 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - 配置SQLite数据源，路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/data_iot.db
2025-08-12 14:04:16.397 [JavaFX Application Thread] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-12 14:04:16.523 [JavaFX Application Thread] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.sqlite.jdbc4.JDBC4Connection@552af74b
2025-08-12 14:04:16.525 [JavaFX Application Thread] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-12 14:04:16.778 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - 成功加载XML映射文件: mapper/DeviceDetectionBasicFieldMapper.xml
2025-08-12 14:04:16.784 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - 成功加载XML映射文件: mapper/DeviceDetectionTableHeaderMapper.xml
2025-08-12 14:04:16.793 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - 成功加载XML映射文件: mapper/DeviceDetectionTableDataMapper.xml
2025-08-12 14:04:16.800 [JavaFX Application Thread] INFO  c.logictrue.config.MyBatisPlusConfig - MyBatis-Plus初始化成功
2025-08-12 14:04:16.801 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始动态创建数据库表结构
2025-08-12 14:04:16.803 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionData
2025-08-12 14:04:16.804 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionData
2025-08-12 14:04:16.805 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_data, 字段数=20
2025-08-12 14:04:16.809 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_data 已存在，跳过创建
2025-08-12 14:04:16.810 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionBasicField
2025-08-12 14:04:16.810 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionBasicField
2025-08-12 14:04:16.810 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_basic_field, 字段数=17
2025-08-12 14:04:16.810 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_basic_field 已存在，跳过创建
2025-08-12 14:04:16.810 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionTableHeader
2025-08-12 14:04:16.810 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionTableHeader
2025-08-12 14:04:16.811 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_table_header, 字段数=13
2025-08-12 14:04:16.811 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_table_header 已存在，跳过创建
2025-08-12 14:04:16.811 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionTableData
2025-08-12 14:04:16.811 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionTableData
2025-08-12 14:04:16.811 [JavaFX Application Thread] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_table_data, 字段数=9
2025-08-12 14:04:16.811 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_table_data 已存在，跳过创建
2025-08-12 14:04:16.812 [JavaFX Application Thread] INFO  c.l.s.DynamicTableCreationService - 动态表创建完成，共处理 4 个实体类
2025-08-12 14:04:16.817 [JavaFX Application Thread] INFO  c.l.s.DatabaseInitializationService - 使用动态表创建服务初始化数据库表结构成功
2025-08-12 14:04:16.818 [JavaFX Application Thread] INFO  c.logictrue.service.DatabaseService - 数据库表结构初始化完成
2025-08-12 14:04:17.061 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-12 14:04:18.936 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏按钮Action事件被触发
2025-08-12 14:04:18.936 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏模式
2025-08-12 14:04:44.100 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-12 14:04:44.101 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
2025-08-12 16:18:02.057 [main] INFO  c.logictrue.test.MinimalMapperTest - 开始最简化Mapper批量插入测试
2025-08-12 16:18:02.058 [main] INFO  c.logictrue.test.MinimalMapperTest - 正在初始化MyBatis-Plus配置...
2025-08-12 16:18:02.062 [main] INFO  c.logictrue.config.MyBatisPlusConfig - 配置SQLite数据源，路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/data_iot.db
2025-08-12 16:18:02.078 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-12 16:18:02.206 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.sqlite.jdbc4.JDBC4Connection@2de23121
2025-08-12 16:18:02.208 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-12 16:18:02.441 [main] INFO  c.logictrue.config.MyBatisPlusConfig - 成功加载XML映射文件: mapper/DeviceDetectionBasicFieldMapper.xml
2025-08-12 16:18:02.447 [main] INFO  c.logictrue.config.MyBatisPlusConfig - 成功加载XML映射文件: mapper/DeviceDetectionTableHeaderMapper.xml
2025-08-12 16:18:02.452 [main] INFO  c.logictrue.config.MyBatisPlusConfig - 成功加载XML映射文件: mapper/DeviceDetectionTableDataMapper.xml
2025-08-12 16:18:02.464 [main] INFO  c.logictrue.config.MyBatisPlusConfig - MyBatis-Plus初始化成功
2025-08-12 16:18:02.464 [main] INFO  c.logictrue.test.MinimalMapperTest - MyBatis-Plus配置初始化成功
2025-08-12 16:18:02.464 [main] INFO  c.logictrue.test.MinimalMapperTest - 正在获取SqlSession...
2025-08-12 16:18:02.468 [main] INFO  c.logictrue.test.MinimalMapperTest - SqlSession获取成功
2025-08-12 16:18:02.474 [main] INFO  c.logictrue.test.MinimalMapperTest - Mapper实例获取成功
2025-08-12 16:18:02.474 [main] INFO  c.logictrue.test.MinimalMapperTest - === 测试DeviceDetectionBasicFieldMapper.batchInsert(空列表) ===
2025-08-12 16:18:02.497 [main] ERROR c.logictrue.test.MinimalMapperTest - 测试执行失败
org.apache.ibatis.exceptions.PersistenceException: 
### Error updating database.  Cause: org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (incomplete input)
### The error may exist in mapper/DeviceDetectionBasicFieldMapper.xml
### The error may involve com.logictrue.mapper.DeviceDetectionBasicFieldMapper.batchInsert
### The error occurred while executing an update
### SQL: insert into device_detection_basic_field         (detection_data_id, sheet_id, sheet_name, sheet_index, field_code, field_name, field_type,         field_value, label_position, value_position, label_row_index, label_col_index,         value_row_index, value_col_index, sort_order, create_time)         values
### Cause: org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (incomplete input)
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:199)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:60)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy14.batchInsert(Unknown Source)
	at com.logictrue.test.MinimalMapperTest.main(MinimalMapperTest.java:48)
Caused by: org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (incomplete input)
	at org.sqlite.core.DB.newSQLException(DB.java:1179)
	at org.sqlite.core.DB.newSQLException(DB.java:1190)
	at org.sqlite.core.DB.throwex(DB.java:1150)
	at org.sqlite.core.NativeDB.prepare_utf8(Native Method)
	at org.sqlite.core.NativeDB.prepare(NativeDB.java:126)
	at org.sqlite.core.DB.prepare(DB.java:264)
	at org.sqlite.core.CorePreparedStatement.<init>(CorePreparedStatement.java:46)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.<init>(JDBC3PreparedStatement.java:32)
	at org.sqlite.jdbc4.JDBC4PreparedStatement.<init>(JDBC4PreparedStatement.java:25)
	at org.sqlite.jdbc4.JDBC4Connection.prepareStatement(JDBC4Connection.java:34)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:226)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:206)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:221)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:362)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:83)
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:88)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:59)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.prepareStatement(MybatisSimpleExecutor.java:94)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doUpdate(MybatisSimpleExecutor.java:55)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.update(MybatisCachingExecutor.java:85)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	... 6 common frames omitted
2025-08-12 16:18:02.499 [main] ERROR c.logictrue.test.MinimalMapperTest - 可能的原因：
2025-08-12 16:18:02.499 [main] ERROR c.logictrue.test.MinimalMapperTest - 1. MyBatis-Plus配置问题
2025-08-12 16:18:02.499 [main] ERROR c.logictrue.test.MinimalMapperTest - 2. XML映射文件加载失败
2025-08-12 16:18:02.499 [main] ERROR c.logictrue.test.MinimalMapperTest - 3. 实体类或Mapper接口定义问题
2025-08-12 16:18:02.499 [main] ERROR c.logictrue.test.MinimalMapperTest - 4. 数据库连接问题
2025-08-12 16:19:06.220 [main] INFO  c.logictrue.test.MinimalMapperTest - 开始最简化Mapper批量插入测试
2025-08-12 16:19:06.222 [main] INFO  c.logictrue.test.MinimalMapperTest - 正在初始化MyBatis-Plus配置...
2025-08-12 16:19:06.225 [main] INFO  c.logictrue.config.MyBatisPlusConfig - 配置SQLite数据源，路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/data_iot.db
2025-08-12 16:19:06.242 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-12 16:19:06.371 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.sqlite.jdbc4.JDBC4Connection@2de23121
2025-08-12 16:19:06.373 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-12 16:19:06.645 [main] INFO  c.logictrue.config.MyBatisPlusConfig - 成功加载XML映射文件: mapper/DeviceDetectionBasicFieldMapper.xml
2025-08-12 16:19:06.653 [main] INFO  c.logictrue.config.MyBatisPlusConfig - 成功加载XML映射文件: mapper/DeviceDetectionTableHeaderMapper.xml
2025-08-12 16:19:06.661 [main] INFO  c.logictrue.config.MyBatisPlusConfig - 成功加载XML映射文件: mapper/DeviceDetectionTableDataMapper.xml
2025-08-12 16:19:06.672 [main] INFO  c.logictrue.config.MyBatisPlusConfig - MyBatis-Plus初始化成功
2025-08-12 16:19:06.672 [main] INFO  c.logictrue.test.MinimalMapperTest - MyBatis-Plus配置初始化成功
2025-08-12 16:19:06.673 [main] INFO  c.logictrue.test.MinimalMapperTest - 正在获取SqlSession...
2025-08-12 16:19:06.676 [main] INFO  c.logictrue.test.MinimalMapperTest - SqlSession获取成功
2025-08-12 16:19:06.682 [main] INFO  c.logictrue.test.MinimalMapperTest - Mapper实例获取成功
2025-08-12 16:19:06.682 [main] INFO  c.logictrue.test.MinimalMapperTest - === 测试DeviceDetectionBasicFieldMapper.batchInsert(空列表) ===
2025-08-12 16:19:06.682 [main] INFO  c.logictrue.test.MinimalMapperTest - 列表为空，跳过批量插入（这是正确的处理方式）
2025-08-12 16:19:06.683 [main] INFO  c.logictrue.test.MinimalMapperTest - ✓ DeviceDetectionBasicFieldMapper.batchInsert 方法存在且可调用
2025-08-12 16:19:06.683 [main] INFO  c.logictrue.test.MinimalMapperTest - === 测试DeviceDetectionTableHeaderMapper.batchInsert方法存在性 ===
2025-08-12 16:19:06.683 [main] INFO  c.logictrue.test.MinimalMapperTest - ✓ DeviceDetectionTableHeaderMapper.batchInsert 方法存在且可调用
2025-08-12 16:19:06.683 [main] INFO  c.logictrue.test.MinimalMapperTest - === 测试DeviceDetectionTableDataMapper.batchInsert方法存在性 ===
2025-08-12 16:19:06.683 [main] INFO  c.logictrue.test.MinimalMapperTest - ✓ DeviceDetectionTableDataMapper.batchInsert 方法存在且可调用
2025-08-12 16:19:06.683 [main] INFO  c.logictrue.test.MinimalMapperTest - === 测试总结 ===
2025-08-12 16:19:06.683 [main] INFO  c.logictrue.test.MinimalMapperTest - ✓ 所有三个Mapper的batchInsert方法都存在且可调用！
2025-08-12 16:19:06.683 [main] INFO  c.logictrue.test.MinimalMapperTest - ✓ MyBatis-Plus配置正常
2025-08-12 16:19:06.683 [main] INFO  c.logictrue.test.MinimalMapperTest - ✓ XML映射文件加载成功
2025-08-12 16:19:06.683 [main] INFO  c.logictrue.test.MinimalMapperTest - ✓ 数据库连接正常
2025-08-12 16:19:06.683 [main] INFO  c.logictrue.test.MinimalMapperTest - ✓ 方法签名正确
2025-08-12 16:19:06.683 [main] INFO  c.logictrue.test.MinimalMapperTest - 
2025-08-12 16:19:06.683 [main] INFO  c.logictrue.test.MinimalMapperTest - 注意：XML映射文件在处理空列表时会产生SQL语法错误，
2025-08-12 16:19:06.684 [main] INFO  c.logictrue.test.MinimalMapperTest - 这是正常现象，实际使用时应在Service层先检查列表是否为空。
