<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.word.mapper.DrlWorkCheckRecordsMapper">
    <resultMap id="BaseResultMap" type="com.logictrue.word.entity.DrlWorkCheckRecords">
        <!--@Table drl_work_check_records-->
        <id column="record_id" jdbcType="INTEGER" property="recordId"/>
        <result column="task_record_id" jdbcType="INTEGER" property="taskRecordId"/>
        <result column="task_id" jdbcType="INTEGER" property="taskId"/>
        <result column="car_id" jdbcType="INTEGER" property="carId"/>
        <result column="check_id" jdbcType="INTEGER" property="checkId"/>
        <result column="parent_id" jdbcType="INTEGER" property="parentId"/>
        <result column="check_name" jdbcType="LONGVARCHAR" property="checkName"/>
        <result column="check_content" jdbcType="LONGVARCHAR" property="checkContent"/>
        <result column="car_type" jdbcType="INTEGER" property="carType"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="check_level" jdbcType="INTEGER" property="checkLevel"/>
        <result column="team" jdbcType="INTEGER" property="team"/>
        <result column="check_type_id" jdbcType="INTEGER" property="checkTypeId"/>
        <result column="result" jdbcType="LONGVARCHAR" property="result"/>
        <result column="is_required" jdbcType="INTEGER" property="isRequired"/>
        <result column="version_id" jdbcType="INTEGER" property="versionId"/>
        <result column="replace_rules" jdbcType="LONGVARCHAR" property="replaceRules"/>
        <result column="result_template" jdbcType="LONGVARCHAR" property="resultTemplate"/>
        <result column="change_index" jdbcType="LONGVARCHAR" property="changeIndex"/>
        <result column="is_pass" jdbcType="BIT" property="isPass"/>
        <result column="result_copy" jdbcType="LONGVARCHAR" property="resultCopy"/>
        <result column="not_reason" jdbcType="LONGVARCHAR" property="notReason"/>
        <result column="check_user_id" jdbcType="INTEGER" property="checkUserId"/>
        <result column="check_user_name" jdbcType="VARCHAR" property="checkUserName"/>
        <result column="check_time" jdbcType="TIMESTAMP" property="checkTime"/>
        <result column="take_user_id" jdbcType="INTEGER" property="takeUserId"/>
        <result column="take_user_name" jdbcType="VARCHAR" property="takeUserName"/>
        <result column="take_time" jdbcType="TIMESTAMP" property="takeTime"/>
        <result column="porp_type" jdbcType="INTEGER" property="porpType"/>
    </resultMap>
    <sql id="Base_Column_List">
        record_id,
        task_record_id,
        task_id,
        car_id,
        check_id,
        parent_id,
        check_name,
        check_content,
        car_type,
        sort,
        check_level,
        team,
        check_type_id,
        `result`,
        is_required,
        version_id,
        replace_rules,
        result_template,
        change_index,
        is_pass,
        result_copy,
        not_reason,
        check_user_id,
        check_user_name,
        check_time,
        take_user_id,
        take_user_name,
        take_time,
        porp_type
    </sql>

    <resultMap id="WorkCheckRecordVo" type="com.logictrue.word.entity.vo.WorkCheckRecordVo">
        <!--@Table drl_work_check_records-->
        <id column="record_id" jdbcType="INTEGER" property="recordId"/>
        <result column="task_record_id" jdbcType="INTEGER" property="taskRecordId"/>
        <result column="task_id" jdbcType="INTEGER" property="taskId"/>
        <result column="car_id" jdbcType="INTEGER" property="carId"/>
        <result column="check_id" jdbcType="INTEGER" property="checkId"/>
        <result column="parent_id" jdbcType="INTEGER" property="parentId"/>
        <result column="check_name" jdbcType="LONGVARCHAR" property="checkName"/>
        <result column="check_content" jdbcType="LONGVARCHAR" property="checkContent"/>
        <result column="car_type" jdbcType="INTEGER" property="carType"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="check_level" jdbcType="INTEGER" property="checkLevel"/>
        <result column="team" jdbcType="INTEGER" property="team"/>
        <result column="check_type_id" jdbcType="INTEGER" property="checkTypeId"/>
        <result column="result" jdbcType="LONGVARCHAR" property="result"/>
        <result column="is_required" jdbcType="INTEGER" property="isRequired"/>
        <result column="version_id" jdbcType="INTEGER" property="versionId"/>
        <result column="replace_rules" jdbcType="LONGVARCHAR" property="replaceRules"/>
        <result column="result_template" jdbcType="LONGVARCHAR" property="resultTemplate"/>
        <result column="change_index" jdbcType="LONGVARCHAR" property="changeIndex"/>
        <result column="is_pass" jdbcType="BIT" property="isPass"/>
        <result column="result_copy" jdbcType="LONGVARCHAR" property="resultCopy"/>
        <result column="not_reason" jdbcType="LONGVARCHAR" property="notReason"/>
        <result column="check_user_id" jdbcType="INTEGER" property="checkUserId"/>
        <result column="check_user_name" jdbcType="VARCHAR" property="checkUserName"/>
        <result column="check_time" jdbcType="TIMESTAMP" property="checkTime"/>
        <result column="take_user_id" jdbcType="INTEGER" property="takeUserId"/>
        <result column="take_user_name" jdbcType="VARCHAR" property="takeUserName"/>
        <result column="take_time" jdbcType="TIMESTAMP" property="takeTime"/>
        <result column="porp_type" jdbcType="INTEGER" property="porpType"/>
        <result column="month_str" jdbcType="VARCHAR" property="monthStr"/>
        <result column="day_str" jdbcType="VARCHAR" property="dayStr"/>
        <result column="bzz" jdbcType="VARCHAR" property="bzz"/>
    </resultMap>

    <sql id="selectWorkCheckRecordVo">
        SELECT record_id,
               check_id,
               check_name,
               result,
               MONTH(check_time) as month_str,
               DAY(check_time)   as day_str,
               check_user_name,
               ''                as bzz,
               take_user_name
        FROM drl_work_check_records
    </sql>

    <select id="getByTemplateIds" resultType="com.logictrue.word.entity.vo.WorkCheckRecordVo">
        <include refid="selectWorkCheckRecordVo"/>
        where check_id in
        <foreach item="templateId" collection="templateIds" open="(" separator="," close=")">
            #{templateId}
        </foreach>
    </select>

    <select id="getByCarIdAndTemplateIds" resultMap="WorkCheckRecordVo">
        <include refid="selectWorkCheckRecordVo"/>
        where car_id = #{carId}
        <if test="templateIds != null and templateIds.size() > 0">
            and check_id in
            <foreach item="templateId" collection="templateIds" open="(" separator="," close=")">
                #{templateId}
            </foreach>
        </if>
    </select>
</mapper>
