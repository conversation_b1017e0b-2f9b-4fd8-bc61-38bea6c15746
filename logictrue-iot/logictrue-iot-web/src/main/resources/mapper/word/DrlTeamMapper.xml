<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.word.mapper.DrlTeamMapper">
  <resultMap id="BaseResultMap" type="com.logictrue.word.entity.DrlTeam">
    <!--@mbg.generated-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="part_code" jdbcType="VARCHAR" property="partCode" />
    <result column="workshop" jdbcType="INTEGER" property="workshop" />
    <result column="leader_id" jdbcType="INTEGER" property="leaderId" />
    <result column="leader_name" jdbcType="VARCHAR" property="leaderName" />
    <result column="team_level" jdbcType="INTEGER" property="teamLevel" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="del_flag" jdbcType="BIT" property="delFlag" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="workshop_code" jdbcType="INTEGER" property="workshopCode" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `name`, part_code, workshop, leader_id, leader_name, team_level, parent_id, del_flag,
    sort, workshop_code
  </sql>
</mapper>
