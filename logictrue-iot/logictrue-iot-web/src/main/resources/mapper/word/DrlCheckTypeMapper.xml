<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.word.mapper.DrlCheckTypeMapper">
  <resultMap id="BaseResultMap" type="com.logictrue.word.entity.DrlCheckType">
    <!--@mbg.generated-->
    <!--@Table drl_check_type-->
    <id column="check_type_id" jdbcType="INTEGER" property="checkTypeId" />
    <result column="check_type_name" jdbcType="VARCHAR" property="checkTypeName" />
    <result column="team_id" jdbcType="INTEGER" property="teamId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="del_flag" jdbcType="BIT" property="delFlag" />
    <result column="create_by" jdbcType="INTEGER" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="INTEGER" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="type_level" jdbcType="INTEGER" property="typeLevel" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="car_type" jdbcType="INTEGER" property="carType" />
    <result column="check_ver_id" jdbcType="INTEGER" property="checkVerId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    check_type_id, check_type_name, team_id, sort, del_flag, create_by, create_time, 
    update_by, update_time, type_level, parent_id, car_type, check_ver_id
  </sql>
</mapper>