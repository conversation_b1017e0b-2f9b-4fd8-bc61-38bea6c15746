<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.word.mapper.DrlDataRecordsMapper">

    <resultMap type="com.logictrue.word.entity.DrlDataRecords" id="DrlDataRecordsResult">
        <result property="recordId" column="record_id"/>
        <result property="carId" column="car_id"/>
        <result property="templateId" column="template_id"/>
        <result property="checkName" column="check_name"/>
        <result property="checkContent" column="check_content"/>
        <result property="result" column="result"/>
        <result property="monthStr" column="month_str"/>
        <result property="dayStr" column="day_str"/>
        <result property="checkUserName" column="check_user_name"/>
        <result property="bzz" column="bzz"/>
        <result property="jyy" column="jyy"/>
    </resultMap>

    <sql id="selectDrlDataRecordsVo">
        select record_id, car_id, template_id, check_name, check_content, result, month_str, day_str, check_user_name, bzz, jyy
        from drl_data_records
    </sql>

    <!-- 根据模板ID列表查询检验记录数据 -->
    <select id="selectByTemplateIds" parameterType="java.util.List" resultMap="DrlDataRecordsResult">
        <include refid="selectDrlDataRecordsVo"/>
        where template_id in
        <foreach item="templateId" collection="templateIds" open="(" separator="," close=")">
            #{templateId}
        </foreach>
        order by template_id, record_id
    </select>

    <!-- 根据车号和模板ID列表查询检验记录数据 -->
    <select id="selectByCarIdAndTemplateIds" resultMap="DrlDataRecordsResult">
        <include refid="selectDrlDataRecordsVo"/>
        where car_id = #{carId}
        <if test="templateIds != null and templateIds.size() > 0">
            and template_id in
            <foreach item="templateId" collection="templateIds" open="(" separator="," close=")">
                #{templateId}
            </foreach>
        </if>
        order by template_id, record_id
    </select>

</mapper>
