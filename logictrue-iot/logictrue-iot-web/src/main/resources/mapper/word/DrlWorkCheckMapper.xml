<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.word.mapper.DrlWorkCheckMapper">
  <resultMap id="BaseResultMap" type="com.logictrue.word.entity.DrlWorkCheck">
    <!--@Table drl_work_check-->
    <id column="check_id" jdbcType="INTEGER" property="checkId" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="check_name" jdbcType="LONGVARCHAR" property="checkName" />
    <result column="check_content" jdbcType="LONGVARCHAR" property="checkContent" />
    <result column="car_type" jdbcType="INTEGER" property="carType" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="check_level" jdbcType="INTEGER" property="checkLevel" />
    <result column="team" jdbcType="INTEGER" property="team" />
    <result column="check_type_id" jdbcType="INTEGER" property="checkTypeId" />
    <result column="result_template" jdbcType="LONGVARCHAR" property="resultTemplate" />
    <result column="is_required" jdbcType="INTEGER" property="isRequired" />
    <result column="replace_num" jdbcType="INTEGER" property="replaceNum" />
    <result column="version_id" jdbcType="INTEGER" property="versionId" />
    <result column="replace_rules" jdbcType="LONGVARCHAR" property="replaceRules" />
    <result column="check_code" jdbcType="VARCHAR" property="checkCode" />
  </resultMap>

    <resultMap id="DrlWorkCheckVo" type="com.logictrue.word.entity.vo.WorkCheckVo">
        <!--  drl_work_check 连表 drl_check_type 字段 check_type_id -->
        <id column="check_id" jdbcType="INTEGER" property="checkId"/>
        <result column="parent_id" jdbcType="INTEGER" property="parentId"/>
        <result column="check_name" jdbcType="LONGVARCHAR" property="checkName"/>
        <result column="check_content" jdbcType="LONGVARCHAR" property="checkContent"/>
        <result column="car_type" jdbcType="INTEGER" property="carType"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="check_level" jdbcType="INTEGER" property="checkLevel"/>
        <result column="team" jdbcType="INTEGER" property="team"/>
        <result column="check_type_id" jdbcType="INTEGER" property="checkTypeId"/>
        <result column="check_type_name" jdbcType="VARCHAR" property="checkTypeName"/>
        <result column="result_template" jdbcType="LONGVARCHAR" property="resultTemplate"/>
        <result column="is_required" jdbcType="INTEGER" property="isRequired"/>
        <result column="version_id" jdbcType="INTEGER" property="versionId"/>
        <result column="result" jdbcType="LONGVARCHAR" property="result"/>
        <result column="month_str" jdbcType="INTEGER" property="monthStr"/>
        <result column="day_str" jdbcType="INTEGER" property="dayStr"/>
        <result column="czy" jdbcType="VARCHAR" property="czy"/>
        <result column="jyy" jdbcType="VARCHAR" property="jyy"/>
        <result column="cover_data" jdbcType="LONGVARCHAR" property="coverData"/>
        <result column="add_new_page" jdbcType="INTEGER" property="addNewPage"/>
        <result column="row_height" jdbcType="INTEGER" property="rowHeight"/>
        <result column="table_json" jdbcType="LONGVARCHAR" property="tableJson"/>
        <result column="value_map" jdbcType="LONGVARCHAR" property="valueMap"/>
    </resultMap>

    <sql id="selectWorkCheckVo">
      dwc.check_id,
      dwc.parent_id,
      dwc.check_name,
      dwc.check_content,
      dwc.car_type,
      dwc.sort,
      dwc.check_level,
      dwc.team,
      dwc.check_type_id,
      dct.check_type_name,
      dwc.result_template,
      dwc.is_required,
      dwc.version_id,
      dwc.cover_data,
      dwc.add_new_page,
      dwc.row_height,
      dwcr.result,
      MONTH(check_time) as month_str,
      DAY(check_time) as day_str,
      dwcr.check_user_name as czy,
      dwcr.take_user_name as jyy,
      dwcr.coordinate as table_json,
      dwcr.value_map
    </sql>

  <!-- 连表查询根据sort字段排序 -->
  <select id="selectWorkCheckVoList" resultMap="DrlWorkCheckVo">
    SELECT
      <include refid="selectWorkCheckVo"/>
    FROM drl_work_check dwc
           LEFT JOIN drl_work_check_records dwcr on dwc.check_id = dwcr.check_id and dwcr.car_id = #{carId}
    LEFT JOIN drl_check_type dct
              ON dwc.check_type_id = dct.check_type_id
    WHERE dwc.car_type = 1
    ORDER BY dwc.check_id
  </select>

  <select id="selectRangeList" resultMap="DrlWorkCheckVo">
      SELECT
      <include refid="selectWorkCheckVo"/>
      FROM drl_work_check dwc
      LEFT JOIN drl_work_check_records dwcr on dwc.check_id = dwcr.check_id and dwcr.car_id = #{carId}
      LEFT JOIN drl_check_type dct
      ON dwc.check_type_id = dct.check_type_id
      WHERE dwc.check_id >= #{start} and dwc.check_id &lt;= #{end}
      ORDER BY dwc.check_id
    </select>
</mapper>
