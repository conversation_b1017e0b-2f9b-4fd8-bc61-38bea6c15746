# Tomcat
server:
  port: 9335

# Spring
spring:
  application:
    # 应用名称
    name: logictrue-iot-web

  datasource:
    url: jdbc:mysql://*************:3308/lt-iot
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000
  redis:
    host: *************
    port: 6379

file:
  path: /home/<USER>

magic-api:
  web: /magic/web
  resource:
    type: database  # 配置接口存储方式，这里选择存在数据库中
    table-name: magic_api_file  # 数据库中的表名
    prefix: /magic-api  # 前缀
  show-url: false
#    location: classpath:magic-api
# 其它配置请参考 https://ssssssss.org/magic-api/config/

# 表格配置
table:
  config:
    # 表格列数
    column-count: 8
    # 检查工序列宽度
    check-process-column-width: 80
    # 检查名称列宽度 左右各5磅间距
    check-name-column-width: 395
    # 检查结果列宽度 左右各5磅 总共160
    check-result-column-width: 145
    # 行高限制 原410 由于增加每一行的单元格内部边距，调整为380
    row-height-limit: 397
    # 表格标题
    table-title: "检验记录表"
    # 小列宽度
    small-column-width: 23
    # 小行高度
    small-row-height: 24
    # 表格中单字高度（包含行间距）
    font-height: 14.6d
    # 表格中单字宽度
    font-width: 11d
    # 批量插入大小
    batch-size: 100
    space-size: 5
    page-line: 29

logging:
  level:
    com.logictrue.word: debug
