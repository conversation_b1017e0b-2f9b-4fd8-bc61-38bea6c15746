package com.logictrue.iot.interceptor;

import com.logictrue.auth.entity.LoginUser;
import com.logictrue.auth.service.TokenService;
import com.logictrue.common.core.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Magic Web访问拦截器
 * 用于控制/magic/web/index.html路径的访问权限
 * 要求用户必须登录且账号为admin
 */
//@Component
public class MagicWebInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(MagicWebInterceptor.class);

    //@Autowired
    private TokenService tokenService;

    /**
     * 登录页面路径
     */
    private static final String LOGIN_PAGE = "/403";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        logger.info("拦截请求: {}", requestURI);

        try {
            // 获取当前登录用户
            LoginUser loginUser = tokenService.getLoginUser(request);

            // 检查用户是否已登录
            if (loginUser == null) {
                logger.warn("用户未登录，拒绝访问: {}", requestURI);
                redirectToLogin(response);
                return false;
            }

            // 检查用户名是否为admin
            String username = loginUser.getUsername();
            if (StringUtils.isEmpty(username) || !"admin".equals(username)) {
                logger.warn("用户 {} 不是admin，拒绝访问: {}", username, requestURI);
                redirectToLogin(response);
                return false;
            }

            logger.info("用户 {} 通过权限验证，允许访问: {}", username, requestURI);
            return true;

        } catch (Exception e) {
            logger.error("权限验证过程中发生异常: {}", e.getMessage(), e);
            redirectToLogin(response);
            return false;
        }
    }

    /**
     * 重定向到登录页面
     */
    private void redirectToLogin(HttpServletResponse response) throws IOException {
        response.sendRedirect(LOGIN_PAGE);
    }
}
