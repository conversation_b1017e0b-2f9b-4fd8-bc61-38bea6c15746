package com.logictrue.word.entity.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 检验卡片记录表
 */
@Data
public class WorkCheckRecordVo {
    private Integer recordId;

    /**
     * 任务记录表id
     */
    private Integer taskRecordId;

    /**
     * 任务id
     */
    private Integer taskId;

    /**
     * 车辆id
     */
    private Integer carId;

    /**
     * 检查项目/标题id
     */
    private Integer checkId;

    /**
     * 所属标题id
     */
    private Integer parentId;

    /**
     * 检查项目及技术条件
     */
    private String checkName;

    /**
     * 内容
     */
    private String checkContent;

    /**
     * 车型
     */
    private Integer carType;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 1.标题2.检查项目
     */
    private Integer checkLevel;

    /**
     * 班组
     */
    private Integer team;

    /**
     * 检查工序
     */
    private Integer checkTypeId;

    /**
     * 实际检查结果
     */
    private String result;

    /**
     * 1.必填2.非必填
     */
    private Integer isRequired;

    /**
     * 检验卡片版本
     */
    private Integer versionId;

    /**
     * 替换规则
     */
    private String replaceRules;

    /**
     * 结果模板
     */
    private String resultTemplate;

    /**
     * 保存后修改规则
     */
    private String changeIndex;

    /**
     * 保存标识
     */
    private Boolean isPass;

    /**
     * 保存替换字段
     */
    private String resultCopy;

    /**
     * 不通过原因
     */
    private String notReason;

    /**
     * 检验填写人id
     */
    private Integer checkUserId;

    /**
     * 检验填写人
     */
    private String checkUserName;

    /**
     * 提交时间
     */
    private Date checkTime;

    /**
     * 检验人id
     */
    private Integer takeUserId;

    /**
     * 检验人
     */
    private String takeUserName;

    /**
     * 检验时间
     */
    private Date takeTime;

    /**
     * 页面输入类型
     */
    private Integer porpType;

    private String monthStr;

    private String dayStr;

    private String bzz;

}
