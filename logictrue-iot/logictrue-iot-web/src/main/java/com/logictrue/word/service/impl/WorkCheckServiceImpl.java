package com.logictrue.word.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.logictrue.common.core.utils.StringUtils;
import com.logictrue.word.config.TableConfigProperties;
import com.logictrue.word.dto.ChunkDto;
import com.logictrue.word.dto.TablePageDto;
import com.logictrue.word.dto.table.*;
import com.logictrue.word.entity.DesignWord;
import com.logictrue.word.entity.DrlTeam;
import com.logictrue.word.entity.vo.WorkCheckVo;
import com.logictrue.word.mapper.DrlWorkCheckMapper;
import com.logictrue.word.mapper.DesignWordMapper;
import com.logictrue.word.service.IWorkCheckService;
import com.logictrue.word.service.TeamService;
import com.logictrue.word.uitls.NestedTableUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 检验记录表服务实现
 */
@Slf4j
@Service
public class WorkCheckServiceImpl implements IWorkCheckService {

    @Autowired
    private DrlWorkCheckMapper drlWorkCheckMapper;

    @Autowired
    private DesignWordMapper designWordMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private TableConfigProperties tableConfigProperties;

    @Autowired
    private TeamService teamService;

    @Override
    public List<WorkCheckVo> selectWorkCheckVoList(String carId) {
        return drlWorkCheckMapper.selectWorkCheckVoList(carId);
    }

    @Override
    public TableDataDto generateWorkCheckTable(String carId) {
        // 1. 查询检验模板数据
        List<WorkCheckVo> workCheckList = selectWorkCheckVoList(carId);

        // 2. 根据checkTypeId分组并组合checkName
        Map<Integer, List<WorkCheckVo>> groupedByType = workCheckList.stream()
                .collect(Collectors.groupingBy(WorkCheckVo::getCheckTypeId));

        // 3. 生成表格数据
        return generateTableData(groupedByType).get(0);
    }

    @Override
    public List<TableDataDto> generateWorkCheckTableList(String carId) {
        // 1. 查询检验模板数据
        List<WorkCheckVo> workCheckList = selectWorkCheckVoList(carId);

        Map<Integer, List<WorkCheckVo>> groupedMap = new LinkedHashMap<>();

        workCheckList.forEach(workCheckVo -> {
            Integer checkTypeId = workCheckVo.getCheckTypeId();
            if (groupedMap.containsKey(checkTypeId)) {
                groupedMap.get(checkTypeId).add(workCheckVo);
            } else {
                List<WorkCheckVo> workCheckVoList = new ArrayList<>();
                workCheckVoList.add(workCheckVo);
                groupedMap.put(checkTypeId, workCheckVoList);
            }
        });

        // 2. 根据checkTypeId分组并组合checkName

        // 3. 生成表格数据列表（支持拆分）
        return generateTableData(groupedMap);
    }

    @Override
    public List<TableDataDto> generateRangeTableList(String carId, String range) {

        // 1. 查询检验模板数据
        List<WorkCheckVo> workCheckList;

        if (StringUtils.hasText(range)) {
            workCheckList = selectRangeList(carId, range);
        } else {
            workCheckList = selectWorkCheckVoList(carId);
        }

        Map<Integer, List<WorkCheckVo>> groupedMap = new LinkedHashMap<>();

        workCheckList.forEach(workCheckVo -> {
            Integer checkTypeId = workCheckVo.getCheckTypeId();
            if (groupedMap.containsKey(checkTypeId)) {
                groupedMap.get(checkTypeId).add(workCheckVo);
            } else {
                List<WorkCheckVo> workCheckVoList = new ArrayList<>();
                workCheckVoList.add(workCheckVo);
                groupedMap.put(checkTypeId, workCheckVoList);
            }
        });

        // 2. 根据checkTypeId分组并组合checkName

        // 3. 生成表格数据列表（支持拆分）
        return generateTableData(groupedMap);
    }

    private List<WorkCheckVo> selectRangeList(String carId, String range) {
        String[] split = range.split("-");
        Integer start = Integer.valueOf(split[0]);
        Integer end = Integer.valueOf(split[1]);
        return drlWorkCheckMapper.selectRangeList(carId, start, end);
    }

    @Override
    public boolean saveWorkCheckTable(String carId, TableDataDto tableDataDto) {
        try {
            // 1. 创建DesignWord对象
            DesignWord designWord = new DesignWord();
            designWord.setCarId(carId);
            designWord.setTitle(tableConfigProperties.getTableTitle());
            designWord.setStatus(1);
            designWord.setPageName("检验记录表");
            designWord.setPageOrder(1);
            designWord.setTotalPages(1);
            designWord.setIsActive(1);

            // 2. 转换表格配置和数据为JSON
            String tableConfigJson = objectMapper.writeValueAsString(tableDataDto.getHeaderConfig());
            String tableDataJson = objectMapper.writeValueAsString(tableDataDto);

            designWord.setTableConfig(tableConfigJson);
            designWord.setTableData(tableDataJson);

            // 3. 保存到数据库
            int result = designWordMapper.insertDesignWord(designWord);

            return result > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 生成表格数据
     *
     * <p>流程说明：
     * 1. 初始化变量：设置行高、字体宽度、单行容纳字符数等基础参数
     * 2. 遍历检查类型分组：对每个检查类型组进行处理
     * 3. 处理检查项目：对每个检查项目进行高度计算和内容组装
     * 4. 高度限制处理：当累积高度超过页面限制时，创建新的表格页面
     * 5. 创建表格行：根据检查类型名称和检查项目名称创建8列单元格
     * 6. 构建表格数据：组装完整的表格数据结构
     * 7. 生成最终DTO：为每个表格数据创建包含表头、单元格和元数据的DTO对象
     *
     * @param groupedByType 按检查类型ID分组的检验项目数据
     * @return 表格数据DTO列表，支持多页面拆分
     */
    private List<TableDataDto> generateTableData(Map<Integer, List<WorkCheckVo>> groupedByType) {
        // 初始化最终的表格数据DTO列表，用于返回结果
        List<TableDataDto> tableDataDtos = new ArrayList<>();

        Map<Integer, DrlTeam> teamMap = teamService.getTeamMap();

        // 用于缓存当前累积的检查项目内容
        ChunkDto chunkDto = new ChunkDto();

        chunkDto.currentChunk = new StringBuilder("");
        chunkDto.resultChunk = new StringBuilder("");
        chunkDto.monthChunk = new StringBuilder("");
        chunkDto.dayChunk = new StringBuilder("");
        chunkDto.czyChunk = new StringBuilder("");
        chunkDto.bzzChunk = new StringBuilder("");
        chunkDto.jyyChunk = new StringBuilder("");

        chunkDto.dateSet = new HashSet<>();

        // ===================== 基础参数设置 =====================
        double lineHeight = tableConfigProperties.getFontHeight(); // 宋体11号字的标准行高（包含行间距）单位磅
        double fontWidth = tableConfigProperties.getFontWidth(); // 宋体11号字的平均字符宽度，单位磅
        int oneLineSize = (int) Math.floor(tableConfigProperties.getCheckNameColumnWidth() / fontWidth); // 单行可容纳的字符数
        int checkProcessLineSize = (int) Math.floor(tableConfigProperties.getCheckProcessColumnWidth() / fontWidth); // 检查类型列可容纳的字符数

        TablePageDto tablePage = resetTablePageDto(0);

        // ===================== 遍历检查类型分组 =====================
        // 获取所有分组键，用于判断是否为最后一个分组
        List<Integer> groupKeys = new ArrayList<>(groupedByType.keySet());

        // 预加载下一个分组
        List<WorkCheckVo> nextGroupItems = null;
        if (groupKeys.size() > 1) {
            Integer nextGroupKey = groupKeys.get(1); // 第二个分组
            nextGroupItems = groupedByType.get(nextGroupKey);
        }

        for (int groupIndex = 0; groupIndex < groupKeys.size(); groupIndex++) {
            Integer checkTypeId = groupKeys.get(groupIndex);
            List<WorkCheckVo> checkItems = groupedByType.get(checkTypeId);

            // 获取下一个分组
            List<WorkCheckVo> currentNextGroupItems = nextGroupItems;
            if (groupIndex + 2 < groupKeys.size()) {
                Integer nextGroupKey = groupKeys.get(groupIndex + 2);
                nextGroupItems = groupedByType.get(nextGroupKey);
            } else {
                nextGroupItems = null;
            }

            if (!checkItems.isEmpty()) {
                // 获取检查类型名称（从第一个检查项目中获取）
                String checkTypeName = checkItems.get(0).getCheckTypeName();
                WorkCheckVo workCheckVo = new WorkCheckVo();

                // 判断当前分组是否为最后一个分组
                boolean isLastGroup = (groupIndex == groupKeys.size() - 1);

                // ===================== 遍历检查项目 =====================


                for (int i = 0; i < checkItems.size(); i++) {
                    WorkCheckVo checkItem = checkItems.get(i);
                    // 当前项目的nextItem就是从上一个循环获取的nextItem
                    WorkCheckVo nextItem = null;

                    if (StringUtils.hasText(checkItem.getTableJson())) {
                        tablePage.tableData.setHasNestedTable(true);
                    }

                    // 为下一次循环预加载nextItem
                    if (i + 1 < checkItems.size()) {
                        nextItem = checkItems.get(i + 1);
                    } else if (i + 1 == checkItems.size() && currentNextGroupItems != null && !currentNextGroupItems.isEmpty()) {
                        // 当前分组的最后一个项目，下一个是下一个分组的第一个项目
                        nextItem = currentNextGroupItems.get(0);
                    }

                    boolean hasNext = nextItem != null;

                    String coverData = checkItem.getCoverData();

                    workCheckVo = checkItem;

                    if (StringUtils.hasText(coverData)) {
                        // 存在封面信息
                        CoverDataDto coverDataDto = JSON.parseObject(coverData, new TypeReference<CoverDataDto>() {
                        });
                        tableDataDtos.add(coverDataDto);
                    }

                    String line = checkItem.getCheckName();

                    String result = "";
                    if (checkItem.getCheckLevel() != 3) {
                        result = checkItem.getResult();
                    }

                    String monthStr = checkItem.getMonthStr();
                    String dayStr = checkItem.getDayStr();
                    String czy = checkItem.getCzy();
                    String jyy = checkItem.getJyy();

                    Integer team = checkItem.getTeam();
                    DrlTeam drlTeam = teamMap.get(team);
                    String bzz = drlTeam.getLeaderName();

                    String dateStr = monthStr + dayStr;

                    // addNewPage值为1时，当前页剩余高度大于本记录所占高度时将当前记录所占行设置为剩余高度
                    int addNewPage = checkItem.getAddNewPage();

                    // ===================== 计算当前检查项目的高度 =====================
                    int thisLineHeight = (int) Math.ceil(calculateCheckItemHeight(checkItem, checkTypeName, lineHeight, oneLineSize, checkProcessLineSize, false));


                    tablePage.processHeight = (int) calculateCheckProcessHeight(checkTypeName, lineHeight, checkProcessLineSize);

                    // ===================== 段落拆分逻辑 =====================
                    // 段落超过一页的高度时，从一半字符位置向后查找第一个换行符作为拆分点，将段落拆分成两页
                    if (tablePage.lastRemainingHeight == tableConfigProperties.getRowHeightLimit() &&
                            thisLineHeight >= tableConfigProperties.getRowHeightLimit()) {
                        //内容长度超出2页
                        if (thisLineHeight > tableConfigProperties.getRowHeightLimit() * 2) {

                            tablePage = handleLongParagraph(line, checkItem, chunkDto, tablePage,
                                    checkTypeName, tableDataDtos, lineHeight, oneLineSize,
                                    monthStr, dayStr, czy, bzz, jyy, dateStr, result);
                            continue;
                        }

                        // 执行段落拆分
                        String[] splitParts = splitParagraphAtBreakPoint(line, line.length());
                        String firstPart = splitParts[0];
                        String secondPart = splitParts[1];

                        // 处理拆分后的数据
                        tablePage = handleSplitParagraph(firstPart, secondPart, checkItem, chunkDto, tablePage,
                                checkTypeName, tableDataDtos, lineHeight, oneLineSize,
                                monthStr, dayStr, czy, bzz, jyy, dateStr, result);
                        continue;
                    }

                    // ===================== addNewPage参数处理 =====================
                    if (addNewPage == 1) {
                        // 计算当前页剩余高度
                        // tableConfigProperties.getSpaceSize() * 2 当前行上下行间距
                        double remainingHeight = tableConfigProperties.getRowHeightLimit() -
                                tablePage.totalHeight - tablePage.spaceHeight - tableConfigProperties.getSpaceSize() * 2;

                        // 如果当前页剩余高度大于本记录所占高度，则将当前记录所占行设置为剩余高度
                        if (remainingHeight > thisLineHeight) {
                            setContent(chunkDto, line, result, monthStr, dayStr, czy, bzz, jyy, dateStr);

                            tablePage.checkIdList.add(checkItem.getCheckId());

                            tablePage.totalHeight += remainingHeight;
                            tablePage.rowHeight += remainingHeight;

                            // 减去当前行上下行间距
                            tablePage.lastRemainingHeight -= tableConfigProperties.getSpaceSize() * 2;

                            tablePage = createNewTablePage(workCheckVo, chunkDto, tablePage, checkTypeName, tableDataDtos, 0);
                            continue;
                        }
                    }

                    // ===================== 预计算下一个项目的高度 =====================
                    double nextItemHeight = 0;
                    boolean isLastItemOnPage = false;

                    // 判断是否为当前分组的最后一个项目
                    boolean isLastItemInGroup = (i == checkItems.size() - 1);

                    if (hasNext) {
                        // 计算下一个项目的高度（使用从上一个循环获取的nextItem）
                        // 计算下一个项目的高度需要追加第一列的高度计算
                        nextItemHeight = calculateCheckItemHeight(nextItem, nextItem.getCheckTypeName(),
                                lineHeight, oneLineSize, checkProcessLineSize, true);

                        // 判断当前项目是否为当前页的最后一个项目
                        // 只有当外层循环也是最后一个分组且内层循环也是最后一个项目时，才考虑页面高度限制
                        if (isLastItemInGroup) {
                            if (thisLineHeight + nextItemHeight > tablePage.lastRemainingHeight) {
                                isLastItemOnPage = true;
                            }
                        }
                    } else {
                        // 当前项目是最后一个项目
                        // 只有当外层循环也是最后一个分组时，才设置为true
                        if (isLastGroup) {
                            isLastItemOnPage = true;
                        }
                    }

                    // ===================== 如果是当前页最后一个项目，设置高度为剩余高度 =====================
                    if (isLastItemOnPage) {

                        // tableConfigProperties.getSpaceSize() * 2 当前行上下行间距

                        double remainingHeight = tableConfigProperties.getRowHeightLimit() -
                                tablePage.totalHeight - tablePage.spaceHeight - tableConfigProperties.getSpaceSize() * 2;

                        tablePage.lastRemainingHeight = (int) remainingHeight;

                        setContent(chunkDto, line, result, monthStr, dayStr, czy, bzz, jyy, dateStr);

                        tablePage.checkIdList.add(checkItem.getCheckId());

                        tablePage.totalHeight += tablePage.lastRemainingHeight;
                        tablePage.rowHeight += tablePage.lastRemainingHeight;

                        tablePage = createNewTablePage(workCheckVo, chunkDto, tablePage, checkTypeName, tableDataDtos, 0);
                        continue;
                    }

                    tablePage.totalH += thisLineHeight;
                    tablePage.rowH += thisLineHeight;

                    if (tablePage.processHeight > tablePage.rowH) {
                        // 第一列内容行高大于第二列行高
                        if (tablePage.processFirst) {
                            tablePage.totalHeight += tablePage.processHeight;
                            tablePage.processFirst = false;
                        }
                        tablePage.rowHeight = tablePage.processHeight;
                    } else {
                        tablePage.totalHeight = tablePage.totalH;
                        tablePage.rowHeight = tablePage.rowH;
                    }
                    // === 高度限制处理（分页逻辑） ===
                    if (tablePage.totalHeight >= tableConfigProperties.getRowHeightLimit()) {
                        // 当累积高度超过页面限制时，需要分页

                        tablePage = createNewTablePage(workCheckVo, chunkDto, tablePage, checkTypeName, tableDataDtos, thisLineHeight);

                        // 清空累积内容，开始新页面
                        chunkDto.currentChunk.setLength(0);
                        chunkDto.currentChunk.append(line).append("\n");

                        chunkDto.resultChunk.setLength(0);

                        chunkDto.monthChunk.setLength(0);
                        chunkDto.dayChunk.setLength(0);
                        chunkDto.czyChunk.setLength(0);
                        chunkDto.bzzChunk.setLength(0);
                        chunkDto.jyyChunk.setLength(0);
                        chunkDto.dateSet.clear();

                        if (StringUtils.hasText(result)) {
                            chunkDto.resultChunk.append(result).append("\n");
                        }

                        if (StringUtils.hasText(monthStr)) {
                            chunkDto.monthChunk.append(monthStr).append("\n");
                        }
                        if (StringUtils.hasText(dayStr)) {
                            chunkDto.dayChunk.append(dayStr).append("\n");
                        }
                        if (StringUtils.hasText(czy)) {
                            chunkDto.czyChunk.append(czy).append("\n");
                        }
                        if (StringUtils.hasText(bzz)) {
                            chunkDto.bzzChunk.append(bzz).append("\n");
                        }
                        if (StringUtils.hasText(jyy)) {
                            chunkDto.jyyChunk.append(jyy).append("\n");
                        }


                    } else {
                        // 未超过高度限制，继续累积内容
                        chunkDto.currentChunk.append(line).append("\n");
                        if (StringUtils.hasText(result)) {
                            chunkDto.resultChunk.append(result).append("\n");
                        }
                        if (!chunkDto.dateSet.contains(dateStr)) {
                            if (StringUtils.hasText(monthStr)) {
                                chunkDto.monthChunk.append(monthStr).append("\n");
                                chunkDto.dayChunk.append(dayStr).append("\n");
                            }
                            chunkDto.dateSet.add(dateStr);
                        }
                        if (StringUtils.hasText(czy) && !chunkDto.czyChunk.toString().contains(czy)) {
                            chunkDto.czyChunk.append(czy);
                        }
                        if (StringUtils.hasText(bzz) && !chunkDto.bzzChunk.toString().contains(bzz)) {
                            chunkDto.bzzChunk.append(bzz);
                        }
                        if (StringUtils.hasText(jyy) && !chunkDto.jyyChunk.toString().contains(jyy)) {
                            chunkDto.jyyChunk.append(jyy);
                        }
                    }

                    tablePage.checkIdList.add(checkItem.getCheckId());
                }

                // ===================== 处理每个检查类型组的剩余内容 =====================
                if (chunkDto.currentChunk.length() > 0) {

                    createRow(tablePage, workCheckVo, checkTypeName, chunkDto, tablePage.rowHeight);

                    // 将行添加到页面中
                    tablePage.rows.add(tablePage.row);

                    tablePage.checkIdMap.put(tablePage.rowIndex, tablePage.checkIdList);

                    tablePage.rowIndex++;

                    resetTablePageAfterRowCreate(tablePage);

                    chunkDto.currentChunk.setLength(0); // 清空累积内容
                    chunkDto.resultChunk.setLength(0);
                    chunkDto.monthChunk.setLength(0);
                    chunkDto.dayChunk.setLength(0);
                    chunkDto.czyChunk.setLength(0);
                    chunkDto.bzzChunk.setLength(0);
                    chunkDto.jyyChunk.setLength(0);
                    chunkDto.dateSet.clear();

                }
            }
        }

        // ===================== 处理最后一个页面的数据 =====================
        if (!tablePage.rows.isEmpty()) {
            tablePage.tableData.setCellRows(tablePage.rows);
            tablePage.tableData.setCheckIdMap(tablePage.checkIdMap);
            //tableDatas.add(tablePage.tableData);
            addToTableDataDtoList(tablePage.tableData, tableDataDtos);
        }

        // ===================== 构建最终的TableDataDto对象 =====================
        /*for (TableData templateTable : tableDatas) {
            addToTableDataDtoList(templateTable, tableDataDtos);
        }*/

        return tableDataDtos;
    }

    private void resetTablePageAfterRowCreate(TablePageDto tablePage) {
        // 重置行相关变量
        tablePage.row = new ArrayList<>(); // 新建行
        tablePage.checkIdList = new ArrayList<>();
        tablePage.rowHeight = 0; // 重置行高
        tablePage.rowH = 0; // 重置行高
        tablePage.lastRemainingHeight = (int) (tableConfigProperties.getRowHeightLimit() - tablePage.totalHeight - tablePage.spaceHeight); // 更新剩余高度
        tablePage.processFirst = true;
    }

    private void addToTableDataDtoList(TableData templateTable, List<TableDataDto> tableDataDtos) {
        TableDataDto tableDataDto = new TableDataDto();

        // 1. 设置表头配置
        HeaderConfig headerConfig = createDefaultHeaderConfig();
        headerConfig.setHeaderWidthConfig(createDefaultHeaderWidthConfig());
        tableDataDto.setHeaderConfig(headerConfig);

        // 2. 设置单元格数据
        tableDataDto.setCellRows(templateTable.getCellRows());
        tableDataDto.setMerges(new ArrayList<>()); // 无单元格合并

        // 3. 设置元数据
        Metadata metadata = createMetadata(templateTable.getCellRows().size());
        tableDataDto.setMetadata(metadata);

        tableDataDto.setCheckIdMap(templateTable.getCheckIdMap());

        tableDataDto.setHasNestedTable(templateTable.isHasNestedTable());

        // 将DTO添加到结果列表中
        tableDataDtos.add(tableDataDto);
    }

    private TablePageDto createNewTablePage(WorkCheckVo workCheckVo,
                                            ChunkDto chunkDto,
                                            TablePageDto tablePage, String checkTypeName, List<TableDataDto> tableDataDtos, double nextLineHeight) {

        if (chunkDto.currentChunk.length() > 0) {
            createRow(tablePage, workCheckVo, checkTypeName, chunkDto, tablePage.lastRemainingHeight);
            // 将当前行添加到页面行列表中
            tablePage.rows.add(tablePage.row);
        }
        tablePage.checkIdMap.put(tablePage.rowIndex, tablePage.checkIdList);


        // 完成当前页面的数据组装
        tablePage.tableData.setCellRows(tablePage.rows);
        tablePage.tableData.setHeight(tableConfigProperties.getRowHeightLimit());

        tablePage.tableData.setCheckIdMap(tablePage.checkIdMap);

        //tableDatas.add(tablePage.tableData);

        addToTableDataDtoList(tablePage.tableData, tableDataDtos);

        // ===================== 分页后的重置操作 =====================

        chunkDto.currentChunk.setLength(0);
        chunkDto.resultChunk.setLength(0);
        chunkDto.monthChunk.setLength(0);
        chunkDto.dayChunk.setLength(0);
        chunkDto.czyChunk.setLength(0);
        chunkDto.bzzChunk.setLength(0);
        chunkDto.jyyChunk.setLength(0);
        chunkDto.dateSet.clear();

        tablePage = resetTablePageDto(nextLineHeight);

        return tablePage;
    }

    private TablePageDto resetTablePageDto(double nextLineHeight) {
        TablePageDto tablePage = new TablePageDto();
        // 创建新页面的数据结构
        tablePage.tableData = new TableData();
        tablePage.row = new ArrayList<>();
        tablePage.rows = new ArrayList<>();

        tablePage.checkIdList = new ArrayList<>();
        tablePage.checkIdMap = new HashMap<>();

        // 重置高度计算，当前检查项目作为新页面的起始内容
        tablePage.totalHeight = nextLineHeight;
        tablePage.rowHeight = nextLineHeight;
        // 重置剩余高度为完整的表格高度
        tablePage.lastRemainingHeight = tableConfigProperties.getRowHeightLimit();
        tablePage.spaceHeight = 0;
        tablePage.processHeight = 0;
        tablePage.processFirst = true;
        tablePage.totalH = nextLineHeight;
        tablePage.rowH = nextLineHeight;
        tablePage.rowIndex = 0;
        return tablePage;
    }

    private void createRow(TablePageDto tablePage, WorkCheckVo workCheckVo,
                           String checkTypeName, ChunkDto chunkDto,
                           double lastRemainingHeight) {

        tablePage.spaceHeight += tableConfigProperties.getSpaceSize() * 2;
        List<CellData> row = tablePage.row;
        String checkName = chunkDto.currentChunk.toString();
        String result = chunkDto.resultChunk.toString();
        String monthStr = chunkDto.monthChunk.toString();
        String dayStr = chunkDto.dayChunk.toString();
        String czy = chunkDto.czyChunk.toString();
        String bzz = chunkDto.bzzChunk.toString();
        String jyy = chunkDto.jyyChunk.toString();
        // ===================== 创建表格行（8列结构） =====================
        // 列0: 检查工序名称（宽度100px）
        row.add(createCellData(checkTypeName, tableConfigProperties.getCheckProcessColumnWidth(), lastRemainingHeight));
        // 列1: 检查项目及技术条件（宽度460px）
        row.add(createNestedTableCellData(checkName, workCheckVo, tableConfigProperties.getCheckNameColumnWidth(), lastRemainingHeight));
        // 列2: 实际检查结果（宽度160px）
        row.add(createCellData(result, tableConfigProperties.getCheckResultColumnWidth(), lastRemainingHeight));
        // 列3-7: 完工（月、日）、操作员、班组长、检验员（各25磅）
        row.add(createCellData(monthStr, tableConfigProperties.getSmallColumnWidth(), lastRemainingHeight));
        row.add(createCellData(dayStr, tableConfigProperties.getSmallColumnWidth(), lastRemainingHeight));
        row.add(createCellData(czy, tableConfigProperties.getSmallColumnWidth(), lastRemainingHeight));
        row.add(createCellData(bzz, tableConfigProperties.getSmallColumnWidth(), lastRemainingHeight));
        row.add(createCellData(jyy, tableConfigProperties.getSmallColumnWidth(), lastRemainingHeight));
    }

    /**
     * 创建默认表头配置
     */
    private HeaderConfig createDefaultHeaderConfig() {
        HeaderConfig headerConfig = new HeaderConfig();

        List<List<String>> headers = new ArrayList<>();
        List<String> firstRow = new ArrayList<>();
        firstRow.add("检查工序\n名称");
        firstRow.add("检 查 项 目 及 技 术 条 件");
        firstRow.add("实 际 检 查 结 果");
        firstRow.add("完工");
        firstRow.add("");
        firstRow.add("操作员");
        firstRow.add("班组长");
        firstRow.add("检验员");

        List<String> secondRow = new ArrayList<>();
        secondRow.add("");
        secondRow.add("");
        secondRow.add("");
        secondRow.add("月");
        secondRow.add("日");
        secondRow.add("");
        secondRow.add("");
        secondRow.add("");

        headers.add(firstRow);
        headers.add(secondRow);

        headerConfig.setHeaders(headers);

        // 设置表头合并配置
        List<MergeConfig> headerMerges = new ArrayList<>();
        headerMerges.add(new MergeConfig(0, 0, 1, 0, "检查工序\n名称"));
        headerMerges.add(new MergeConfig(0, 1, 1, 1, "检 查 项 目 及 技 术 条 件"));
        headerMerges.add(new MergeConfig(0, 2, 1, 2, "实 际 检 查 结 果"));
        headerMerges.add(new MergeConfig(0, 3, 0, 4, "完工"));
        headerMerges.add(new MergeConfig(0, 5, 1, 5, "操作员"));
        headerMerges.add(new MergeConfig(0, 6, 1, 6, "班组长"));
        headerMerges.add(new MergeConfig(0, 7, 1, 7, "检验员"));

        headerConfig.setHeaderMerges(headerMerges);

        return headerConfig;
    }

    /**
     * 创建默认表头宽度配置
     */
    private HeaderWidthConfig createDefaultHeaderWidthConfig() {
        HeaderWidthConfig headerWidthConfig = new HeaderWidthConfig();

        List<Integer> columnWidths = new ArrayList<>();
        columnWidths.add(tableConfigProperties.getCheckProcessColumnWidth());
        columnWidths.add(tableConfigProperties.getCheckNameColumnWidth());
        columnWidths.add(tableConfigProperties.getCheckResultColumnWidth());
        columnWidths.add(tableConfigProperties.getSmallColumnWidth());
        columnWidths.add(tableConfigProperties.getSmallColumnWidth());
        columnWidths.add(tableConfigProperties.getSmallColumnWidth());
        columnWidths.add(tableConfigProperties.getSmallColumnWidth());
        columnWidths.add(tableConfigProperties.getSmallColumnWidth());
        headerWidthConfig.setColumnWidths(columnWidths);

        List<Integer> headerHeights = new ArrayList<>();
        headerHeights.add(tableConfigProperties.getSmallRowHeight());
        headerHeights.add(tableConfigProperties.getSmallRowHeight());
        headerWidthConfig.setHeaderHeights(headerHeights);

        return headerWidthConfig;
    }

    /**
     * 创建单元格数据
     */
    private CellData createCellData(String content, int width, double height) {
        CellData cellData = new CellData();
        cellData.setContent(content);
        cellData.setOriginContent(content);
        cellData.setWidth(width);
        cellData.setHeight(height);
        cellData.setHasMath(false);
        cellData.setHasMultipleContent(false);
        return cellData;
    }

    /**
     * 创建单元格数据
     */
    private CellData createNestedTableCellData(String content, WorkCheckVo workCheckVo, int width, double height) {
        CellData cellData = new CellData();
        cellData.setContent(content);
        cellData.setOriginContent(content);
        cellData.setWidth(width);
        cellData.setHeight(height);
        cellData.setHasMath(false);
        cellData.setHasMultipleContent(false);
        if (workCheckVo != null && StringUtils.hasText(workCheckVo.getTableJson())) {
            String tableJson = workCheckVo.getTableJson();
            String valueMap = workCheckVo.getValueMap();
            if (!StringUtils.hasText(valueMap)) {
                valueMap = "{}";
            }
            cellData.setNestedTable(NestedTableUtil.convertStringToNestedTableConfig(tableJson, valueMap));
        }
        return cellData;
    }

    /**
     * 创建元数据
     */
    private Metadata createMetadata(int totalRows) {
        Metadata metadata = new Metadata();
        metadata.setTitle(tableConfigProperties.getTableTitle());
        metadata.setTotalRows(totalRows);
        metadata.setTotalColumns(tableConfigProperties.getColumnCount());
        metadata.setHeaderRows(2);
        metadata.setHasMergedCells(false);
        metadata.setHasHeaderMerges(true);
        metadata.setHasLatexProcessing(true);
        metadata.setUseDynamicHeader(true);
        metadata.setHasVerticalHeaders(true);
        metadata.setExportTime(LocalDateTime.now().toString());
        metadata.setHasNestedTables(true);
        metadata.setNestedLevel(0);
        metadata.setMaxNestedLevel(2);

        return metadata;
    }


    @Override
    public boolean saveWorkCheckTableList(String carId, List<TableDataDto> tableDataDtoList) {
        try {
            if (tableDataDtoList == null || tableDataDtoList.isEmpty()) {
                return false;
            }

            // 使用批量插入方法
            return batchSaveWorkCheckTableList(carId, tableDataDtoList);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean batchSaveWorkCheckTableList(String carId, List<TableDataDto> tableDataDtoList) {
        try {
            if (tableDataDtoList == null || tableDataDtoList.isEmpty()) {
                return false;
            }

            // 创建DesignWord对象列表
            List<DesignWord> designWordList = new ArrayList<>();
            List<Integer> templateIdList = new ArrayList<>();
            int totalResult = 0;
            for (int i = 0; i < tableDataDtoList.size(); i++) {
                TableDataDto tableDataDto = tableDataDtoList.get(i);

                // 创建DesignWord对象
                DesignWord designWord = new DesignWord();
                designWord.setCarId(carId);
                designWord.setTitle(tableConfigProperties.getTableTitle() + (i > 0 ? " - 第" + (i + 1) + "部分" : ""));
                designWord.setStatus(1);
                designWord.setPageName("检验记录表" + (i > 0 ? " - 第" + (i + 1) + "部分" : ""));
                designWord.setPageOrder(i + 1);
                designWord.setTotalPages(tableDataDtoList.size());
                designWord.setIsActive(1);
                designWord.setHasNestedTable(tableDataDto.isHasNestedTable() ? 1 : 0);

                if (tableDataDto instanceof CoverDataDto) {
                    CoverDataDto coverDataDto = (CoverDataDto) tableDataDto;
                    designWord.setCoverData(JSON.toJSONString(coverDataDto));
                } else {
                    Map<Integer, List<Integer>> checkIdMap = tableDataDto.getCheckIdMap();
                    templateIdList.addAll(checkIdMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
                    designWord.setRowTemplateBindings(JSON.toJSONString(checkIdMap));
                    designWord.setBoundTemplateIds(StringUtils.join(templateIdList, ","));
                    // 转换表格配置和数据为JSON
                    String tableConfigJson = objectMapper.writeValueAsString(tableDataDto.getHeaderConfig());
                    String tableDataJson = objectMapper.writeValueAsString(tableDataDto);

                    designWord.setTableConfig(tableConfigJson);
                    designWord.setTableData(tableDataJson);
                }
                designWordList.add(designWord);

                templateIdList.clear();

                int size = designWordList.size();
                if (size >= tableConfigProperties.getBatchSize()) {
                    int result = designWordMapper.batchInsert(designWordList);
                    if (result > 0) {
                        designWordList.clear();
                        log.info("批量插入成功，数量: {}", result);
                        totalResult += result;
                    } else {
                        throw new RuntimeException("批量插入失败");
                    }
                }
            }

            if (!designWordList.isEmpty()) {
                // 使用批量插入方法保存到数据库
                int result = designWordMapper.batchInsert(designWordList);
                if (result > 0) {
                    log.info("批量插入剩余数据成功，数量: {}", result);
                    totalResult += result;
                }
            }

            return totalResult > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    private TablePageDto handleLongParagraph(String content, WorkCheckVo checkItem,
                                              ChunkDto chunkDto, TablePageDto tablePage, String checkTypeName,
                                              List<TableDataDto> tableDataDtos, double lineHeight, double oneLineSize,
                                              String monthStr, String dayStr, String czy, String bzz, String jyy,
                                              String dateStr, String result) {
        // 按换行符分割文本，-1参数保留空行
        String[] lines = content.split("\n", -1);
        int lineCount = 0;
        int lineIndex = 0;
        int tempLineCount = 0;
        int tempLineindex = 0;
        int lastIndex = 0;
        for (String line : lines) {
            tempLineCount = lineCount;
            tempLineindex = lineIndex;
            int count = calculateActualLineCount(line, oneLineSize);
            lineCount += count;
            lineIndex += line.length() + 1;

            if (lineCount >= tableConfigProperties.getPageLine() && tempLineCount < tableConfigProperties.getPageLine()) {
                String pageText = content.substring(lastIndex, tempLineindex);
                tablePage = handleSplitParagraph(pageText, null, checkItem, chunkDto, tablePage,
                        checkTypeName, tableDataDtos, lineHeight, oneLineSize,
                        monthStr, dayStr, czy, bzz, jyy, dateStr, result);
                lastIndex = tempLineindex;
                lineCount = count;
            }


        }
        // 插入剩余数据
        String remainingText = content.substring(lastIndex);
        tablePage = handleSplitParagraph(remainingText, null, checkItem, chunkDto, tablePage,
                checkTypeName, tableDataDtos, lineHeight, oneLineSize,
                monthStr, dayStr, czy, bzz, jyy, dateStr, result);

        return tablePage;
    }

    /**
     * 段落拆分方法
     * 从一半字符位置向后查找第一个换行符作为拆分点，没有则找句号，再没有则找分号/逗号，最后从一半位置直接拆分
     *
     * @param originalText 原始文本
     * @param maxLength    最大长度（用于计算拆分点）
     * @return 拆分后的两部分文本 [第一部分, 第二部分]
     */
    private String[] splitParagraphAtBreakPoint(String originalText, int maxLength) {

        // 计算拆分点位置（从一半位置开始）
        int splitPoint = maxLength / 2;

        // 1. 优先查找换行符
        int newlineIndex = originalText.indexOf('\n', splitPoint);
        if (newlineIndex != -1 && newlineIndex < originalText.length()) {
            return new String[]{
                    originalText.substring(0, newlineIndex).trim(),
                    originalText.substring(newlineIndex + 1).trim()
            };
        }

        // 2. 查找句号
        int periodIndex = originalText.indexOf('。', splitPoint);
        if (periodIndex != -1 && periodIndex < originalText.length()) {
            return new String[]{
                    originalText.substring(0, periodIndex + 1).trim(),
                    originalText.substring(periodIndex + 1).trim()
            };
        }

        // 3. 查找分号
        int semicolonIndex = originalText.indexOf('；', splitPoint);
        if (semicolonIndex != -1 && semicolonIndex < originalText.length()) {
            return new String[]{
                    originalText.substring(0, semicolonIndex + 1).trim(),
                    originalText.substring(semicolonIndex + 1).trim()
            };
        }

        // 4. 查找逗号
        int commaIndex = originalText.indexOf('，', splitPoint);
        if (commaIndex != -1 && commaIndex < originalText.length()) {
            return new String[]{
                    originalText.substring(0, commaIndex + 1).trim(),
                    originalText.substring(commaIndex + 1).trim()
            };
        }

        // 5. 如果都没有合适的拆分点，从一半位置直接拆分
        return new String[]{
                originalText.substring(0, splitPoint).trim(),
                originalText.substring(splitPoint).trim()
        };
    }

    /**
     * 处理拆分后的段落数据
     * 第一部分留在当前页，第二部分放到新页面
     *
     * @param firstPart     第一部分文本
     * @param secondPart    第二部分文本
     * @param checkItem     检查项目
     * @param chunkDto      数据块
     * @param tablePage     表格页面
     * @param checkTypeName 检查类型名称
     * @param tableDataDtos 表格数据列表
     * @param lineHeight    行高
     * @param oneLineSize   单行大小
     * @param monthStr      月份字符串
     * @param dayStr        日期字符串
     * @param czy           操作员
     * @param bzz           班组长
     * @param jyy           检验员
     * @param dateStr       日期字符串
     * @param result        结果
     */
    private TablePageDto handleSplitParagraph(String firstPart, String secondPart, WorkCheckVo checkItem,
                                              ChunkDto chunkDto, TablePageDto tablePage, String checkTypeName,
                                              List<TableDataDto> tableDataDtos, double lineHeight, double oneLineSize,
                                              String monthStr, String dayStr, String czy, String bzz, String jyy,
                                              String dateStr, String result) {

        // 处理第一部分（当前页）
        if (StringUtils.hasText(firstPart)) {

            // 当前页剩余高度足够，添加第一部分
            setContent(chunkDto, firstPart, result, monthStr, dayStr, czy, bzz, jyy, dateStr);

            tablePage.checkIdList.add(checkItem.getCheckId());

            tablePage = createNewTablePage(checkItem, chunkDto, tablePage, checkTypeName, tableDataDtos, 0);

        }

        // 处理第二部分（新页面）
        if (StringUtils.hasText(secondPart)) {
            setContent(chunkDto, secondPart, result, monthStr, dayStr, czy, bzz, jyy, dateStr);
            // 创建新页面
            tablePage = createNewTablePage(checkItem, chunkDto, tablePage, checkTypeName, tableDataDtos, 0);
        }

        return tablePage;
    }

    private static void setContent(ChunkDto chunkDto, String firstPart, String result, String monthStr, String dayStr, String czy, String bzz, String jyy, String dateStr) {
        chunkDto.currentChunk.append(firstPart);
        if (StringUtils.hasText(result)) {
            chunkDto.resultChunk.append(result);
        }

        // 处理其他字段
        if (!chunkDto.dateSet.contains(dateStr)) {
            if (StringUtils.hasText(monthStr)) {
                chunkDto.monthChunk.append(monthStr);
                chunkDto.dayChunk.append(dayStr);
            }
            chunkDto.dateSet.add(dateStr);
        }
        if (StringUtils.hasText(czy) && !chunkDto.czyChunk.toString().contains(czy)) {
            chunkDto.czyChunk.append(czy);
        }
        if (StringUtils.hasText(bzz) && !chunkDto.bzzChunk.toString().contains(bzz)) {
            chunkDto.bzzChunk.append(bzz);
        }
        if (StringUtils.hasText(jyy) && !chunkDto.jyyChunk.toString().contains(jyy)) {
            chunkDto.jyyChunk.append(jyy);
        }
    }

    /**
     * 计算文本实际需要的行数
     * 同时考虑换行符和字符长度导致的自动换行
     *
     * @param text        文本内容
     * @param oneLineSize 单行可容纳的字符数
     * @return 实际需要的行数
     */
    public int calculateActualLineCount(String text, double oneLineSize) {
        if (!StringUtils.hasText(text)) {
            return 1; // 空文本至少占一行
        }



        // 按换行符分割文本，-1参数保留空行
        String[] lines = text.split("\n", -1);

        int totalLines = 0;

        for (String line : lines) {
            if (line.isEmpty()) {
                totalLines += 1; // 空行占一行
            } else {
                // 计算该行需要的行数（考虑字符长度）
                int count = calcEnWord(line);
                int lineLength = line.length() - (count/2); //英文字符数字英文符合两个字符占一个配置的长度
                int lineCount = (int) Math.ceil(lineLength / oneLineSize);
                totalLines += Math.max(1, lineCount); // 至少占一行
            }
        }

        return totalLines;
    }

    private int calcEnWord(String text) {
        int count = 0;

        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            if ((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || (c >= '0' && c <= '9') || isPunc(c)) {
                count++;
            }
        }
        return count;
    }

    private static String PUNC =",.-+~()!;:'\"/<>[]{}@#*&%";

    private boolean isPunc(char c) {
        return PUNC.indexOf(c) != -1;
    }

    /**
     * 计算检查项目的高度
     *
     * @param checkItem            检查项目
     * @param checkTypeName        检查类型名称
     * @param lineHeight           行高
     * @param oneLineSize          单行可容纳的字符数
     * @param checkProcessLineSize 检查类型列可容纳的字符数
     * @return 检查项目的高度
     */
    private double calculateCheckItemHeight(WorkCheckVo checkItem, String checkTypeName,
                                            double lineHeight, double oneLineSize,
                                            double checkProcessLineSize, boolean useProcess) {
        double thisLineHeight = 0;

        // 计算实际需要的行数（考虑换行符和字符长度）
        int actualLineCount = calculateActualLineCount(checkItem.getCheckName(), oneLineSize);
        thisLineHeight = lineHeight * actualLineCount;

        if (useProcess) {
            // 增加对checkTypeName高度的计算，选取两个最大的值作为行高
            int checkTypeNameLineCount = calculateActualLineCount(checkTypeName, checkProcessLineSize);
            double checkTypeNameHeight = lineHeight * checkTypeNameLineCount;
            thisLineHeight = Math.max(thisLineHeight, checkTypeNameHeight);
        }
        if (checkItem.getRowHeight() != null) {
            thisLineHeight = Math.max(thisLineHeight, checkItem.getRowHeight());
        }

        return thisLineHeight;
    }

    private double calculateCheckProcessHeight(String checkTypeName,
                                               double lineHeight, double checkProcessLineSize) {


        // checkTypeName高度的计算
        int checkTypeNameLineCount = calculateActualLineCount(checkTypeName, checkProcessLineSize);

        if (checkTypeName.indexOf("（") != -1 && checkTypeNameLineCount == 2) {
            int count = calcEnWord(checkTypeName);
            if (count >= 12) {
                checkTypeNameLineCount++;
            }
        }
        return Math.ceil(lineHeight * checkTypeNameLineCount);
    }

}
