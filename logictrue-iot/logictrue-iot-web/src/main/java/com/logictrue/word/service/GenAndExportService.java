package com.logictrue.word.service;

import com.alibaba.fastjson2.JSON;
import com.logictrue.common.core.utils.StringUtils;
import com.logictrue.word.dto.table.CoverDataDto;
import com.logictrue.word.dto.table.TableDataDto;
import com.logictrue.word.entity.DesignWord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class GenAndExportService {

    @Autowired
    private IWordExportService wordExportService;

    public byte[] genAndExport(List<TableDataDto> tableDataDtoList) throws Exception {
        List<DesignWord> designWordList = new ArrayList<>();
        List<Integer> templateIdList = new ArrayList<>();
        for (int i = 0; i < tableDataDtoList.size(); i++) {
            TableDataDto tableDataDto = tableDataDtoList.get(i);

            // 创建DesignWord对象
            DesignWord designWord = new DesignWord();
            designWord.setTitle((i > 0 ? " - 第" + (i + 1) + "部分" : ""));
            designWord.setStatus(1);
            designWord.setPageName("检验记录表" + (i > 0 ? " - 第" + (i + 1) + "部分" : ""));
            designWord.setPageOrder(i + 1);
            designWord.setTotalPages(tableDataDtoList.size());
            designWord.setIsActive(1);
            designWord.setHasNestedTable(tableDataDto.isHasNestedTable() ? 1 : 0);

            if (tableDataDto instanceof CoverDataDto) {
                CoverDataDto coverDataDto = (CoverDataDto) tableDataDto;
                designWord.setCoverData(JSON.toJSONString(coverDataDto));
            }else {
                Map<Integer, List<Integer>> checkIdMap = tableDataDto.getCheckIdMap();
                templateIdList.addAll(checkIdMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
                designWord.setRowTemplateBindings(JSON.toJSONString(checkIdMap));
                designWord.setBoundTemplateIds(StringUtils.join(templateIdList, ","));
                // 转换表格配置和数据为JSON
                String tableConfigJson = JSON.toJSONString(tableDataDto.getHeaderConfig());
                String tableDataJson = JSON.toJSONString(tableDataDto);

                designWord.setTableConfig(tableConfigJson);
                designWord.setTableData(tableDataJson);
            }
            designWordList.add(designWord);
        }

        return wordExportService.mergeDesignWordsToSingleWord(designWordList);

    }


}
