package com.logictrue.word.service;

import com.logictrue.word.dto.SimpleWordExportRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.Units;
import org.apache.poi.wp.usermodel.HeaderFooterType;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPageMar;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPageSz;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSectPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STPageOrientation;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 简单Word导出服务
 */
@Slf4j
@Service
public class SimpleWordExportService {

    /**
     * 导出简单Word文档
     */
    public byte[] exportSimpleWord(SimpleWordExportRequest request) throws IOException {
        log.info("开始导出简单Word文档，标题: {}", request.getTitle());

        // 创建Word文档
        XWPFDocument document = new XWPFDocument();

        try {
            // 设置页面属性
            setPageSettings(document, request.getPageSettings());

            // 添加页眉页脚
            addHeaderFooter(document, request.getPageSettings());

            // 使用JSON结构化数据导出
            if (request.getJsonContent() != null && !request.getJsonContent().isEmpty()) {
                log.info("使用JSON结构化数据导出，节点数量: {}", request.getJsonContent().size());

                // 统计内容信息
                long imageCount = request.getJsonContent().stream()
                        .mapToLong(this::countImages)
                        .sum();
                log.info("文档包含图片数量: {}", imageCount);

                // 直接使用原始JSON结构，支持内联图片
                parseAndAddJsonContent(document, request.getJsonContent(), request.getExportConfig());
            } else {
                log.warn("没有找到有效的JSON文档内容");
                // 添加一个空段落
                XWPFParagraph paragraph = document.createParagraph();
                XWPFRun run = paragraph.createRun();
                run.setText("文档内容为空");
                run.setColor("999999");
            }

            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);

            log.info("简单Word文档导出成功，大小: {} bytes", outputStream.size());
            return outputStream.toByteArray();

        } finally {
            document.close();
        }
    }

    /**
     * 解析JSON结构化内容并添加到Word文档
     */
    private void parseAndAddJsonContent(XWPFDocument document, List<SimpleWordExportRequest.ContentNode> jsonContent,
                                        SimpleWordExportRequest.ExportConfig exportConfig) throws IOException {
        log.info("开始解析JSON结构化内容，节点数量: {}", jsonContent.size());

        for (SimpleWordExportRequest.ContentNode node : jsonContent) {
            processContentNode(document, node, exportConfig);
        }

        log.info("JSON结构化内容解析完成");
    }

    /**
     * 处理单个内容节点
     */
    private void processContentNode(XWPFDocument document, SimpleWordExportRequest.ContentNode node,
                                    SimpleWordExportRequest.ExportConfig exportConfig) throws IOException {
        if (node == null || node.getType() == null) {
            return;
        }

        String nodeType = node.getType().toLowerCase();
        log.debug("处理节点类型: {}, 内容: {}", nodeType,
                node.getContent() != null ? node.getContent().substring(0, Math.min(50, node.getContent().length())) : "无内容");

        switch (nodeType) {
            case "p":
            case "div":
                processParagraphNode(document, node, exportConfig);
                break;
            case "img":
                processImageNode(document, node, exportConfig);
                break;
            case "h1":
            case "h2":
            case "h3":
            case "h4":
            case "h5":
            case "h6":
                processHeadingNode(document, node, exportConfig);
                break;
            default:
                // 对于其他类型的节点，作为普通文本处理
                processTextNode(document, node, exportConfig);
                break;
        }
    }

    /**
     * 处理段落节点
     */
    private void processParagraphNode(XWPFDocument document, SimpleWordExportRequest.ContentNode node,
                                      SimpleWordExportRequest.ExportConfig exportConfig) throws IOException {
        log.debug("处理段落节点 - 内容: {}, 子节点数: {}",
                node.getContent() != null ? node.getContent().substring(0, Math.min(30, node.getContent().length())) : "无",
                node.getChildren() != null ? node.getChildren().size() : 0);

        // 创建段落
        XWPFParagraph paragraph = document.createParagraph();
        applyParagraphStyles(paragraph, node.getStyles());

        // 检查是否有子节点（内联元素）
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            log.debug("段落包含子节点，处理内联内容");

            // 处理真正的内联内容：在同一段落中混合文本和内联图片
            processInlineContent(paragraph, node, exportConfig);
        } else if (node.getContent() != null && !node.getContent().trim().isEmpty()) {
            // 纯文本段落
            XWPFRun run = paragraph.createRun();
            run.setText(node.getContent());
            applyRunStyles(run, node.getStyles());
            log.debug("创建纯文本段落: {}", node.getContent());
        } else {
            // 空段落
            log.debug("创建空段落");
        }
    }

    /**
     * 处理真正的内联内容（文本和图片在同一行，保持正确位置）
     */
    private void processInlineContent(XWPFParagraph paragraph, SimpleWordExportRequest.ContentNode node,
                                      SimpleWordExportRequest.ExportConfig exportConfig) throws IOException {
        log.debug("处理内联内容 - 段落文本: {}, 子节点数: {}",
                node.getContent() != null ? node.getContent().substring(0, Math.min(30, node.getContent().length())) : "无",
                node.getChildren() != null ? node.getChildren().size() : 0);

        String fullText = node.getContent() != null ? node.getContent() : "";
        List<SimpleWordExportRequest.ContentNode> inlineElements = node.getChildren();

        // 如果只有文本没有内联元素，直接处理
        if (inlineElements == null || inlineElements.isEmpty()) {
            if (!fullText.trim().isEmpty()) {
                XWPFRun textRun = paragraph.createRun();
                textRun.setText(fullText);
                applyRunStyles(textRun, node.getStyles());
            }
            return;
        }

        // 如果只有内联元素没有文本，直接处理内联元素
        if (fullText.trim().isEmpty()) {
            for (SimpleWordExportRequest.ContentNode child : inlineElements) {
                if ("img".equals(child.getType())) {
                    processInlineImageInSameParagraph(paragraph, child, exportConfig);
                } else if ("br".equals(child.getType())) {
                    processBreakNode(paragraph, node, exportConfig);
                } else {
                    processInlineNode(paragraph, child, exportConfig);
                }
            }
            return;
        }

        // 混合内容：需要智能分割文本和内联元素
        processTextWithInlineElementsInCorrectOrder(paragraph, fullText, inlineElements, node, exportConfig);
    }

    /**
     * 按正确顺序处理文本和内联元素的混合内容
     */
    private void processTextWithInlineElementsInCorrectOrder(XWPFParagraph paragraph, String fullText,
                                                             List<SimpleWordExportRequest.ContentNode> inlineElements,
                                                             SimpleWordExportRequest.ContentNode parentNode,
                                                             SimpleWordExportRequest.ExportConfig exportConfig) throws IOException {
        log.debug("处理混合内容 - 全文长度: {}, 内联元素数: {}", fullText.length(), inlineElements.size());

        // 简化处理：假设内联图片在文本中间
        // 这是一个启发式方法，适用于大多数情况

        if (inlineElements.size() == 1 && "img".equals(inlineElements.get(0).getType())) {
            // 单个内联图片的情况，尝试智能分割
            processSingleInlineImageInText(paragraph, fullText, inlineElements.get(0), parentNode, exportConfig);
        } else {
            // 多个内联元素或复杂情况，使用默认处理
            // 先添加文本，再添加内联元素
            XWPFRun textRun = paragraph.createRun();
            textRun.setText(fullText);
            applyRunStyles(textRun, parentNode.getStyles());

            for (SimpleWordExportRequest.ContentNode child : inlineElements) {
                if ("img".equals(child.getType())) {
                    processInlineImageInSameParagraph(paragraph, child, exportConfig);
                } else {
                    processInlineNode(paragraph, child, exportConfig);
                }
            }
        }
    }

    /**
     * 处理包含单个内联图片的文本
     */
    private void processSingleInlineImageInText(XWPFParagraph paragraph, String fullText,
                                                SimpleWordExportRequest.ContentNode imageNode,
                                                SimpleWordExportRequest.ContentNode parentNode,
                                                SimpleWordExportRequest.ExportConfig exportConfig) throws IOException {

        // 尝试从图片属性中获取位置信息
        String textPosition = null;
        if (imageNode.getAttributes() != null) {
            textPosition = imageNode.getAttributes().get("textPosition");
        }

        int splitPosition = -1;
        if (textPosition != null) {
            try {
                splitPosition = Integer.parseInt(textPosition);
                log.debug("使用位置信息分割文本，位置: {}", splitPosition);
            } catch (NumberFormatException e) {
                log.debug("位置信息格式错误: {}", textPosition);
            }
        }

        // 如果没有位置信息，使用启发式方法
        if (splitPosition < 0 || splitPosition > fullText.length()) {
            // 尝试在合适的位置分割（空格、标点符号等）
            splitPosition = findBestSplitPosition(fullText);
            log.debug("使用启发式方法确定分割位置: {}", splitPosition);
        }

        // 分割文本
        String beforeText = fullText.substring(0, splitPosition);
        String afterText = fullText.substring(splitPosition);

        log.debug("分割文本 - 前: '{}', 后: '{}'", beforeText, afterText);

        // 按顺序添加：前文本 + 图片 + 后文本
        if (!beforeText.isEmpty()) {
            XWPFRun beforeRun = paragraph.createRun();
            beforeRun.setText(beforeText);
            applyRunStyles(beforeRun, parentNode.getStyles());
            log.debug("添加前置文本: {}", beforeText);
        }

        // 添加内联图片
        processInlineImageInSameParagraph(paragraph, imageNode, exportConfig);
        log.debug("添加内联图片");

        if (!afterText.isEmpty()) {
            XWPFRun afterRun = paragraph.createRun();
            afterRun.setText(afterText);
            applyRunStyles(afterRun, parentNode.getStyles());
            log.debug("添加后置文本: {}", afterText);
        }
    }

    /**
     * 寻找最佳的文本分割位置
     */
    private int findBestSplitPosition(String text) {
        int length = text.length();
        int midPoint = length / 2;

        // 在中点附近寻找合适的分割位置
        int searchRange = Math.min(10, length / 4); // 搜索范围

        // 优先在空格处分割
        for (int i = 0; i <= searchRange; i++) {
            // 向后搜索
            int pos = midPoint + i;
            if (pos < length && text.charAt(pos) == ' ') {
                return pos + 1; // 空格后面
            }

            // 向前搜索
            pos = midPoint - i;
            if (pos >= 0 && text.charAt(pos) == ' ') {
                return pos + 1; // 空格后面
            }
        }

        // 如果没找到空格，在标点符号处分割
        for (int i = 0; i <= searchRange; i++) {
            int pos = midPoint + i;
            if (pos < length && isPunctuation(text.charAt(pos))) {
                return pos + 1;
            }

            pos = midPoint - i;
            if (pos >= 0 && isPunctuation(text.charAt(pos))) {
                return pos + 1;
            }
        }

        // 最后使用中点
        return midPoint;
    }

    /**
     * 判断是否为标点符号
     */
    private boolean isPunctuation(char c) {
        return c == '，' || c == '。' || c == '！' || c == '？' || c == '；' || c == '：' ||
                c == ',' || c == '.' || c == '!' || c == '?' || c == ';' || c == ':';
    }

    /**
     * 在同一段落中处理内联图片
     */
    private void processInlineImageInSameParagraph(XWPFParagraph paragraph, SimpleWordExportRequest.ContentNode node,
                                                   SimpleWordExportRequest.ExportConfig exportConfig) throws IOException {
        if (exportConfig == null || !Boolean.TRUE.equals(exportConfig.getIncludeImages())) {
            log.info("跳过内联图片处理，配置不允许包含图片");
            // 添加替代文本
            XWPFRun run = paragraph.createRun();
            run.setText("[图片]");
            run.setColor("999999");
            return;
        }

        String imageSrc = null;
        if (node.getAttributes() != null) {
            imageSrc = node.getAttributes().get("src");
        }

        if (imageSrc == null || imageSrc.trim().isEmpty()) {
            log.warn("内联图片节点没有src属性，跳过处理");
            return;
        }

        try {
            String imageInfo = getImageInfo(imageSrc, node);
            log.info("开始处理同行内联图片 - {}", imageInfo);

            if (imageSrc.startsWith("data:image/")) {
                processInlineBase64ImageInSameParagraph(paragraph, imageSrc, node, exportConfig);
                log.info("同行内联图片处理完成 - {}", imageInfo);
            } else {
                log.warn("暂不支持非Base64格式的内联图片");
                // 添加替代文本
                XWPFRun run = paragraph.createRun();
                run.setText("[图片]");
                run.setColor("999999");
            }

        } catch (Exception e) {
            log.error("处理同行内联图片时发生错误", e);
            // 添加错误提示文本
            XWPFRun run = paragraph.createRun();
            run.setText("[图片加载失败]");
            run.setColor("FF0000");
        }
    }

    /**
     * 在同一段落中处理Base64内联图片
     */
    private void processInlineBase64ImageInSameParagraph(XWPFParagraph paragraph, String base64Data,
                                                         SimpleWordExportRequest.ContentNode node,
                                                         SimpleWordExportRequest.ExportConfig exportConfig) throws IOException {
        try {
            // 解析Base64数据
            String[] parts = base64Data.split(",");
            if (parts.length != 2) {
                throw new IOException("内联图片Base64格式不正确");
            }

            String mimeType = parts[0];
            String base64Content = parts[1];

            // 确定图片格式
            ImageTypeInfo imageTypeInfo = determineImageType(mimeType);
            log.debug("同行内联图片类型识别 - 格式: {}", imageTypeInfo.getFormat());

            // 解码Base64数据
            byte[] imageBytes = Base64.getDecoder().decode(base64Content);
            log.debug("同行内联图片解码成功，大小: {} bytes", imageBytes.length);

            // 计算内联图片尺寸（保持较小以适应行高）
            ImageDimensions dimensions = calculateInlineImageDimensions(node, exportConfig);
            int width = dimensions.getWidth();
            int height = dimensions.getHeight();

            log.debug("同行内联图片尺寸: {}x{}, 来源: {}", width, height, dimensions.getSource());

            // 在同一段落中创建运行并插入图片
            XWPFRun run = paragraph.createRun();

            try (ByteArrayInputStream imageStream = new ByteArrayInputStream(imageBytes)) {
                run.addPicture(imageStream, imageTypeInfo.getWordType(), "inline_image",
                        Units.toEMU(width), Units.toEMU(height));
                log.info("同行内联图片插入成功 - 格式: {}, 尺寸: {}x{}", imageTypeInfo.getFormat(), width, height);
            }

        } catch (Exception e) {
            log.error("处理同行内联Base64图片时发生错误", e);
            throw new IOException("同行内联图片处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理包含内联元素的文本内容
     * 这个方法尝试根据DOM结构重建文本和内联元素的正确顺序
     */
    private void processTextWithInlineElements(XWPFParagraph paragraph, SimpleWordExportRequest.ContentNode node,
                                               SimpleWordExportRequest.ExportConfig exportConfig) throws IOException {
        String fullText = node.getContent();
        List<SimpleWordExportRequest.ContentNode> inlineElements = node.getChildren();

        log.debug("处理文本与内联元素 - 全文: {}, 内联元素数: {}",
                fullText.substring(0, Math.min(50, fullText.length())), inlineElements.size());

        // 简化处理：由于前端JSON结构的限制，我们采用以下策略：
        // 1. 如果文本很短且只有一个内联元素，可能是在中间插入
        // 2. 否则按照：文本 + 内联元素的顺序处理

        if (fullText.length() < 100 && inlineElements.size() == 1) {
            // 尝试智能分割（简化版本）
            processTextWithSingleInlineElement(paragraph, fullText, inlineElements.get(0), node, exportConfig);
        } else {
            // 默认处理：先文本，后内联元素
            XWPFRun textRun = paragraph.createRun();
            textRun.setText(fullText);
            applyRunStyles(textRun, node.getStyles());
            log.debug("添加完整文本: {}", fullText.substring(0, Math.min(30, fullText.length())));

            // 然后添加内联元素
            for (SimpleWordExportRequest.ContentNode child : inlineElements) {
                if ("img".equals(child.getType())) {
                    processInlineImage(paragraph, child, exportConfig);
                } else {
                    processInlineNode(paragraph, child, exportConfig);
                }
            }
        }
    }

    /**
     * 处理包含单个内联元素的文本
     */
    private void processTextWithSingleInlineElement(XWPFParagraph paragraph, String fullText,
                                                    SimpleWordExportRequest.ContentNode inlineElement,
                                                    SimpleWordExportRequest.ContentNode parentNode,
                                                    SimpleWordExportRequest.ExportConfig exportConfig) throws IOException {

        // 由于JSON结构的限制，我们无法准确知道内联元素在文本中的确切位置
        // 这里采用启发式方法：
        // 1. 检查是否有位置信息
        // 2. 如果没有，假设在文本中间

        String beforeText = "";
        String afterText = "";

        // 尝试从属性中获取位置信息
        if (inlineElement.getAttributes() != null) {
            String positionHint = inlineElement.getAttributes().get("textPosition");
            if (positionHint != null) {
                try {
                    int position = Integer.parseInt(positionHint);
                    if (position >= 0 && position <= fullText.length()) {
                        beforeText = fullText.substring(0, position);
                        afterText = fullText.substring(position);
                        log.debug("使用位置提示分割文本 - 位置: {}, 前: '{}', 后: '{}'",
                                position, beforeText, afterText);
                    }
                } catch (NumberFormatException e) {
                    log.debug("位置提示格式错误: {}", positionHint);
                }
            }
        }

        // 如果没有位置信息，使用默认分割策略
        if (beforeText.isEmpty() && afterText.isEmpty()) {
            int midPoint = fullText.length() / 2;
            // 尝试在空格处分割
            int spaceIndex = fullText.indexOf(' ', midPoint);
            if (spaceIndex > 0 && spaceIndex < fullText.length() - 1) {
                beforeText = fullText.substring(0, spaceIndex + 1);
                afterText = fullText.substring(spaceIndex + 1);
            } else {
                // 简单的中点分割
                beforeText = fullText.substring(0, midPoint);
                afterText = fullText.substring(midPoint);
            }
            log.debug("使用默认分割策略 - 前: '{}', 后: '{}'", beforeText, afterText);
        }

        // 按顺序添加：前文本 + 内联元素 + 后文本
        if (!beforeText.isEmpty()) {
            XWPFRun beforeRun = paragraph.createRun();
            beforeRun.setText(beforeText);
            applyRunStyles(beforeRun, parentNode.getStyles());
            log.debug("添加前置文本: {}", beforeText);
        }

        // 添加内联元素
        if ("img".equals(inlineElement.getType())) {
            processInlineImage(paragraph, inlineElement, exportConfig);
            log.debug("添加内联图片");
        } else {
            processInlineNode(paragraph, inlineElement, exportConfig);
            log.debug("添加内联元素: {}", inlineElement.getType());
        }

        if (!afterText.isEmpty()) {
            XWPFRun afterRun = paragraph.createRun();
            afterRun.setText(afterText);
            applyRunStyles(afterRun, parentNode.getStyles());
            log.debug("添加后置文本: {}", afterText);
        }
    }


    /**
     * 处理内联图片
     */
    private void processInlineImage(XWPFParagraph paragraph, SimpleWordExportRequest.ContentNode node,
                                    SimpleWordExportRequest.ExportConfig exportConfig) throws IOException {
        if (exportConfig == null || !Boolean.TRUE.equals(exportConfig.getIncludeImages())) {
            log.info("跳过内联图片处理，配置不允许包含图片");
            return;
        }

        String imageSrc = null;
        if (node.getAttributes() != null) {
            imageSrc = node.getAttributes().get("src");
        }

        if (imageSrc == null || imageSrc.trim().isEmpty()) {
            log.warn("内联图片节点没有src属性，跳过处理");
            return;
        }

        try {
            String imageInfo = getImageInfo(imageSrc, node);
            log.info("开始处理内联图片 - {}", imageInfo);

            if (imageSrc.startsWith("data:image/")) {
                processInlineBase64Image(paragraph, imageSrc, node, exportConfig);
                log.info("内联图片处理完成 - {}", imageInfo);
            } else {
                log.warn("暂不支持非Base64格式的内联图片");
                // 添加替代文本
                XWPFRun run = paragraph.createRun();
                run.setText("[图片]");
                run.setColor("999999");
            }

        } catch (Exception e) {
            log.error("处理内联图片时发生错误", e);
            // 添加错误提示文本
            XWPFRun run = paragraph.createRun();
            run.setText("[图片加载失败]");
            run.setColor("FF0000");
        }
    }

    /**
     * 处理内联Base64图片
     */
    private void processInlineBase64Image(XWPFParagraph paragraph, String base64Data,
                                          SimpleWordExportRequest.ContentNode node,
                                          SimpleWordExportRequest.ExportConfig exportConfig) throws IOException {
        try {
            // 解析Base64数据
            String[] parts = base64Data.split(",");
            if (parts.length != 2) {
                throw new IOException("内联图片Base64格式不正确");
            }

            String mimeType = parts[0];
            String base64Content = parts[1];

            // 确定图片格式
            ImageTypeInfo imageTypeInfo = determineImageType(mimeType);
            log.debug("内联图片类型识别 - 格式: {}", imageTypeInfo.getFormat());

            // 解码Base64数据
            byte[] imageBytes = Base64.getDecoder().decode(base64Content);
            log.debug("内联图片解码成功，大小: {} bytes", imageBytes.length);

            // 计算内联图片尺寸（通常较小）
            ImageDimensions dimensions = calculateInlineImageDimensions(node, exportConfig);
            int width = dimensions.getWidth();
            int height = dimensions.getHeight();

            log.debug("内联图片尺寸: {}x{}, 来源: {}", width, height, dimensions.getSource());

            // 创建运行并插入图片
            XWPFRun run = paragraph.createRun();

            try (ByteArrayInputStream imageStream = new ByteArrayInputStream(imageBytes)) {
                run.addPicture(imageStream, imageTypeInfo.getWordType(), "inline_image",
                        Units.toEMU(width), Units.toEMU(height));
                log.info("内联图片插入成功 - 格式: {}, 尺寸: {}x{}", imageTypeInfo.getFormat(), width, height);
            }

        } catch (Exception e) {
            log.error("处理内联Base64图片时发生错误", e);
            throw new IOException("内联图片处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 计算内联图片尺寸
     */
    private ImageDimensions calculateInlineImageDimensions(SimpleWordExportRequest.ContentNode node,
                                                           SimpleWordExportRequest.ExportConfig exportConfig) {
        // 内联图片默认较小尺寸
        int width = 24;  // 默认24像素，约等于1.5em
        int height = 24;
        String source = "内联默认值";

        // 从样式中获取尺寸
        if (node.getStyles() != null) {
            String styleWidth = node.getStyles().get("width");
            String styleHeight = node.getStyles().get("height");
            String maxHeight = node.getStyles().get("maxHeight");

            log.debug("解析内联图片样式 - width: {}, height: {}, maxHeight: {}", styleWidth, styleHeight, maxHeight);

            // 优先使用maxHeight（通常用于内联图片）
            if (maxHeight != null && !maxHeight.trim().isEmpty()) {
                Integer parsedMaxHeight = parseInlineDimension(maxHeight);
                if (parsedMaxHeight != null) {
                    height = parsedMaxHeight;
                    width = height; // 内联图片通常保持正方形
                    source = "CSS maxHeight";
                    log.debug("从maxHeight解析内联图片尺寸: {}x{}", width, height);
                }
            } else {
                // 解析width和height
                if (styleWidth != null && !styleWidth.trim().isEmpty()) {
                    Integer parsedWidth = parseInlineDimension(styleWidth);
                    if (parsedWidth != null) {
                        width = parsedWidth;
                        source = "CSS样式";
                    }
                }

                if (styleHeight != null && !styleHeight.trim().isEmpty()) {
                    Integer parsedHeight = parseInlineDimension(styleHeight);
                    if (parsedHeight != null) {
                        height = parsedHeight;
                        source = "CSS样式";
                    }
                }
            }
        }

        // 内联图片尺寸限制（通常不超过行高）
        final int maxInlineSize = 32; // 最大32像素
        final int minInlineSize = 12; // 最小12像素

        width = Math.max(minInlineSize, Math.min(width, maxInlineSize));
        height = Math.max(minInlineSize, Math.min(height, maxInlineSize));

        return new ImageDimensions(width, height, source);
    }

    /**
     * 解析内联图片尺寸（支持em单位）
     */
    private Integer parseInlineDimension(String dimensionStr) {
        if (dimensionStr == null || dimensionStr.trim().isEmpty()) {
            return null;
        }

        String cleanStr = dimensionStr.trim().toLowerCase();

        try {
            // 处理em单位（假设1em = 16px）
            if (cleanStr.endsWith("em")) {
                double em = Double.parseDouble(cleanStr.replace("em", ""));
                return (int) Math.round(em * 16);
            }

            // 处理像素单位
            if (cleanStr.endsWith("px")) {
                return Integer.parseInt(cleanStr.replace("px", ""));
            }

            // 处理纯数字
            return Integer.parseInt(cleanStr);

        } catch (NumberFormatException e) {
            log.warn("无法解析内联图片尺寸: {}", dimensionStr);
            return null;
        }
    }

    /**
     * 处理图片节点
     */
    private void processImageNode(XWPFDocument document, SimpleWordExportRequest.ContentNode node,
                                  SimpleWordExportRequest.ExportConfig exportConfig) throws IOException {
        if (exportConfig == null || !Boolean.TRUE.equals(exportConfig.getIncludeImages())) {
            log.info("跳过图片处理，配置不允许包含图片");
            return;
        }

        String imageSrc = null;
        if (node.getAttributes() != null) {
            imageSrc = node.getAttributes().get("src");
        }

        if (imageSrc == null || imageSrc.trim().isEmpty()) {
            log.warn("图片节点没有src属性，跳过处理");
            return;
        }

        try {
            // 记录图片基本信息
            String imageInfo = getImageInfo(imageSrc, node);
            log.info("开始处理图片 - {}", imageInfo);

            // 处理Base64图片
            if (imageSrc.startsWith("data:image/")) {
                processBase64Image(document, imageSrc, node, exportConfig);
                log.info("图片处理完成 - {}", imageInfo);
            } else {
                log.warn("暂不支持非Base64格式的图片: {}", imageSrc.substring(0, Math.min(50, imageSrc.length())));
                addImageErrorPlaceholder(document, "不支持的图片格式");
            }

        } catch (Exception e) {
            log.error("处理图片时发生错误", e);
            addImageErrorPlaceholder(document, "图片加载失败: " + e.getMessage());
        }
    }

    /**
     * 处理换行节点
     */
    private void processBreakNode(XWPFParagraph paragraph, SimpleWordExportRequest.ContentNode node,
                                  SimpleWordExportRequest.ExportConfig exportConfig) throws IOException {
        Map<String, String> styles = node.getStyles();
        String fontSize = styles.get("fontSize");
        if (fontSize != null && fontSize.endsWith("px")) {

            int size = Integer.parseInt(fontSize.replace("px", ""));
            // 将像素转换为磅值（大致转换）
            int points = Math.max(8, Math.min(72, size * 3 / 4));
            XWPFRun run = paragraph.createRun();
            run.setFontSize(points);
            run.setText("");
            run.addBreak();
        }
    }

    /**
     * 获取图片信息用于日志记录
     */
    private String getImageInfo(String imageSrc, SimpleWordExportRequest.ContentNode node) {
        StringBuilder info = new StringBuilder();

        // Base64数据大小
        if (imageSrc.contains(",")) {
            String base64Data = imageSrc.split(",")[1];
            long estimatedSize = (base64Data.length() * 3L) / 4L;
            info.append("大小: ").append(formatFileSize(estimatedSize));
        }

        // 图片尺寸信息
        if (node.getStyles() != null) {
            String width = node.getStyles().get("width");
            String height = node.getStyles().get("height");
            if (width != null || height != null) {
                info.append(", 显示尺寸: ").append(width != null ? width : "auto")
                        .append("x").append(height != null ? height : "auto");
            }
        }

        // 原始尺寸信息
        if (node.getAttributes() != null) {
            String naturalWidth = node.getAttributes().get("naturalWidth");
            String naturalHeight = node.getAttributes().get("naturalHeight");
            String aspectRatio = node.getAttributes().get("aspectRatio");

            if (naturalWidth != null && naturalHeight != null) {
                info.append(", 原始尺寸: ").append(naturalWidth).append("x").append(naturalHeight);
            }

            if (aspectRatio != null) {
                info.append(", 宽高比: ").append(aspectRatio);
            }
        }

        // 图片格式
        if (imageSrc.startsWith("data:image/")) {
            String mimeType = imageSrc.substring(0, imageSrc.indexOf(";"));
            String format = mimeType.substring(mimeType.lastIndexOf("/") + 1).toUpperCase();
            info.append(", 格式: ").append(format);
        }

        return info.toString();
    }

    /**
     * 添加图片错误占位符
     */
    private void addImageErrorPlaceholder(XWPFDocument document, String errorMessage) {
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText("[" + errorMessage + "]");
        run.setColor("FF0000"); // 红色
        run.setItalic(true);
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + "B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1fKB", bytes / 1024.0);
        } else {
            return String.format("%.1fMB", bytes / (1024.0 * 1024.0));
        }
    }

    /**
     * 处理Base64格式的图片
     */
    private void processBase64Image(XWPFDocument document, String base64Data,
                                    SimpleWordExportRequest.ContentNode node,
                                    SimpleWordExportRequest.ExportConfig exportConfig) throws IOException {
        try {
            // 解析Base64数据
            String[] parts = base64Data.split(",");
            if (parts.length != 2) {
                throw new IOException("Base64图片格式不正确，缺少逗号分隔符");
            }

            String mimeType = parts[0]; // data:image/jpeg;base64
            String base64Content = parts[1];

            log.debug("解析Base64图片 - MIME类型: {}, Base64长度: {}", mimeType, base64Content.length());

            // 验证MIME类型
            if (!mimeType.startsWith("data:image/") || !mimeType.contains("base64")) {
                throw new IOException("无效的Base64图片MIME类型: " + mimeType);
            }

            // 确定图片格式和对应的Word图片类型
            ImageTypeInfo imageTypeInfo = determineImageType(mimeType);
            log.debug("图片类型识别 - 格式: {}, Word类型: {}", imageTypeInfo.getFormat(), imageTypeInfo.getWordType());

            // 验证Base64内容
            if (base64Content.trim().isEmpty()) {
                throw new IOException("Base64图片内容为空");
            }

            // 解码Base64数据
            byte[] imageBytes = Base64.getDecoder().decode(base64Content);
            log.info("图片解码成功，大小: {} bytes ({})", imageBytes.length, formatFileSize(imageBytes.length));

            // 创建段落和运行
            XWPFParagraph paragraph = document.createParagraph();
            XWPFRun run = paragraph.createRun();

            // 计算图片尺寸
            ImageDimensions dimensions = calculateImageDimensions(node, exportConfig);
            int width = dimensions.getWidth();
            int height = dimensions.getHeight();

            log.info("图片尺寸计算完成 - 宽度: {}px, 高度: {}px, 来源: {}", width, height, dimensions.getSource());

            // 插入图片到Word文档
            try (ByteArrayInputStream imageStream = new ByteArrayInputStream(imageBytes)) {
                run.addPicture(imageStream, imageTypeInfo.getWordType(), "image",
                        Units.toEMU(width), Units.toEMU(height));
                log.info("图片插入成功 - 格式: {}, 尺寸: {}x{}", imageTypeInfo.getFormat(), width, height);
            }

        } catch (Exception e) {
            log.error("处理Base64图片时发生错误", e);
            throw new IOException("图片处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理标题节点
     */
    private void processHeadingNode(XWPFDocument document, SimpleWordExportRequest.ContentNode node,
                                    SimpleWordExportRequest.ExportConfig exportConfig) {
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();

        if (node.getContent() != null) {
            run.setText(node.getContent());
        }

        // 根据标题级别设置字体大小
        String nodeType = node.getType().toLowerCase();
        switch (nodeType) {
            case "h1":
                run.setFontSize(24);
                run.setBold(true);
                break;
            case "h2":
                run.setFontSize(20);
                run.setBold(true);
                break;
            case "h3":
                run.setFontSize(18);
                run.setBold(true);
                break;
            case "h4":
                run.setFontSize(16);
                run.setBold(true);
                break;
            case "h5":
                run.setFontSize(14);
                run.setBold(true);
                break;
            case "h6":
                run.setFontSize(12);
                run.setBold(true);
                break;
        }

        // 应用自定义样式
        applyRunStyles(run, node.getStyles());
    }

    /**
     * 处理文本节点
     */
    private void processTextNode(XWPFDocument document, SimpleWordExportRequest.ContentNode node,
                                 SimpleWordExportRequest.ExportConfig exportConfig) {
        if (node.getContent() == null || node.getContent().trim().isEmpty()) {
            return;
        }

        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText(node.getContent());

        // 应用样式
        applyRunStyles(run, node.getStyles());
    }

    /**
     * 处理内联节点（span等）
     */
    private void processInlineNode(XWPFParagraph paragraph, SimpleWordExportRequest.ContentNode node,
                                   SimpleWordExportRequest.ExportConfig exportConfig) {
        if (node.getContent() == null || node.getContent().trim().isEmpty()) {
            return;
        }

        XWPFRun run = paragraph.createRun();
        run.setText(node.getContent());

        // 应用样式
        applyRunStyles(run, node.getStyles());
    }

    /**
     * 应用段落样式
     */
    private void applyParagraphStyles(XWPFParagraph paragraph, Map<String, String> styles) {
        if (styles == null || styles.isEmpty()) {
            return;
        }

        // 文本对齐
        String textAlign = styles.get("textAlign");
        if (textAlign != null) {
            switch (textAlign.toLowerCase()) {
                case "center":
                    paragraph.setAlignment(ParagraphAlignment.CENTER);
                    break;
                case "right":
                    paragraph.setAlignment(ParagraphAlignment.RIGHT);
                    break;
                case "justify":
                    paragraph.setAlignment(ParagraphAlignment.BOTH);
                    break;
                default:
                    paragraph.setAlignment(ParagraphAlignment.LEFT);
                    break;
            }
        }

        // 行间距
        String lineHeight = styles.get("lineHeight");
        if (lineHeight != null) {
            try {
                double spacing = Double.parseDouble(lineHeight);
                paragraph.setSpacingBetween(spacing);
            } catch (NumberFormatException e) {
                log.warn("无法解析行间距: {}", lineHeight);
            }
        }
    }

    /**
     * 应用运行样式
     */
    private void applyRunStyles(XWPFRun run, Map<String, String> styles) {
        if (styles == null || styles.isEmpty()) {
            return;
        }

        // 字体大小
        String fontSize = styles.get("fontSize");
        if (fontSize != null && fontSize.endsWith("px")) {
            try {
                int size = Integer.parseInt(fontSize.replace("px", ""));
                // 将像素转换为磅值（大致转换）
                int points = Math.max(8, Math.min(72, size * 3 / 4));
                run.setFontSize(points);
            } catch (NumberFormatException e) {
                log.warn("无法解析字体大小: {}", fontSize);
            }
        }

        // 字体族
        String fontFamily = styles.get("fontFamily");
        if (fontFamily != null) {
            run.setFontFamily(fontFamily);
        }

        // 字体颜色
        String color = styles.get("color");
        if (color != null) {
            // 移除#号
            if (color.startsWith("#")) {
                color = color.substring(1);
            }
            try {
                run.setColor(color.toUpperCase());
            } catch (Exception e) {
                log.warn("无法解析字体颜色: {}", color);
            }
        }

        // 加粗
        String fontWeight = styles.get("fontWeight");
        if ("bold".equals(fontWeight) || "700".equals(fontWeight)) {
            run.setBold(true);
        }

        // 斜体
        String fontStyle = styles.get("fontStyle");
        if ("italic".equals(fontStyle)) {
            run.setItalic(true);
        }

        // 下划线
        String textDecoration = styles.get("textDecoration");
        if (textDecoration != null && textDecoration.contains("underline")) {
            run.setUnderline(UnderlinePatterns.SINGLE);
        }

        // 删除线
        if (textDecoration != null && textDecoration.contains("line-through")) {
            run.setStrikeThrough(true);
        }
    }

    /**
     * 递归统计节点中的图片数量
     */
    private long countImages(SimpleWordExportRequest.ContentNode node) {
        if (node == null) {
            return 0;
        }

        long count = 0;

        // 如果当前节点是图片
        if ("img".equals(node.getType())) {
            count = 1;
        }

        // 递归统计子节点中的图片
        if (node.getChildren() != null) {
            count += node.getChildren().stream()
                    .mapToLong(this::countImages)
                    .sum();
        }

        return count;
    }

    /**
     * 修复JSON数据结构问题（临时解决方案）
     * 将段落中的图片子节点提取为独立节点，保持原始顺序
     */
    private List<SimpleWordExportRequest.ContentNode> fixJsonStructure(List<SimpleWordExportRequest.ContentNode> originalNodes) {
        List<SimpleWordExportRequest.ContentNode> fixedNodes = new ArrayList<>();

        for (SimpleWordExportRequest.ContentNode node : originalNodes) {
            if ("p".equals(node.getType()) && node.getChildren() != null && !node.getChildren().isEmpty()) {
                log.debug("修复段落节点结构 - 原始子节点数: {}", node.getChildren().size());

                // 检查是否有图片子节点
                boolean hasImageChildren = node.getChildren().stream()
                        .anyMatch(child -> "img".equals(child.getType()));

                if (hasImageChildren) {
                    log.debug("发现包含图片的段落，按顺序重新组织结构");

                    // 按照原始顺序处理：先处理段落文本，再按子节点顺序处理

                    // 1. 如果段落有文本内容，先创建文本段落
                    if (node.getContent() != null && !node.getContent().trim().isEmpty()) {
                        // 收集非图片子节点
                        List<SimpleWordExportRequest.ContentNode> nonImageChildren = node.getChildren().stream()
                                .filter(child -> !"img".equals(child.getType()))
                                .collect(Collectors.toList());

                        SimpleWordExportRequest.ContentNode textNode = createTextNode(node, nonImageChildren);
                        fixedNodes.add(textNode);
                        log.debug("创建文本节点: {}", node.getContent().substring(0, Math.min(30, node.getContent().length())));
                    }

                    // 2. 按原始顺序处理子节点
                    for (SimpleWordExportRequest.ContentNode child : node.getChildren()) {
                        if ("img".equals(child.getType())) {
                            // 图片节点作为独立节点添加
                            child.setId("img_" + System.currentTimeMillis() + "_" + Math.random());
                            fixedNodes.add(child);
                            log.debug("按顺序添加图片节点: {}", child.getId());
                        } else {
                            // 其他子节点如果不是纯文本，也作为独立节点添加
                            if (child.getContent() != null && !child.getContent().trim().isEmpty() &&
                                    !"span".equals(child.getType())) {
                                fixedNodes.add(child);
                                log.debug("按顺序添加其他子节点: {} - {}", child.getType(),
                                        child.getContent().substring(0, Math.min(20, child.getContent().length())));
                            }
                        }
                    }
                } else {
                    // 没有图片子节点，直接添加原节点
                    fixedNodes.add(node);
                }
            } else {
                // 非段落节点或没有子节点的段落，直接添加
                fixedNodes.add(node);
            }
        }

        log.info("JSON结构修复完成 - 原始节点数: {}, 修复后节点数: {}", originalNodes.size(), fixedNodes.size());

        // 输出修复后的节点顺序用于调试
        for (int i = 0; i < fixedNodes.size(); i++) {
            SimpleWordExportRequest.ContentNode fixedNode = fixedNodes.get(i);
            String content = fixedNode.getContent() != null ?
                    fixedNode.getContent().substring(0, Math.min(30, fixedNode.getContent().length())) : "无内容";
            log.debug("修复后节点顺序[{}]: {} - {}", i, fixedNode.getType(), content);
        }

        return fixedNodes;
    }

    /**
     * 创建文本节点
     */
    private SimpleWordExportRequest.ContentNode createTextNode(SimpleWordExportRequest.ContentNode originalNode,
                                                               List<SimpleWordExportRequest.ContentNode> children) {
        SimpleWordExportRequest.ContentNode textNode = new SimpleWordExportRequest.ContentNode();
        textNode.setId("text_" + System.currentTimeMillis() + "_" + Math.random());
        textNode.setType("p");
        textNode.setContent(originalNode.getContent());
        textNode.setStyles(originalNode.getStyles());
        textNode.setAttributes(originalNode.getAttributes());
        textNode.setChildren(children);
        return textNode;
    }

    /**
     * 设置页面属性
     */
    private void setPageSettings(XWPFDocument document, SimpleWordExportRequest.PageSettings settings) {
        if (settings == null) {
            return;
        }

        // 获取文档的节属性
        CTSectPr sectPr = document.getDocument().getBody().addNewSectPr();
        CTPageSz pageSize = sectPr.addNewPgSz();
        CTPageMar pageMargin = sectPr.addNewPgMar();

        // 设置纸张大小
        setPaperSize(pageSize, settings.getPaperSize(), settings.getOrientation());

        // 设置页边距（转换毫米到twips，1毫米 = 56.7 twips）
        int marginTopTwips = (int) (settings.getMarginTop() * 56.7);
        int marginBottomTwips = (int) (settings.getMarginBottom() * 56.7);
        int marginLeftTwips = (int) (settings.getMarginLeft() * 56.7);
        int marginRightTwips = (int) (settings.getMarginRight() * 56.7);

        pageMargin.setTop(BigInteger.valueOf(marginTopTwips));
        pageMargin.setBottom(BigInteger.valueOf(marginBottomTwips));
        pageMargin.setLeft(BigInteger.valueOf(marginLeftTwips));
        pageMargin.setRight(BigInteger.valueOf(marginRightTwips));

        log.debug("页面设置完成: 纸张={}, 方向={}, 边距={}mm",
                settings.getPaperSize(), settings.getOrientation(),
                String.format("上%d 下%d 左%d 右%d",
                        settings.getMarginTop(), settings.getMarginBottom(),
                        settings.getMarginLeft(), settings.getMarginRight()));
    }

    /**
     * 设置纸张大小
     */
    private void setPaperSize(CTPageSz pageSize, String paperSizeType, String orientation) {
        boolean isLandscape = "landscape".equals(orientation);

        switch (paperSizeType) {
            case "A4":
                if (isLandscape) {
                    pageSize.setW(BigInteger.valueOf(16838)); // A4横向宽度
                    pageSize.setH(BigInteger.valueOf(11906)); // A4横向高度
                } else {
                    pageSize.setW(BigInteger.valueOf(11906)); // A4纵向宽度
                    pageSize.setH(BigInteger.valueOf(16838)); // A4纵向高度
                }
                break;
            case "A3":
                if (isLandscape) {
                    pageSize.setW(BigInteger.valueOf(23811)); // A3横向宽度
                    pageSize.setH(BigInteger.valueOf(16838)); // A3横向高度
                } else {
                    pageSize.setW(BigInteger.valueOf(16838)); // A3纵向宽度
                    pageSize.setH(BigInteger.valueOf(23811)); // A3纵向高度
                }
                break;
            case "Letter":
                if (isLandscape) {
                    pageSize.setW(BigInteger.valueOf(15840)); // Letter横向宽度
                    pageSize.setH(BigInteger.valueOf(12240)); // Letter横向高度
                } else {
                    pageSize.setW(BigInteger.valueOf(12240)); // Letter纵向宽度
                    pageSize.setH(BigInteger.valueOf(15840)); // Letter纵向高度
                }
                break;
            default:
                // 默认A4纵向
                pageSize.setW(BigInteger.valueOf(11906));
                pageSize.setH(BigInteger.valueOf(16838));
        }

        if (isLandscape) {
            pageSize.setOrient(STPageOrientation.LANDSCAPE);
        }
    }

    /**
     * 添加页眉页脚
     */
    private void addHeaderFooter(XWPFDocument document, SimpleWordExportRequest.PageSettings settings) {
        if (settings == null) {
            return;
        }

        try {
            // 添加页眉
            if (settings.getShowHeader() && settings.getHeaderText() != null && !settings.getHeaderText().trim().isEmpty()) {
                XWPFHeader header = document.createHeader(HeaderFooterType.DEFAULT);
                XWPFParagraph headerParagraph = header.createParagraph();
                headerParagraph.setAlignment(ParagraphAlignment.CENTER);

                XWPFRun headerRun = headerParagraph.createRun();
                headerRun.setText(settings.getHeaderText());
                headerRun.setFontFamily(settings.getHeaderFooterFont());
                headerRun.setFontSize(settings.getHeaderFooterFontSize());

                log.debug("添加页眉: {}", settings.getHeaderText());
            }

            // 添加页脚
            if (settings.getShowFooter() || settings.getShowPageNumber()) {
                XWPFFooter footer = document.createFooter(HeaderFooterType.DEFAULT);
                XWPFParagraph footerParagraph = footer.createParagraph();
                footerParagraph.setAlignment(ParagraphAlignment.CENTER);

                XWPFRun footerRun = footerParagraph.createRun();

                StringBuilder footerText = new StringBuilder();
                if (settings.getShowFooter() && settings.getFooterText() != null && !settings.getFooterText().trim().isEmpty()) {
                    footerText.append(settings.getFooterText());
                }

                if (settings.getShowPageNumber()) {
                    if (footerText.length() > 0) {
                        footerText.append(" ");
                    }
                    footerText.append("第 ");
                }

                footerRun.setText(footerText.toString());
                footerRun.setFontFamily(settings.getHeaderFooterFont());
                footerRun.setFontSize(settings.getHeaderFooterFontSize());

                // 添加页码
                if (settings.getShowPageNumber()) {
                    footerParagraph.getCTP().addNewFldSimple().setInstr("PAGE");
                    footerRun = footerParagraph.createRun();
                    footerRun.setText(" 页");
                    footerRun.setFontFamily(settings.getHeaderFooterFont());
                    footerRun.setFontSize(settings.getHeaderFooterFontSize());
                }

                log.debug("添加页脚: {}", footerText.toString());
            }

        } catch (Exception e) {
            log.warn("添加页眉页脚时出现错误", e);
        }
    }


    /**
     * 确定图片类型
     */
    private ImageTypeInfo determineImageType(String mimeType) throws IOException {
        String format;
        int wordType;

        if (mimeType.contains("jpeg") || mimeType.contains("jpg")) {
            format = "JPEG";
            wordType = XWPFDocument.PICTURE_TYPE_JPEG;
        } else if (mimeType.contains("png")) {
            format = "PNG";
            wordType = XWPFDocument.PICTURE_TYPE_PNG;
        } else if (mimeType.contains("gif")) {
            format = "GIF";
            wordType = XWPFDocument.PICTURE_TYPE_GIF;
        } else if (mimeType.contains("bmp")) {
            format = "BMP";
            wordType = XWPFDocument.PICTURE_TYPE_BMP;
        } else if (mimeType.contains("webp")) {
            // WebP转换为PNG处理
            format = "WebP->PNG";
            wordType = XWPFDocument.PICTURE_TYPE_PNG;
            log.warn("WebP格式将作为PNG处理，可能影响图片质量");
        } else {
            throw new IOException("不支持的图片格式: " + mimeType);
        }

        return new ImageTypeInfo(format, wordType);
    }

    /**
     * 计算图片尺寸（保持宽高比）
     */
    private ImageDimensions calculateImageDimensions(SimpleWordExportRequest.ContentNode node,
                                                     SimpleWordExportRequest.ExportConfig exportConfig) {
        int width = 400; // 默认宽度（像素）
        int height = 300; // 默认高度（像素）
        String source = "默认值";

        // 获取原始图片尺寸信息
        int naturalWidth = 0;
        int naturalHeight = 0;
        double aspectRatio = 0.0;

        if (node.getAttributes() != null) {
            try {
                String naturalWidthStr = node.getAttributes().get("naturalWidth");
                String naturalHeightStr = node.getAttributes().get("naturalHeight");
                String aspectRatioStr = node.getAttributes().get("aspectRatio");

                if (naturalWidthStr != null && naturalHeightStr != null) {
                    naturalWidth = Integer.parseInt(naturalWidthStr);
                    naturalHeight = Integer.parseInt(naturalHeightStr);
                    aspectRatio = (double) naturalWidth / naturalHeight;
                    log.debug("获取原始图片尺寸: {}x{}, 宽高比: {}", naturalWidth, naturalHeight, aspectRatio);
                } else if (aspectRatioStr != null) {
                    aspectRatio = Double.parseDouble(aspectRatioStr);
                    log.debug("获取图片宽高比: {}", aspectRatio);
                }
            } catch (NumberFormatException e) {
                log.debug("解析原始图片尺寸信息失败: {}", e.getMessage());
            }
        }

        // 从样式中获取尺寸
        Integer styleWidth = null;
        Integer styleHeight = null;

        if (node.getStyles() != null) {
            String styleWidthStr = node.getStyles().get("width");
            String styleHeightStr = node.getStyles().get("height");

            log.debug("解析图片样式尺寸 - width: {}, height: {}", styleWidthStr, styleHeightStr);

            // 解析宽度
            if (styleWidthStr != null && !styleWidthStr.trim().isEmpty()) {
                styleWidth = parseDimension(styleWidthStr);
                if (styleWidth != null) {
                    log.debug("从CSS样式解析宽度: {}px", styleWidth);
                }
            }

            // 解析高度
            if (styleHeightStr != null && !styleHeightStr.trim().isEmpty()) {
                styleHeight = parseDimension(styleHeightStr);
                if (styleHeight != null) {
                    log.debug("从CSS样式解析高度: {}px", styleHeight);
                }
            }
        }

        // 智能计算最终尺寸，保持宽高比
        if (styleWidth != null && styleHeight != null) {
            // 用户同时设置了宽度和高度，直接使用
            width = styleWidth;
            height = styleHeight;
            source = "CSS样式(宽高)";
            log.debug("使用用户设置的宽高: {}x{}", width, height);
        } else if (styleWidth != null && aspectRatio > 0) {
            // 用户只设置了宽度，根据宽高比计算高度
            width = styleWidth;
            height = (int) Math.round(width / aspectRatio);
            source = "CSS样式(宽度) + 宽高比";
            log.debug("根据宽度和宽高比计算: {}x{}", width, height);
        } else if (styleHeight != null && aspectRatio > 0) {
            // 用户只设置了高度，根据宽高比计算宽度
            height = styleHeight;
            width = (int) Math.round(height * aspectRatio);
            source = "CSS样式(高度) + 宽高比";
            log.debug("根据高度和宽高比计算: {}x{}", width, height);
        } else if (styleWidth != null) {
            // 只有宽度，没有宽高比信息，使用默认比例
            width = styleWidth;
            height = (int) Math.round(width * 0.75); // 4:3比例
            source = "CSS样式(宽度) + 默认比例";
            log.debug("使用宽度和默认比例: {}x{}", width, height);
        } else if (styleHeight != null) {
            // 只有高度，没有宽高比信息，使用默认比例
            height = styleHeight;
            width = (int) Math.round(height * 1.33); // 4:3比例
            source = "CSS样式(高度) + 默认比例";
            log.debug("使用高度和默认比例: {}x{}", width, height);
        } else if (naturalWidth > 0 && naturalHeight > 0) {
            // 没有CSS尺寸，使用原始尺寸（可能需要缩放）
            width = naturalWidth;
            height = naturalHeight;
            source = "原始尺寸";
            log.debug("使用原始图片尺寸: {}x{}", width, height);
        }

        // 应用最大尺寸限制（保持宽高比）
        if (exportConfig != null) {
            boolean sizeAdjusted = false;
            int originalWidth = width;
            int originalHeight = height;

            // 计算当前宽高比
            double currentAspectRatio = (double) width / height;

            // 检查是否超过最大宽度限制
            if (exportConfig.getMaxImageWidth() != null && width > exportConfig.getMaxImageWidth()) {
                width = exportConfig.getMaxImageWidth();
                height = (int) Math.round(width / currentAspectRatio);
                sizeAdjusted = true;
                log.debug("应用最大宽度限制: {}px, 保持宽高比, 调整后尺寸: {}x{}", exportConfig.getMaxImageWidth(), width, height);
            }

            // 检查是否超过最大高度限制
            if (exportConfig.getMaxImageHeight() != null && height > exportConfig.getMaxImageHeight()) {
                height = exportConfig.getMaxImageHeight();
                width = (int) Math.round(height * currentAspectRatio);
                sizeAdjusted = true;
                log.debug("应用最大高度限制: {}px, 保持宽高比, 调整后尺寸: {}x{}", exportConfig.getMaxImageHeight(), width, height);

                // 如果调整后宽度又超过了最大宽度限制，需要再次调整
                if (exportConfig.getMaxImageWidth() != null && width > exportConfig.getMaxImageWidth()) {
                    width = exportConfig.getMaxImageWidth();
                    height = (int) Math.round(width / currentAspectRatio);
                    log.debug("二次调整以满足宽度限制, 最终尺寸: {}x{}", width, height);
                }
            }

            if (sizeAdjusted) {
                source += " + 尺寸限制";
                log.debug("尺寸限制调整: {}x{} → {}x{}, 宽高比保持: {}",
                        originalWidth, originalHeight, width, height, currentAspectRatio);
            }
        }

        // 确保最小尺寸
        final int minSize = 10;
        if (width < minSize) {
            width = minSize;
            log.debug("应用最小宽度限制: {}px", minSize);
        }
        if (height < minSize) {
            height = minSize;
            log.debug("应用最小高度限制: {}px", minSize);
        }

        return new ImageDimensions(width, height, source);
    }

    /**
     * 解析尺寸字符串
     */
    private Integer parseDimension(String dimensionStr) {
        if (dimensionStr == null || dimensionStr.trim().isEmpty()) {
            return null;
        }

        String cleanStr = dimensionStr.trim().toLowerCase();

        try {
            // 处理像素单位
            if (cleanStr.endsWith("px")) {
                return Integer.parseInt(cleanStr.replace("px", ""));
            }

            // 处理百分比（转换为像素，假设基准宽度为600px）
            if (cleanStr.endsWith("%")) {
                double percentage = Double.parseDouble(cleanStr.replace("%", ""));
                return (int) Math.round(600 * percentage / 100);
            }

            // 处理纯数字（假设为像素）
            return Integer.parseInt(cleanStr);

        } catch (NumberFormatException e) {
            log.warn("无法解析尺寸字符串: {}", dimensionStr);
            return null;
        }
    }

    /**
     * 图片尺寸信息类
     */
    private static class ImageDimensions {
        private final int width;
        private final int height;
        private final String source;

        public ImageDimensions(int width, int height, String source) {
            this.width = width;
            this.height = height;
            this.source = source;
        }

        public int getWidth() {
            return width;
        }

        public int getHeight() {
            return height;
        }

        public String getSource() {
            return source;
        }
    }

    /**
     * 图片类型信息类
     */
    private static class ImageTypeInfo {
        private final String format;
        private final int wordType;

        public ImageTypeInfo(String format, int wordType) {
            this.format = format;
            this.wordType = wordType;
        }

        public String getFormat() {
            return format;
        }

        public int getWordType() {
            return wordType;
        }
    }
}
