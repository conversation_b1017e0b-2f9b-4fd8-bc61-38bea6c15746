package com.logictrue.word.dto.table;

import com.logictrue.word.dto.JsonTableExportRequest;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 嵌套表格配置
 */
@Data
public class NestedTableConfig {
    /**
     * 是否启用嵌套表格
     */
    private Boolean enabled;

    /**
     * 嵌套表格的配置信息
     */
    private NestedTableData config;

    /**
     * 嵌套表格的元数据
     */
    private NestedTableMetadata metadata;

    @Data
    public static class NestedTableData {

        private List<Integer> columnWidths;

        private List<Integer> rowHeights;

        /**
         * 数据行
         */
        private List<List<CellData>> cellRows;

        /**
         * 合并单元格配置
         */
        private List<MergeConfig> merges;

        /**
         * 元数据
         */
        private Map<String, Object> metadata;
    }

    /**
     * 嵌套表格元数据
     */
    @Data
    public static class NestedTableMetadata {
        /**
         * 父单元格行索引
         */
        private Integer parentRowIndex;

        /**
         * 父单元格列索引
         */
        private Integer parentCellIndex;

        /**
         * 嵌套层级
         */
        private Integer nestedLevel;

        /**
         * 是否有主内容
         */
        private Boolean hasContent;
    }

}
