package com.logictrue.word.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logictrue.word.entity.DrlWorkCheck;
import com.logictrue.word.entity.vo.WorkCheckVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DrlWorkCheckMapper extends BaseMapper<DrlWorkCheck> {

    /**
     * 连表查询检验模板数据
     * @return 检验模板VO列表
     */
    List<WorkCheckVo> selectWorkCheckVoList(@Param("carId") String carId);

    List<WorkCheckVo> selectRangeList(@Param("carId") String carId, @Param("start") Integer start, @Param("end") Integer end);
}
