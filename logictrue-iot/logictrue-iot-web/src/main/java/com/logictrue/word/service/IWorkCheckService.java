package com.logictrue.word.service;

import com.logictrue.word.dto.table.TableDataDto;
import com.logictrue.word.entity.vo.WorkCheckVo;
import java.util.List;

/**
 * 检验记录表服务接口
 */
public interface IWorkCheckService {

    /**
     * 生成检验记录表数据
     * @param carId 车辆ID
     * @return 表格数据DTO
     */
    TableDataDto generateWorkCheckTable(String carId);

    /**
     * 查询检验模板数据
     * @return 检验模板VO列表
     */
    List<WorkCheckVo> selectWorkCheckVoList(String carId);

    /**
     * 保存检验记录表数据
     * @param carId 车辆ID
     * @param tableDataDto 表格数据DTO
     * @return 保存结果
     */
    boolean saveWorkCheckTable(String carId, TableDataDto tableDataDto);

    /**
     * 生成检验记录表数据列表（支持拆分）
     * @param carId 车辆ID
     * @return 表格数据DTO列表，每个拆分的数据都是独立的TableDataDto
     */
    List<TableDataDto> generateWorkCheckTableList(String carId);

    List<TableDataDto> generateRangeTableList(String carId, String range);

    /**
     * 批量保存检验记录表数据
     * @param carId 车辆ID
     * @param tableDataDtoList 表格数据DTO列表
     * @return 保存结果
     */
    boolean saveWorkCheckTableList(String carId, List<TableDataDto> tableDataDtoList);

    /**
     * 批量保存检验记录表数据（使用批量插入）
     * @param carId 车辆ID
     * @param tableDataDtoList 表格数据DTO列表
     * @return 保存结果
     */
    boolean batchSaveWorkCheckTableList(String carId, List<TableDataDto> tableDataDtoList);
}
