package com.logictrue.word.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

@Data
@TableName(value = "drl_check_type")
public class DrlCheckType {
    /**
     * 主键
     */
    @TableId(value = "check_type_id", type = IdType.AUTO)
    private Integer checkTypeId;

    /**
     * 检验工序名称
     */
    @TableField(value = "check_type_name")
    private String checkTypeName;

    /**
     * 班组
     */
    @TableField(value = "team_id")
    private Integer teamId;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 逻辑删除
     */
    @TableField(value = "del_flag")
    private Boolean delFlag;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private Integer createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_by")
    private Integer updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 1章节 2 检验工序
     */
    @TableField(value = "type_level")
    private Integer typeLevel;

    /**
     * 父级Id
     */
    @TableField(value = "parent_id")
    private Integer parentId;

    /**
     * 车型
     */
    @TableField(value = "car_type")
    private Integer carType;

    /**
     * 检验卡片版本id
     */
    @TableField(value = "check_ver_id")
    private Integer checkVerId;
}