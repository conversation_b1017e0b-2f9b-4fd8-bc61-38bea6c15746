package com.logictrue.word.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logictrue.word.entity.DrlDataRecords;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检验卡片记录表Mapper接口
 */
public interface DrlDataRecordsMapper extends BaseMapper<DrlDataRecords> {

    /**
     * 根据模板ID列表查询检验记录数据
     *
     * @param templateIds 模板ID列表
     * @return 检验记录数据列表
     */
    List<DrlDataRecords> selectByTemplateIds(@Param("templateIds") List<Integer> templateIds);

    /**
     * 根据车号和模板ID列表查询检验记录数据
     *
     * @param carId 车号
     * @param templateIds 模板ID列表
     * @return 检验记录数据列表
     */
    List<DrlDataRecords> selectByCarIdAndTemplateIds(@Param("carId") String carId, @Param("templateIds") List<Integer> templateIds);
}
