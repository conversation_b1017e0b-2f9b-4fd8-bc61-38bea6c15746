package com.logictrue.word.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 班组表
 */
@Data
@TableName(value = "drl_team")
public class DrlTeam {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 班组名称
     */
    @TableField(value = "`name`")
    private String name;

    @TableField(value = "part_code")
    private String partCode;

    /**
     * 1总装 2光电
     */
    @TableField(value = "workshop")
    private Integer workshop;

    /**
     * 班长id
     */
    @TableField(value = "leader_id")
    private Integer leaderId;

    /**
     * 班长姓名
     */
    @TableField(value = "leader_name")
    private String leaderName;

    /**
     * 1车间 2班组
     */
    @TableField(value = "team_level")
    private Integer teamLevel;

    /**
     * 父级id
     */
    @TableField(value = "parent_id")
    private Integer parentId;

    /**
     * 逻辑删除
     */
    @TableField(value = "del_flag")
    private Boolean delFlag;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 车间号
     */
    @TableField(value = "workshop_code")
    private Integer workshopCode;
}