package com.logictrue.word.entity.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 检验模板表
 */
@Data
public class WorkCheckVo {
    /**
     * 主键
     */
    private Integer checkId;

    /**
     * 标题id
     */
    private Integer parentId;

    /**
     * 检验项
     */
    private String checkName;

    /**
     * 内容
     */
    private String checkContent;

    /**
     * 车型
     */
    private Integer carType;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 层级(1:标题.2:检验项)如果是1则结果模板,必填,需要替换值必须为null
     */
    private Integer checkLevel;

    /**
     * 班组
     */
    private Integer team;

    /**
     * 检验工序id
     */
    private Integer checkTypeId;


    private String checkTypeName;

    /**
     * 结果模板
     */
    private String resultTemplate;

    /**
     * 必填
     */
    private Integer isRequired;

    /**
     * 需要替换值个数
     */
    private Integer replaceNum;

    /**
     * 版本id
     */
    private Integer versionId;

    /**
     * 检验结果
     */
    private String result;


    private String monthStr;


    private String dayStr;

    private String czy;

    private String jyy;

    /**
     * 导入编码
     */
    private String checkCode;

    private String coverData;

    private int addNewPage;

    private String tableJson;

    private String valueMap;

    private Integer rowHeight;
}
