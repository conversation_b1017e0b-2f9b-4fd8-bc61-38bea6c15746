package com.logictrue.word.dto.table;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 表格数据DTO
 */
@Data
public class TableDataDto {

    /**
     * 单元格行数据
     */
    private List<List<CellData>> cellRows;

    /**
     * 合并单元格配置
     */
    private List<MergeConfig> merges;

    /**
     * 表头配置
     */
    private HeaderConfig headerConfig;

    /**
     * 表头宽度配置
     */
    private HeaderWidthConfig headerWidthConfig;

    /**
     * 元数据
     */
    private Metadata metadata;

    private Map<Integer, List<Integer>> checkIdMap;

    private boolean hasNestedTable;

}
