package com.logictrue.word.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 检验卡片记录表
 */
@Data
@TableName(value = "drl_work_check_records")
public class DrlWorkCheckRecords {
    @TableId(value = "record_id", type = IdType.AUTO)
    private Integer recordId;

    /**
     * 任务记录表id
     */
    @TableField(value = "task_record_id")
    private Integer taskRecordId;

    /**
     * 任务id
     */
    @TableField(value = "task_id")
    private Integer taskId;

    /**
     * 车辆id
     */
    @TableField(value = "car_id")
    private Integer carId;

    /**
     * 检查项目/标题id
     */
    @TableField(value = "check_id")
    private Integer checkId;

    /**
     * 所属标题id
     */
    @TableField(value = "parent_id")
    private Integer parentId;

    /**
     * 检查项目及技术条件
     */
    @TableField(value = "check_name")
    private String checkName;

    /**
     * 内容
     */
    @TableField(value = "check_content")
    private String checkContent;

    /**
     * 车型
     */
    @TableField(value = "car_type")
    private Integer carType;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 1.标题2.检查项目
     */
    @TableField(value = "check_level")
    private Integer checkLevel;

    /**
     * 班组
     */
    @TableField(value = "team")
    private Integer team;

    /**
     * 检查工序
     */
    @TableField(value = "check_type_id")
    private Integer checkTypeId;

    /**
     * 实际检查结果
     */
    @TableField(value = "`result`")
    private String result;

    /**
     * 1.必填2.非必填
     */
    @TableField(value = "is_required")
    private Integer isRequired;

    /**
     * 检验卡片版本
     */
    @TableField(value = "version_id")
    private Integer versionId;

    /**
     * 替换规则
     */
    @TableField(value = "replace_rules")
    private String replaceRules;

    /**
     * 结果模板
     */
    @TableField(value = "result_template")
    private String resultTemplate;

    /**
     * 保存后修改规则
     */
    @TableField(value = "change_index")
    private String changeIndex;

    /**
     * 保存标识
     */
    @TableField(value = "is_pass")
    private Boolean isPass;

    /**
     * 保存替换字段
     */
    @TableField(value = "result_copy")
    private String resultCopy;

    /**
     * 不通过原因
     */
    @TableField(value = "not_reason")
    private String notReason;

    /**
     * 检验填写人id
     */
    @TableField(value = "check_user_id")
    private Integer checkUserId;

    /**
     * 检验填写人
     */
    @TableField(value = "check_user_name")
    private String checkUserName;

    /**
     * 提交时间
     */
    @TableField(value = "check_time")
    private Date checkTime;

    /**
     * 检验人id
     */
    @TableField(value = "take_user_id")
    private Integer takeUserId;

    /**
     * 检验人
     */
    @TableField(value = "take_user_name")
    private String takeUserName;

    /**
     * 检验时间
     */
    @TableField(value = "take_time")
    private Date takeTime;

    /**
     * 页面输入类型
     */
    @TableField(value = "porp_type")
    private Integer porpType;
}