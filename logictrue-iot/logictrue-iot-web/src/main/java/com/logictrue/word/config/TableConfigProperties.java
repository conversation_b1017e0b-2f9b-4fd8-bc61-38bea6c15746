package com.logictrue.word.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 表格配置属性类
 */
@Data
@Component
@ConfigurationProperties(prefix = "table.config")
public class TableConfigProperties {

    /**
     * 表格列数
     */
    private int columnCount = 8;

    /**
     * 检查工序列宽度
     */
    private int checkProcessColumnWidth = 80;

    /**
     * 检查名称列宽度
     */
    private int checkNameColumnWidth = 350;

    /**
     * 检查结果列宽度
     */
    private int checkResultColumnWidth = 160;

    /**
     * 行高限制
     */
    private int rowHeightLimit = 420;

    /**
     * 表格标题
     */
    private String tableTitle = "检验记录表";

    /**
     * 小列宽度
     */
    private int smallColumnWidth = 25;

    /**
     * 小行高度
     */
    private int smallRowHeight = 25;

    /**
     * 表格中单字高度（包含行间距）
     */
    private double fontHeight = 14.5d;

    /**
     * 表格中单字宽度
     */
    private double fontWidth = 11d;

    /**
     * 批量插入大小
     */
    private int batchSize = 100;

    private int spaceSize = 5;

    private int pageLine = 29;
}
