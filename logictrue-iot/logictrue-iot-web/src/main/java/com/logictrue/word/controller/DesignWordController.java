package com.logictrue.word.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logictrue.common.core.web.controller.BaseController;
import com.logictrue.common.core.web.domain.AjaxResult;
import com.logictrue.common.core.web.page.TableDataInfo;
import com.logictrue.word.entity.DesignWord;
import com.logictrue.word.service.IDesignWordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 检验记录表设计Controller
 */
@RestController
@RequestMapping("/designWord")
@Api(value = "检验记录表设计", tags = "检验记录表设计")
public class DesignWordController extends BaseController {

    @Autowired
    private IDesignWordService designWordService;

    /**
     * 查询检验记录表设计列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询检验记录表设计列表")
    public TableDataInfo list(DesignWord designWord) {
        startPage();
        List<DesignWord> list = designWordService.selectDesignWordList(designWord);
        return getDataTable(list);
    }

    /**
     * 分页查询检验记录表设计列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页查询检验记录表设计列表")
    public AjaxResult page(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("车辆ID") @RequestParam(required = false) String carId,
            @ApiParam("标题") @RequestParam(required = false) String title,
            @ApiParam("状态") @RequestParam(required = false) Integer status) {

        try {
            Page<DesignWord> page = new Page<>(pageNum, pageSize);
            DesignWord designWord = new DesignWord();
            designWord.setCarId(carId);
            designWord.setTitle(title);
            designWord.setStatus(status);

            IPage<DesignWord> result = designWordService.selectDesignWordPage(page, designWord);
            return AjaxResult.success(result);

        } catch (Exception e) {
            logger.error("分页查询检验记录表设计失败", e);
            return AjaxResult.error("分页查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取检验记录表设计详细信息
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取检验记录表设计详细信息")
    public AjaxResult getInfo(@ApiParam("主键ID") @PathVariable("id") Long id) {
        try {
            DesignWord designWord = designWordService.selectDesignWordById(id);
            return AjaxResult.success(designWord);
        } catch (Exception e) {
            logger.error("获取检验记录表设计详细信息失败", e);
            return AjaxResult.error("获取详细信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据车辆ID获取检验记录表设计
     */
    @GetMapping("/byCarId/{carId}")
    @ApiOperation(value = "根据车辆ID获取检验记录表设计")
    public AjaxResult getByCarId(@ApiParam("车辆ID") @PathVariable("carId") String carId) {
        try {
            DesignWord designWord = designWordService.selectDesignWordByCarId(carId);
            return AjaxResult.success(designWord);
        } catch (Exception e) {
            logger.error("根据车辆ID获取检验记录表设计失败", e);
            return AjaxResult.error("获取设计信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据车辆ID获取所有页面
     */
    @GetMapping("/pages/{carId}")
    @ApiOperation(value = "根据车辆ID获取所有页面")
    public AjaxResult getPagesByCarId(@ApiParam("车辆ID") @PathVariable("carId") String carId) {
        try {
            List<DesignWord> pages = designWordService.selectDesignWordPagesByCarId(carId);
            return AjaxResult.success(pages);
        } catch (Exception e) {
            logger.error("根据车辆ID获取所有页面失败", e);
            return AjaxResult.error("获取页面信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据车辆ID和页面顺序获取特定页面
     */
    @GetMapping("/page/{carId}/{pageId}")
    @ApiOperation(value = "根据车辆ID和页面顺序获取特定页面")
    public AjaxResult getPageByCarIdAndId(
            @ApiParam("车辆ID") @PathVariable("carId") String carId,
            @ApiParam("页面顺序") @PathVariable("pageId") Integer pageId) {
        try {
            DesignWord designWord = designWordService.selectDesignWordByCarIdAndPage(carId, pageId);
            return AjaxResult.success(designWord);
        } catch (Exception e) {
            logger.error("根据车辆ID和页面顺序获取特定页面失败", e);
            return AjaxResult.error("获取页面信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据车辆ID获取当前活动页面
     */
    @GetMapping("/activePage/{carId}")
    @ApiOperation(value = "根据车辆ID获取当前活动页面")
    public AjaxResult getActivePageByCarId(@ApiParam("车辆ID") @PathVariable("carId") String carId) {
        try {
            DesignWord designWord = designWordService.selectActiveDesignWordByCarId(carId);
            return AjaxResult.success(designWord);
        } catch (Exception e) {
            logger.error("根据车辆ID获取当前活动页面失败", e);
            return AjaxResult.error("获取活动页面信息失败：" + e.getMessage());
        }
    }

    /**
     * 新增检验记录表设计
     */
    @PostMapping
    @ApiOperation(value = "新增检验记录表设计")
    public AjaxResult add(@RequestBody DesignWord designWord) {
        try {
            // 设置默认值
            if (designWord.getStatus() == null) {
                designWord.setStatus(1);
            }
            if (designWord.getTitle() == null || designWord.getTitle().trim().isEmpty()) {
                designWord.setTitle("检验记录表");
            }

            int result = designWordService.insertDesignWord(designWord);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("新增检验记录表设计失败", e);
            return AjaxResult.error("新增失败：" + e.getMessage());
        }
    }

    /**
     * 修改检验记录表设计
     */
    @PutMapping
    @ApiOperation(value = "修改检验记录表设计")
    public AjaxResult edit(@RequestBody DesignWord designWord) {
        try {
            int result = designWordService.updateDesignWord(designWord);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("修改检验记录表设计失败", e);
            return AjaxResult.error("修改失败：" + e.getMessage());
        }
    }

    /**
     * 保存或更新检验记录表设计
     */
    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "保存或更新检验记录表设计")
    public AjaxResult saveOrUpdate(@RequestBody DesignWord designWord) {
        try {
            // 设置默认值
            if (designWord.getStatus() == null) {
                designWord.setStatus(1);
            }
            if (designWord.getTitle() == null || designWord.getTitle().trim().isEmpty()) {
                designWord.setTitle("检验记录表");
            }
            if (designWord.getPageName() == null || designWord.getPageName().trim().isEmpty()) {
                designWord.setPageName("第1页");
            }
            if (designWord.getPageOrder() == null) {
                designWord.setPageOrder(1);
            }
            if (designWord.getTotalPages() == null) {
                designWord.setTotalPages(1);
            }
            if (designWord.getIsActive() == null) {
                designWord.setIsActive(1);
            }

            // 前端已经提供了boundTemplateIds，无需后端再次提取
            // 如果前端没有提供boundTemplateIds但有rowTemplateBindings，则自动提取
            if ((designWord.getBoundTemplateIds() == null || designWord.getBoundTemplateIds().trim().isEmpty())
                && designWord.getRowTemplateBindings() != null && !designWord.getRowTemplateBindings().trim().isEmpty()) {
                String boundTemplateIds = extractBoundTemplateIds(designWord.getRowTemplateBindings());
                designWord.setBoundTemplateIds(boundTemplateIds);
            }

            int result = designWordService.saveOrUpdateDesignWord(designWord);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("保存或更新检验记录表设计失败", e);
            return AjaxResult.error("保存失败：" + e.getMessage());
        }
    }

    /**
     * 设置活动页面
     */
    @PostMapping("/setActivePage")
    @ApiOperation(value = "设置活动页面")
    public AjaxResult setActivePage(
            @ApiParam("车辆ID") @RequestParam String carId,
            @ApiParam("页面顺序") @RequestParam Integer pageId) {
        try {
            int result = designWordService.setActivePage(carId, pageId);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("设置活动页面失败", e);
            return AjaxResult.error("设置活动页面失败：" + e.getMessage());
        }
    }

    /**
     * 更新总页数
     */
    @PostMapping("/updateTotalPages")
    @ApiOperation(value = "更新总页数")
    public AjaxResult updateTotalPages(
            @ApiParam("车辆ID") @RequestParam String carId,
            @ApiParam("总页数") @RequestParam Integer totalPages) {
        try {
            int result = designWordService.updateTotalPagesByCarId(carId, totalPages);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("更新总页数失败", e);
            return AjaxResult.error("更新总页数失败：" + e.getMessage());
        }
    }

    /**
     * 删除检验记录表设计
     */
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除检验记录表设计")
    public AjaxResult remove(@ApiParam("主键ID数组") @PathVariable Long[] ids) {
        try {
            int result = designWordService.deleteDesignWordByIds(ids);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("删除检验记录表设计失败", e);
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新页面顺序
     */
    @PostMapping("/batchUpdatePageOrder")
    @ApiOperation(value = "批量更新页面顺序")
    public AjaxResult batchUpdatePageOrder(@RequestBody java.util.List<DesignWord> pages) {
        try {
            if (pages == null || pages.isEmpty()) {
                return AjaxResult.error("页面列表不能为空");
            }

            // 验证数据完整性
            for (DesignWord page : pages) {
                if (page.getId() == null) {
                    return AjaxResult.error("页面ID不能为空");
                }
                if (page.getCarId() == null || page.getCarId().trim().isEmpty()) {
                    return AjaxResult.error("车辆ID不能为空");
                }
                if (page.getPageOrder() == null) {
                    return AjaxResult.error("页面顺序不能为空");
                }
            }

            // 批量更新页面顺序
            int result = designWordService.batchUpdatePageOrder(pages);
            return result > 0 ? AjaxResult.success("页面顺序更新成功") : AjaxResult.error("页面顺序更新失败");

        } catch (Exception e) {
            logger.error("批量更新页面顺序失败", e);
            return AjaxResult.error("批量更新页面顺序失败：" + e.getMessage());
        }
    }

    /**
     * 交换两个页面的顺序
     */
    @PostMapping("/swapPageOrder")
    @ApiOperation(value = "交换两个页面的顺序")
    public AjaxResult swapPageOrder(@RequestBody List<DesignWord> pages) {
        try {
            if (pages == null || pages.size() != 2) {
                return AjaxResult.error("需要传入两个页面信息");
            }

            DesignWord page1 = pages.get(0);
            DesignWord page2 = pages.get(1);

            if (page1.getId() == null || page2.getId() == null) {
                return AjaxResult.error("页面ID不能为空");
            }
            if (page1.getPageOrder() == null || page2.getPageOrder() == null) {
                return AjaxResult.error("页面顺序不能为空");
            }
            if (page1.getId().equals(page2.getId())) {
                return AjaxResult.error("不能交换同一个页面的顺序");
            }

            int result = designWordService.swapPageOrder(page1, page2);
            return result > 0 ? AjaxResult.success("页面顺序交换成功") : AjaxResult.error("页面顺序交换失败");

        } catch (Exception e) {
            logger.error("交换页面顺序失败", e);
            return AjaxResult.error("交换页面顺序失败：" + e.getMessage());
        }
    }

    /**
     * 保存字段绑定配置
     */
    @PostMapping("/saveFieldBinding")
    @ApiOperation(value = "保存字段绑定配置")
    public AjaxResult saveFieldBinding(@RequestBody java.util.Map<String, Object> request) {
        try {
            String carId = (String) request.get("carId");
            Integer pageOrder = (Integer) request.get("pageOrder");
            @SuppressWarnings("unchecked")
            java.util.Map<String, Integer> fieldBindingConfig = (java.util.Map<String, Integer>) request.get("fieldBindingConfig");

            if (carId == null || carId.trim().isEmpty()) {
                return AjaxResult.error("车辆ID不能为空");
            }

            if (pageOrder == null) {
                return AjaxResult.error("页面顺序不能为空");
            }

            if (fieldBindingConfig == null || fieldBindingConfig.isEmpty()) {
                return AjaxResult.error("字段绑定配置不能为空");
            }

            // 将字段绑定配置转换为JSON字符串
            String fieldBindingJson = JSON.toJSONString(fieldBindingConfig);

            // 查找对应的设计记录
            DesignWord designWord = designWordService.selectDesignWordByCarIdAndPage(carId, pageOrder);

            if (designWord == null) {
                return AjaxResult.error("未找到对应的设计记录");
            }

            // 更新字段绑定配置
            designWord.setFieldBindingConfig(fieldBindingJson);
            int result = designWordService.updateDesignWord(designWord);

            return result > 0 ? AjaxResult.success("字段绑定配置保存成功") : AjaxResult.error("字段绑定配置保存失败");

        } catch (Exception e) {
            logger.error("保存字段绑定配置失败", e);
            return AjaxResult.error("保存字段绑定配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取字段绑定配置
     */
    @GetMapping("/getFieldBinding")
    @ApiOperation(value = "获取字段绑定配置")
    public AjaxResult getFieldBinding(
            @ApiParam("车辆ID") @RequestParam String carId,
            @ApiParam("页面顺序") @RequestParam Integer pageOrder) {
        try {
            if (carId == null || carId.trim().isEmpty()) {
                return AjaxResult.error("车辆ID不能为空");
            }

            if (pageOrder == null) {
                return AjaxResult.error("页面顺序不能为空");
            }

            // 查找对应的设计记录
            DesignWord designWord = designWordService.selectDesignWordByCarIdAndPage(carId, pageOrder);

            if (designWord == null) {
                return AjaxResult.error("未找到对应的设计记录");
            }

            // 解析字段绑定配置
            java.util.Map<String, Integer> fieldBindingConfig = null;
            if (designWord.getFieldBindingConfig() != null && !designWord.getFieldBindingConfig().trim().isEmpty()) {
                fieldBindingConfig = JSON.parseObject(
                    designWord.getFieldBindingConfig(),
                    new TypeReference<Map<String, Integer>>() {}
                );
            } else {
                // 返回默认配置
                fieldBindingConfig = getDefaultFieldBindingConfig();
            }

            return AjaxResult.success(fieldBindingConfig);

        } catch (Exception e) {
            logger.error("获取字段绑定配置失败", e);
            return AjaxResult.error("获取字段绑定配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取默认字段绑定配置
     */
    private Map<String, Integer> getDefaultFieldBindingConfig() {
        Map<String, Integer> defaultConfig = new HashMap<>();
        defaultConfig.put("result", 2);           // 第3列（索引2）
        defaultConfig.put("month_str", 3);        // 第4列（索引3）
        defaultConfig.put("day_str", 4);          // 第5列（索引4）
        defaultConfig.put("check_user_name", 5);  // 第6列（索引5）
        defaultConfig.put("bzz", 6);              // 第7列（索引6）
        defaultConfig.put("jyy", 7);              // 第8列（索引7）
        return defaultConfig;
    }

    /**
     * 从行模板绑定关系中提取模板ID列表
     */
    private String extractBoundTemplateIds(String rowTemplateBindings) {
        if (rowTemplateBindings == null || rowTemplateBindings.trim().isEmpty()) {
            return null;
        }

        try {
            java.util.Map<String, String> bindings = JSON.parseObject(
                rowTemplateBindings,
                new TypeReference<Map<String, String>>() {}
            );

            if (bindings == null || bindings.isEmpty()) {
                return null;
            }

            // 按表格行索引顺序提取所有模板ID，保持表格行顺序
            java.util.List<String> templateIds = new java.util.ArrayList<>();

            // 将行索引转换为整数并按数字大小排序（确保按表格行顺序）
            java.util.List<Integer> sortedRowIndexes = bindings.keySet().stream()
                .map(key -> {
                    try {
                        return Integer.parseInt(key);
                    } catch (NumberFormatException e) {
                        logger.warn("无效的行索引格式: " + key);
                        return -1; // 无效索引排在最前面
                    }
                })
                .filter(index -> index >= 0) // 过滤掉无效索引
                .sorted() // 按数字大小排序
                .collect(java.util.stream.Collectors.toList());

            // 按表格行顺序提取模板ID
            for (Integer rowIndex : sortedRowIndexes) {
                String templateId = bindings.get(String.valueOf(rowIndex));
                if (templateId != null && !templateId.trim().isEmpty()) {
                    templateIds.add(templateId.trim());
                }
            }

            if (templateIds.isEmpty()) {
                return null;
            }

            // 转换为逗号分隔的字符串，保持表格行顺序
            return String.join(",", templateIds);

        } catch (Exception e) {
            logger.error("解析行模板绑定关系失败", e);
            return null;
        }
    }
}
