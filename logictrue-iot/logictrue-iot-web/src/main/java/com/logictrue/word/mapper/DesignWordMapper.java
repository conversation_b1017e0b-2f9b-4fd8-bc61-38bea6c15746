package com.logictrue.word.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logictrue.word.entity.DesignWord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检验记录表设计Mapper接口
 */
@Mapper
public interface DesignWordMapper extends BaseMapper<DesignWord> {

    /**
     * 查询检验记录表设计列表
     *
     * @param designWord 检验记录表设计
     * @return 检验记录表设计集合
     */
    List<DesignWord> selectDesignWordList(DesignWord designWord);

    /**
     * 分页查询检验记录表设计列表
     *
     * @param page 分页对象
     * @param designWord 检验记录表设计
     * @return 检验记录表设计集合
     */
    IPage<DesignWord> selectDesignWordPage(Page<DesignWord> page, @Param("designWord") DesignWord designWord);

    /**
     * 根据车辆ID查询检验记录表设计
     *
     * @param carId 车辆ID
     * @return 检验记录表设计
     */
    DesignWord selectDesignWordByCarId(@Param("carId") String carId);

    /**
     * 根据车辆ID查询所有页面
     *
     * @param carId 车辆ID
     * @return 检验记录表设计列表
     */
    List<DesignWord> selectDesignWordPagesByCarId(@Param("carId") String carId);

    /**
     * 根据车辆ID和页面顺序查询特定页面
     *
     * @param carId 车辆ID
     * @param pageId 页面ID
     * @return 检验记录表设计
     */
    DesignWord selectDesignWordByCarIdAndPage(@Param("carId") String carId, @Param("pageId") Integer pageId);

    /**
     * 根据车辆ID查询当前活动页面
     *
     * @param carId 车辆ID
     * @return 检验记录表设计
     */
    DesignWord selectActiveDesignWordByCarId(@Param("carId") String carId);

    /**
     * 更新车辆的总页数
     *
     * @param carId 车辆ID
     * @param totalPages 总页数
     * @return 结果
     */
    int updateTotalPagesByCarId(@Param("carId") String carId, @Param("totalPages") Integer totalPages);

    /**
     * 设置活动页面
     *
     * @param carId 车辆ID
     * @param pageId 页面id
     * @return 结果
     */
    int setActivePage(@Param("carId") String carId, @Param("pageId") Integer pageId);

    /**
     * 新增检验记录表设计
     *
     * @param designWord 检验记录表设计
     * @return 结果
     */
    int insertDesignWord(DesignWord designWord);

    /**
     * 修改检验记录表设计
     *
     * @param designWord 检验记录表设计
     * @return 结果
     */
    int updateDesignWord(DesignWord designWord);

    /**
     * 删除检验记录表设计
     *
     * @param id 检验记录表设计主键
     * @return 结果
     */
    int deleteDesignWordById(Long id);

    /**
     * 批量删除检验记录表设计
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteDesignWordByIds(Long[] ids);

    int batchInsert(@Param("list") List<DesignWord> designWords);

    /**
     * 批量更新页面顺序
     *
     * @param pages 页面列表
     * @return 结果
     */
    int batchUpdatePageOrder(@Param("list") List<DesignWord> pages);
}
