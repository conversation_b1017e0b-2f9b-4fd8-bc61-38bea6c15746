package com.logictrue.word.dto.table;

/**
 * 合并单元格配置
 */
public class MergeConfig {

    /**
     * 起始行索引
     */
    private Integer startRow;

    /**
     * 起始列索引
     */
    private Integer startCol;

    /**
     * 结束行索引
     */
    private Integer endRow;

    /**
     * 结束列索引
     */
    private Integer endCol;

    /**
     * 合并单元格内容
     */
    private String content;

    public MergeConfig() {
    }

    public MergeConfig(Integer startRow, Integer startCol, Integer endRow, Integer endCol, String content) {
        this.startRow = startRow;
        this.startCol = startCol;
        this.endRow = endRow;
        this.endCol = endCol;
        this.content = content;
    }

    public Integer getStartRow() {
        return startRow;
    }

    public void setStartRow(Integer startRow) {
        this.startRow = startRow;
    }

    public Integer getStartCol() {
        return startCol;
    }

    public void setStartCol(Integer startCol) {
        this.startCol = startCol;
    }

    public Integer getEndRow() {
        return endRow;
    }

    public void setEndRow(Integer endRow) {
        this.endRow = endRow;
    }

    public Integer getEndCol() {
        return endCol;
    }

    public void setEndCol(Integer endCol) {
        this.endCol = endCol;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
