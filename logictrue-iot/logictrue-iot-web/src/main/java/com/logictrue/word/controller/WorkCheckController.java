package com.logictrue.word.controller;

import com.logictrue.common.core.web.controller.BaseController;
import com.logictrue.common.core.web.domain.AjaxResult;
import com.logictrue.word.dto.table.TableDataDto;
import com.logictrue.word.entity.vo.WorkCheckVo;
import com.logictrue.word.service.GenAndExportService;
import com.logictrue.word.service.IWordExportService;
import com.logictrue.word.service.IWorkCheckService;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 检验记录表Controller
 */
@Slf4j
@RestController
@RequestMapping("/workCheck")
public class WorkCheckController extends BaseController {

    @Autowired
    private IWorkCheckService workCheckService;

    @Autowired
    private IWordExportService newWordExportService;

    @Autowired
    private GenAndExportService genAndExportService;


    /**
     * 生成检验记录表数据列表并批量保存（支持拆分）
     */
    @GetMapping("/generateList")
    public AjaxResult generateList(
            @ApiParam("车辆ID") @RequestParam String carId) {
        try {
            if (carId == null || carId.trim().isEmpty()) {
                return AjaxResult.error("车辆ID不能为空");
            }

            // 1. 生成表格数据列表
            List<TableDataDto> tableDataDtoList = workCheckService.generateWorkCheckTableList(carId);

            // 2. 调用批量保存方法
            boolean result = workCheckService.saveWorkCheckTableList(carId, tableDataDtoList);

            return result ? AjaxResult.success("生成并批量保存成功") : AjaxResult.error("批量保存失败");
        } catch (Exception e) {
            logger.error("生成检验记录表数据列表并批量保存失败", e);
            return AjaxResult.error("生成并批量保存失败：" + e.getMessage());
        }
    }

    /**
     * 生成并保存检验记录表数据
     */
    @GetMapping("/generate")
    public AjaxResult generate(
            @ApiParam("车辆ID") @RequestParam String carId) {
        try {
            if (carId == null || carId.trim().isEmpty()) {
                return AjaxResult.error("车辆ID不能为空");
            }

            // 1. 生成表格数据
            TableDataDto tableDataDto = workCheckService.generateWorkCheckTable(carId);

            // 2. 保存表格数据
            boolean result = workCheckService.saveWorkCheckTable(carId, tableDataDto);

            return result ? AjaxResult.success("生成并保存成功") : AjaxResult.error("生成并保存失败");
        } catch (Exception e) {
            logger.error("生成并保存检验记录表数据失败", e);
            return AjaxResult.error("生成并保存失败：" + e.getMessage());
        }
    }

    /**
     * 生成检验记录表并导出
     */
    @GetMapping("/genAndExport")
    public ResponseEntity<byte[]> genAndExport(@RequestParam String carId, @RequestParam(required = false) String range) {
        try {
            if (carId == null || carId.trim().isEmpty()) {
                return ResponseEntity.ok()
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(AjaxResult.error("车辆ID不能为空").toString().getBytes());
            }

            // 1. 生成表格数据列表
            List<TableDataDto> tableDataDtoList = workCheckService.generateRangeTableList(carId, range);

            // 3. 生成Word文档
            byte[] wordBytes = genAndExportService.genAndExport(tableDataDtoList);

            // 5. 设置文件下载响应头
            String fileName = "检验记录表_" + carId + "_" + System.currentTimeMillis() + ".docx";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(wordBytes);

        } catch (Exception e) {
            logger.error("生成并导出检验记录表失败", e);
            try {
                return ResponseEntity.ok()
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(AjaxResult.error("生成并导出失败：" + e.getMessage()).toString().getBytes());
            } catch (Exception jsonException) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        }
    }

    /**
     * 测试根据设计表ID导出Word文档（返回JSON结果）
     */
    @GetMapping("/export/{designWordId}")
    public ResponseEntity<byte[]> exportByDesignId(@ApiParam("设计表ID") @PathVariable Long designWordId) {
        try {
            log.info("开始测试根据设计表ID导出Word文档，设计表ID: {}", designWordId);

            // 生成Word文档字节数组
            byte[] wordBytes = newWordExportService.generateWordByDesignId(designWordId);

            log.info("Word文档生成成功，设计表ID: {}，文档大小: {} bytes", designWordId, wordBytes.length);

            String fileName = "test" + System.currentTimeMillis() + ".docx";

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("复杂表格Word文档导出成功，文件名: {}, 大小: {} bytes", fileName, wordBytes.length);
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(wordBytes);
        } catch (Exception e) {
            log.error("测试导出Word文档失败，设计表ID: " + designWordId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/export")
    public ResponseEntity<byte[]> exportByDesignIds(@RequestParam("ids") String designWordIds) {
        try {
            List<Long> designWordId = Arrays.stream(designWordIds.split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());

            // 生成Word文档字节数组
            byte[] wordBytes = newWordExportService.generateWordByDesignIdList(designWordId);

            log.info("Word文档生成成功，文档大小: {} bytes", wordBytes.length);

            String fileName = "export" + System.currentTimeMillis() + ".docx";

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("复杂表格Word文档导出成功，文件名: {}, 大小: {} bytes", fileName, wordBytes.length);
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(wordBytes);
        } catch (Exception e) {
            log.error("测试导出Word文档失败: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/exportAll")
    public ResponseEntity<byte[]> exportAll(@RequestParam("carId") String carId) {
        try {
            // 生成Word文档字节数组
            byte[] wordBytes = newWordExportService.generateAllDesignWords(carId);

            log.info("Word文档生成成功，文档大小: {} bytes", wordBytes.length);

            String fileName = "all_" + System.currentTimeMillis() + ".docx";

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("复杂表格Word文档导出成功，文件名: {}, 大小: {} bytes", fileName, wordBytes.length);
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(wordBytes);
        } catch (Exception e) {
            log.error("测试导出Word文档失败: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

}
