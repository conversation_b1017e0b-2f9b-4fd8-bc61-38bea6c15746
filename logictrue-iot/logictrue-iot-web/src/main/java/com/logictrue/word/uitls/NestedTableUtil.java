package com.logictrue.word.uitls;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.logictrue.word.dto.table.CellData;
import com.logictrue.word.dto.table.MergeConfig;
import com.logictrue.word.dto.table.NestedTableConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 嵌套表格工具类
 */
@Slf4j
public class NestedTableUtil {


    /**
     * 将特定字符串转换为NestedTableConfig
     * 字符串格式示例：
     * [
     *   [
     *     "1-1",
     *     {
     *       "colspan": 1,
     *       "rowspan": 2,
     *       "value": "第一二行第一列合并"
     *     }
     *   ],
     *   ...
     * ]
     *
     * @param jsonString JSON字符串
     * @return NestedTableConfig对象
     */
    public static NestedTableConfig convertStringToNestedTableConfig(String jsonString, String tableValue) {
        try {
            // 解析JSON字符串
            List<List<Object>> data = JSON.parseObject(jsonString, new TypeReference<List<List<Object>>>() {});

            Map<String,String> valueMap = JSON.parseObject(tableValue, new TypeReference<Map<String, String>>() {});

            // 分析数据，确定需要的行列数
            int maxRow = 0;
            int maxCol = 0;

            for (List<Object> cellData : data) {
                String position = (String) cellData.get(0);
                Map<String, Object> config = (Map<String, Object>) cellData.get(1);

                String[] positionParts = position.split("-");
                int row = Integer.parseInt(positionParts[0]) - 1; // 转换为0-based
                int col = Integer.parseInt(positionParts[1]) - 1; // 转换为0-based

                // 只有非合并单元格才计算行列数
                int colspan = ((Number) config.getOrDefault("colspan", 1)).intValue();
                int rowspan = ((Number) config.getOrDefault("rowspan", 1)).intValue();

                if (colspan > 0 && rowspan > 0) {
                    maxRow = Math.max(maxRow, row + rowspan);
                    maxCol = Math.max(maxCol, col + colspan);
                }

                if (valueMap.containsKey(position)) {
                    config.put("value", valueMap.get(position));
                }

            }

            // 确保最小行列数
            maxRow = Math.max(maxRow, 1);
            maxCol = Math.max(maxCol, 1);

            // 创建嵌套表格数据
            NestedTableConfig.NestedTableData nestedTableData = new NestedTableConfig.NestedTableData();

            // 初始化单元格数据
            List<List<CellData>> cellRows = new ArrayList<>();
            for (int i = 0; i < maxRow; i++) {
                List<CellData> row = new ArrayList<>();
                for (int j = 0; j < maxCol; j++) {
                    CellData cellData = new CellData();
                    cellData.setContent("");
                    row.add(cellData);
                }
                cellRows.add(row);
            }

            // 填充单元格数据和合并单元格信息
            List<MergeConfig> merges = new ArrayList<>();

            for (List<Object> cellData : data) {
                String position = (String) cellData.get(0);
                Map<String, Object> config = (Map<String, Object>) cellData.get(1);

                String[] positionParts = position.split("-");
                int row = Integer.parseInt(positionParts[0]) - 1; // 转换为0-based
                int col = Integer.parseInt(positionParts[1]) - 1; // 转换为0-based

                int colspan = ((Number) config.getOrDefault("colspan", 1)).intValue();
                int rowspan = ((Number) config.getOrDefault("rowspan", 1)).intValue();
                String value = (String) config.getOrDefault("value", "");

                // 只有在有效范围内才设置数据
                if (row < maxRow && col < maxCol) {
                    // 设置单元格内容
                    CellData cell = cellRows.get(row).get(col);
                    cell.setContent(value);

                    // 如果是合并单元格，添加合并配置
                    if (colspan > 1 || rowspan > 1) {
                        MergeConfig mergeConfig = new MergeConfig();
                        mergeConfig.setStartRow(row);
                        mergeConfig.setStartCol(col);
                        mergeConfig.setEndRow(row + rowspan - 1);
                        mergeConfig.setEndCol(col + colspan - 1);
                        mergeConfig.setContent(value);
                        merges.add(mergeConfig);
                    }
                }
            }

            // 设置嵌套表格数据
            nestedTableData.setCellRows(cellRows);
            nestedTableData.setMerges(merges);

            List<Integer> columnWidths = new ArrayList<>();
            int width = 160 / maxCol;
            for (int i = 0; i < maxCol; i++) {
                columnWidths.add(width);
            }

            nestedTableData.setColumnWidths(columnWidths);

            // 创建元数据
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("totalRows", maxRow);
            metadata.put("totalColumns", maxCol);
            metadata.put("hasMergedCells", !merges.isEmpty());
            nestedTableData.setMetadata(metadata);

            // 创建嵌套表格配置
            NestedTableConfig nestedTableConfig = new NestedTableConfig();
            nestedTableConfig.setEnabled(true);
            nestedTableConfig.setConfig(nestedTableData);

            // 创建嵌套表格元数据
            NestedTableConfig.NestedTableMetadata nestedTableMetadata = new NestedTableConfig.NestedTableMetadata();
            nestedTableMetadata.setParentRowIndex(0);
            nestedTableMetadata.setParentCellIndex(0);
            nestedTableMetadata.setNestedLevel(1);
            nestedTableMetadata.setHasContent(true);
            nestedTableConfig.setMetadata(nestedTableMetadata);

            return nestedTableConfig;

        } catch (Exception e) {
            log.error("转换字符串为NestedTableConfig失败", e);
            throw new RuntimeException("转换字符串为NestedTableConfig失败", e);
        }
    }

    /**
     * 测试方法：验证字符串转换功能
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 测试数据
        String testJson = "[\n" +
                "  [\n" +
                "    \"1-1\",\n" +
                "    {\n" +
                "      \"colspan\": 1,\n" +
                "      \"rowspan\": 2,\n" +
                "      \"value\": \"第一二行第一列合并\"\n" +
                "    }\n" +
                "  ],\n" +
                "  [\n" +
                "    \"2-1\",\n" +
                "    {\n" +
                "      \"colspan\": 0,\n" +
                "      \"rowspan\": 0,\n" +
                "      \"value\": \"\"\n" +
                "    }\n" +
                "  ],\n" +
                "  [\n" +
                "    \"1-2\",\n" +
                "    {\n" +
                "      \"colspan\": 2,\n" +
                "      \"rowspan\": 1,\n" +
                "      \"value\": \"第一行第二三列合并\"\n" +
                "    }\n" +
                "  ],\n" +
                "  [\n" +
                "    \"1-3\",\n" +
                "    {\n" +
                "      \"colspan\": 0,\n" +
                "      \"rowspan\": 0,\n" +
                "      \"value\": \"\"\n" +
                "    }\n" +
                "  ],\n" +
                "  [\n" +
                "    \"2-2\",\n" +
                "    {\n" +
                "      \"colspan\": 2,\n" +
                "      \"rowspan\": 2,\n" +
                "      \"value\": \"第二三行第二三列合并\"\n" +
                "    }\n" +
                "  ],\n" +
                "  [\n" +
                "    \"2-3\",\n" +
                "    {\n" +
                "      \"colspan\": 0,\n" +
                "      \"rowspan\": 0,\n" +
                "      \"value\": \"\"\n" +
                "    }\n" +
                "  ],\n" +
                "  [\n" +
                "    \"3-2\",\n" +
                "    {\n" +
                "      \"colspan\": 0,\n" +
                "      \"rowspan\": 0,\n" +
                "      \"value\": \"\"\n" +
                "    }\n" +
                "  ],\n" +
                "  [\n" +
                "    \"3-3\",\n" +
                "    {\n" +
                "      \"colspan\": 0,\n" +
                "      \"rowspan\": 0,\n" +
                "      \"value\": \"\"\n" +
                "    }\n" +
                "  ],\n" +
                "  [\n" +
                "    \"3-1\",\n" +
                "    {\n" +
                "      \"colspan\": 1,\n" +
                "      \"rowspan\": 1,\n" +
                "      \"value\": \"可以的\"\n" +
                "    }\n" +
                "  ]\n" +
                "]";

        try {
            System.out.println("开始测试字符串转换功能...");

            // 转换字符串为NestedTableConfig
            NestedTableConfig config = convertStringToNestedTableConfig(testJson, null);

            // 输出结果
            System.out.println("转换成功！");
            System.out.println("是否启用: " + config.getEnabled());
            System.out.println("嵌套表格数据: " + config.getConfig());
            System.out.println("元数据: " + config.getMetadata());

            // 输出详细信息
            if (config.getConfig() != null) {
                System.out.println("总行数: " + config.getConfig().getMetadata().get("totalRows"));
                System.out.println("总列数: " + config.getConfig().getMetadata().get("totalColumns"));
                System.out.println("是否有合并单元格: " + config.getConfig().getMetadata().get("hasMergedCells"));
                System.out.println("合并单元格数量: " + config.getConfig().getMerges().size());

                // 输出单元格数据
                System.out.println("单元格数据:");
                for (int i = 0; i < config.getConfig().getCellRows().size(); i++) {
                    for (int j = 0; j < config.getConfig().getCellRows().get(i).size(); j++) {
                        CellData cell = config.getConfig().getCellRows().get(i).get(j);
                        System.out.printf("行%d列%d: %s%n", i+1, j+1, cell.getContent());
                    }
                }

                // 输出合并单元格信息
                System.out.println("合并单元格信息:");
                for (MergeConfig merge : config.getConfig().getMerges()) {
                    System.out.printf("合并区域: 行%d-%d, 列%d-%d, 内容: %s%n",
                            merge.getStartRow()+1, merge.getEndRow()+1,
                            merge.getStartCol()+1, merge.getEndCol()+1,
                            merge.getContent());
                }
            }

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
