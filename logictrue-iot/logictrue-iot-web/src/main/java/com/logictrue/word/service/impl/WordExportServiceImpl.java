package com.logictrue.word.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.logictrue.word.dto.JsonTableExportRequest;
import com.logictrue.word.dto.SimpleWordExportRequest;
import com.logictrue.word.entity.DesignWord;
import com.logictrue.word.entity.DrlDataRecords;
import com.logictrue.word.entity.vo.WorkCheckRecordVo;
import com.logictrue.word.mapper.DrlDataRecordsMapper;
import com.logictrue.word.mapper.DrlWorkCheckRecordsMapper;
import com.logictrue.word.service.IDesignWordService;
import com.logictrue.word.service.IWordExportService;
import com.logictrue.word.service.SimpleWordExportService;
import com.logictrue.word.service.WordExportService;
import org.apache.poi.common.usermodel.PictureType;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.drawingml.x2006.wordprocessingDrawing.CTInline;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTbl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Word导出服务实现类
 */
@Service
public class WordExportServiceImpl implements IWordExportService {

    private static final Logger logger = LoggerFactory.getLogger(WordExportServiceImpl.class);

    @Autowired
    private IDesignWordService designWordService;

    @Autowired
    private DrlWorkCheckRecordsMapper drlWorkCheckRecordsMapper;

    @Autowired
    private WordExportService wordExportService;

    @Autowired
    private SimpleWordExportService simpleWordExportService;

    @Override
    public byte[] generateWordByDesignId(Long designWordId) throws Exception {
        // 1. 获取设计表数据
        DesignWord designWord = designWordService.selectDesignWordById(designWordId);
        if (designWord == null) {
            throw new RuntimeException("设计表数据不存在，ID: " + designWordId);
        }

        // 2. 解析绑定的模板ID列表
        List<Integer> templateIds = parseTemplateIds(designWord.getBoundTemplateIds());
        if (templateIds.isEmpty()) {
            logger.warn("设计表ID: {} 没有绑定任何模板ID", designWordId);
        }

        // 3. 查询检验记录数据
        List<WorkCheckRecordVo> dataRecords = queryDataRecords(designWord.getCarId(), templateIds);

        // 4. 解析字段绑定配置和行模板绑定关系
        Map<String, Integer> fieldBindingConfig = parseFieldBindingConfig(designWord.getFieldBindingConfig());
        Map<Integer, List<Integer>> rowTemplateBindings = parseRowTemplateBindings(designWord.getRowTemplateBindings());

        // 5. 构建JSON导出请求并生成Word文档
        return generateWordWithJsonFormat(designWord, dataRecords, fieldBindingConfig, rowTemplateBindings);
    }

    /**
     * 解析模板ID列表
     */
    private List<Integer> parseTemplateIds(String boundTemplateIds) {
        List<Integer> templateIds = new ArrayList<>();
        if (StringUtils.hasText(boundTemplateIds)) {
            String[] ids = boundTemplateIds.split(",");
            for (String id : ids) {
                try {
                    templateIds.add(Integer.parseInt(id.trim()));
                } catch (NumberFormatException e) {
                    logger.warn("无效的模板ID: {}", id);
                }
            }
        }
        return templateIds;
    }

    /**
     * 查询检验记录数据
     */
    private List<WorkCheckRecordVo> queryDataRecords(String carId, List<Integer> templateIds) {
        if (templateIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<WorkCheckRecordVo> dataRecords;
        if (StringUtils.hasText(carId)) {
            // 根据车号和模板ID查询
            dataRecords = drlWorkCheckRecordsMapper.getByCarIdAndTemplateIds(carId, templateIds);
        } else {
            // 仅根据模板ID查询
            dataRecords = drlWorkCheckRecordsMapper.getByTemplateIds(templateIds);
        }

        logger.info("查询到检验记录数据 {} 条", dataRecords.size());
        return dataRecords;
    }

    /**
     * 解析字段绑定配置
     */
    private Map<String, Integer> parseFieldBindingConfig(String fieldBindingConfigJson) {
        Map<String, Integer> config = new HashMap<>();
        if (StringUtils.hasText(fieldBindingConfigJson)) {
            try {
                config = JSON.parseObject(fieldBindingConfigJson, new TypeReference<Map<String, Integer>>() {});
            } catch (Exception e) {
                logger.error("解析字段绑定配置失败", e);
            }
        }

        // 设置默认配置
        if (config.isEmpty()) {
            config.put("result", 2);
            config.put("month_str", 3);
            config.put("day_str", 4);
            config.put("check_user_name", 5);
            config.put("bzz", 6);
            config.put("jyy", 7);
        }

        return config;
    }

    /**
     * 解析行模板绑定关系
     * 支持格式：{0:[1,2,3,4,5],1:[6,7,8,9,10],2:[11,12,13,14,15,16,17,18,19,20,21,22,23,24,25]}
     * 其中键是行索引，值是该行对应的模板ID列表
     */
    private Map<Integer, List<Integer>> parseRowTemplateBindings(String rowTemplateBindingsJson) {
        Map<Integer, List<Integer>> bindings = new HashMap<>();
        if (StringUtils.hasText(rowTemplateBindingsJson)) {
            try {
                // 解析为 Map<Integer, List<Integer>> 格式
                bindings = JSON.parseObject(rowTemplateBindingsJson,
                    new TypeReference<Map<Integer, List<Integer>>>() {});

                // 验证数据格式
                for (Map.Entry<Integer, List<Integer>> entry : bindings.entrySet()) {
                    if (entry.getValue() == null || entry.getValue().isEmpty()) {
                        logger.warn("行索引 {} 的模板ID列表为空", entry.getKey());
                    }
                }

                logger.info("解析行模板绑定关系成功，共 {} 行绑定数据", bindings.size());
            } catch (Exception e) {
                logger.error("解析行模板绑定关系失败", e);
                // 如果解析失败，尝试兼容旧格式
                try {
                    Map<Integer, Integer> oldFormatBindings = JSON.parseObject(rowTemplateBindingsJson,
                        new TypeReference<Map<Integer, Integer>>() {});

                    // 转换为新格式
                    for (Map.Entry<Integer, Integer> entry : oldFormatBindings.entrySet()) {
                        bindings.put(entry.getKey(), Arrays.asList(entry.getValue()));
                    }
                    logger.info("兼容旧格式转换成功，共 {} 行绑定数据", bindings.size());
                } catch (Exception e2) {
                    logger.error("兼容旧格式转换也失败", e2);
                }
            }
        }
        return bindings;
    }

    /**
     * 为表格行合并多个模板ID的数据记录
     * @param templateIds 模板ID列表
     * @param templateDataMap 模板ID到数据记录的映射
     * @param fieldBindingConfig 字段绑定配置
     * @return 合并后的数据记录
     */
    private DrlDataRecords mergeRecordsForTableRow(List<Integer> templateIds,
                                                   Map<Integer, List<DrlDataRecords>> templateDataMap,
                                                   Map<String, Integer> fieldBindingConfig) {

        if (templateIds == null || templateIds.isEmpty()) {
            return null;
        }

        // 使用第一个模板ID的记录作为基础
        Integer firstTemplateId = templateIds.get(0);
        List<DrlDataRecords> firstRecords = templateDataMap.get(firstTemplateId);

        if (firstRecords == null || firstRecords.isEmpty()) {
            return null;
        }

        DrlDataRecords mergedRecord = firstRecords.get(0);

        // 如果有多个模板ID，合并它们的字段数据
        if (templateIds.size() > 1) {
            // 创建合并后的记录（这里可以根据业务需求调整合并策略）
            for (int i = 1; i < templateIds.size(); i++) {
                Integer templateId = templateIds.get(i);
                List<DrlDataRecords> records = templateDataMap.get(templateId);

                if (records != null && !records.isEmpty()) {
                    DrlDataRecords record = records.get(0);
                    // 合并字段（这里简单覆盖，可以根据需要调整合并逻辑）
                    mergeRecordFields(mergedRecord, record, fieldBindingConfig);
                }
            }
        }

        return mergedRecord;
    }

    /**
     * 合并两个记录的字段
     * @param target 目标记录
     * @param source 源记录
     * @param fieldBindingConfig 字段绑定配置
     */
    private void mergeRecordFields(DrlDataRecords target, DrlDataRecords source,
                                  Map<String, Integer> fieldBindingConfig) {

        // 根据字段绑定配置决定合并策略
        // 这里简单实现：如果目标字段为空，则使用源字段的值

        if (target.getResult() == null || target.getResult().trim().isEmpty()) {
            target.setResult(source.getResult());
        }

        if (target.getMonthStr() == null || target.getMonthStr().trim().isEmpty()) {
            target.setMonthStr(source.getMonthStr());
        }

        if (target.getDayStr() == null || target.getDayStr().trim().isEmpty()) {
            target.setDayStr(source.getDayStr());
        }

        if (target.getCheckUserName() == null || target.getCheckUserName().trim().isEmpty()) {
            target.setCheckUserName(source.getCheckUserName());
        }

        if (target.getBzz() == null || target.getBzz().trim().isEmpty()) {
            target.setBzz(source.getBzz());
        }

        if (target.getJyy() == null || target.getJyy().trim().isEmpty()) {
            target.setJyy(source.getJyy());
        }
    }

    /**
     * 填充表格行数据
     */
    private void fillTableRow(XWPFTableRow row, DrlDataRecords record, Map<String, Integer> fieldBindingConfig) {
        // 初始化所有单元格
        String[] cellValues = new String[8];
        Arrays.fill(cellValues, "");

        // 固定列
        cellValues[0] = record.getCheckName() != null ? record.getCheckName() : "";
        cellValues[1] = record.getCheckContent() != null ? record.getCheckContent() : "";

        // 根据字段绑定配置填充其他列
        if (fieldBindingConfig.containsKey("result") && record.getResult() != null) {
            int colIndex = fieldBindingConfig.get("result");
            if (colIndex >= 0 && colIndex < 8) {
                cellValues[colIndex] = record.getResult();
            }
        }

        if (fieldBindingConfig.containsKey("month_str") && record.getMonthStr() != null) {
            int colIndex = fieldBindingConfig.get("month_str");
            if (colIndex >= 0 && colIndex < 8) {
                cellValues[colIndex] = record.getMonthStr();
            }
        }

        if (fieldBindingConfig.containsKey("day_str") && record.getDayStr() != null) {
            int colIndex = fieldBindingConfig.get("day_str");
            if (colIndex >= 0 && colIndex < 8) {
                cellValues[colIndex] = record.getDayStr();
            }
        }

        if (fieldBindingConfig.containsKey("check_user_name") && record.getCheckUserName() != null) {
            int colIndex = fieldBindingConfig.get("check_user_name");
            if (colIndex >= 0 && colIndex < 8) {
                cellValues[colIndex] = record.getCheckUserName();
            }
        }

        if (fieldBindingConfig.containsKey("bzz") && record.getBzz() != null) {
            int colIndex = fieldBindingConfig.get("bzz");
            if (colIndex >= 0 && colIndex < 8) {
                cellValues[colIndex] = record.getBzz();
            }
        }

        if (fieldBindingConfig.containsKey("jyy") && record.getJyy() != null) {
            int colIndex = fieldBindingConfig.get("jyy");
            if (colIndex >= 0 && colIndex < 8) {
                cellValues[colIndex] = record.getJyy();
            }
        }

        // 设置单元格值
        for (int i = 0; i < cellValues.length; i++) {
            if (i < row.getTableCells().size()) {
                row.getCell(i).setText(cellValues[i]);
            } else {
                row.addNewTableCell().setText(cellValues[i]);
            }
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(DesignWord designWord) {
        StringBuilder fileName = new StringBuilder();

        if (StringUtils.hasText(designWord.getTitle())) {
            fileName.append(designWord.getTitle());
        } else {
            fileName.append("检验记录表");
        }

        if (StringUtils.hasText(designWord.getCarId())) {
            fileName.append("_").append(designWord.getCarId());
        }

        if (StringUtils.hasText(designWord.getPageName())) {
            fileName.append("_").append(designWord.getPageName());
        }

        fileName.append(".docx");

        return fileName.toString();
    }

    /**
     * 使用JSON格式生成Word文档
     * 参考CheckRecordExportService的exportCurrentPage方法
     */
    private byte[] generateWordWithJsonFormat(DesignWord designWord, List<WorkCheckRecordVo> dataRecords,
                                           Map<String, Integer> fieldBindingConfig,
                                           Map<Integer, List<Integer>> rowTemplateBindings) throws IOException {
        logger.info("开始使用JSON格式生成Word文档，设计表ID: {}, 检验记录数量: {}", designWord.getId(), dataRecords.size());

        // 1. 构建JsonTableExportRequest
        JsonTableExportRequest exportRequest = new JsonTableExportRequest();

        // 2. 解析表格配置
        Map<String, Object> tableConfig = parseTableConfig(designWord.getTableConfig());

        // 3. 设置表头相关配置
        setHeaderConfigs(exportRequest, tableConfig);

        // 4. 构建数据行
        List<List<JsonTableExportRequest.CellData>> cellRows = buildCellRows(
                designWord, dataRecords, fieldBindingConfig, rowTemplateBindings);
        exportRequest.setCellRows(cellRows);

        // 5. 设置合并单元格（如果有）
        exportRequest.setMerges(getMerges(tableConfig));

        // 6. 设置元数据
        JsonTableExportRequest.MetadataInfo metadata = new JsonTableExportRequest.MetadataInfo();
        metadata.setTitle(designWord.getTitle());
        metadata.setTotalRows(cellRows.size());
        metadata.setTotalColumns(8); // 固定为8列
        metadata.setHeaderRows(2);   // 默认2行表头
        metadata.setExportTime(java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        exportRequest.setMetadata(metadata);

        // 7. 设置文档标题
        exportRequest.setTitle(designWord.getTitle());

        exportRequest.setHasNestedTable(designWord.getHasNestedTable() == 1);

        // 使用WordExportService导出
        return wordExportService.exportNewJsonFormatToWord(exportRequest);
    }

    /**
     * 解析表格配置
     */
    private Map<String, Object> parseTableConfig(String tableConfigJson) {
        try {
            if (StringUtils.hasText(tableConfigJson)) {
                return JSON.parseObject(tableConfigJson, Map.class);
            }
        } catch (Exception e) {
            logger.warn("解析表格配置失败，使用默认配置", e);
        }
        return getDefaultTableConfig();
    }

    /**
     * 获取默认表格配置
     */
    private Map<String, Object> getDefaultTableConfig() {
        Map<String, Object> config = new HashMap<>();

        // 默认表头
        List<List<String>> headers = Arrays.asList(
                Arrays.asList("检查工序\n名称", "检 查 项 目 及 技 术 条 件", "实 际 检 查 结 果", "完工", "", "操作员", "班组长", "检验员"),
                Arrays.asList("", "", "", "月", "日", "", "", "")
        );
        config.put("headers", headers);

        // 默认表头合并
        List<Map<String, Object>> headerMerges = Arrays.asList(
                createMerge(0, 0, 1, 0, "检查工序\n名称"),
                createMerge(0, 1, 1, 1, "检 查 项 目 及 技 术 条 件"),
                createMerge(0, 2, 1, 2, "实 际 检 查 结 果"),
                createMerge(0, 3, 0, 4, "完工"),
                createMerge(0, 5, 1, 5, "操作员"),
                createMerge(0, 6, 1, 6, "班组长"),
                createMerge(0, 7, 1, 7, "检验员")
        );
        config.put("headerMerges", headerMerges);

        // 默认列宽配置
        Map<String, Object> headerWidthConfig = new HashMap<>();
        headerWidthConfig.put("columnWidths", Arrays.asList(100, 460, 160, 32, 32, 32, 32, 32));
        headerWidthConfig.put("headerHeights", Arrays.asList(35, 35));
        config.put("headerWidthConfig", headerWidthConfig);

        // 默认纵向文字配置
        config.put("verticalHeadersConfig", Arrays.asList(false, false, false, false, false, true, true, true));

        return config;
    }

    /**
     * 创建合并单元格配置
     */
    private Map<String, Object> createMerge(int startRow, int startCol, int endRow, int endCol, String content) {
        Map<String, Object> merge = new HashMap<>();
        merge.put("startRow", startRow);
        merge.put("startCol", startCol);
        merge.put("endRow", endRow);
        merge.put("endCol", endCol);
        merge.put("content", content);
        return merge;
    }

    /**
     * 设置表头相关配置
     */
    private void setHeaderConfigs(JsonTableExportRequest exportRequest, Map<String, Object> tableConfig) {
        // 设置表头
        exportRequest.setHeaders(getHeaders(tableConfig));
        exportRequest.setHeaderMerges(getHeaderMerges(tableConfig));
        exportRequest.setHeaderWidthConfig(getHeaderWidthConfig(tableConfig));
        exportRequest.setVerticalHeadersConfig(getVerticalHeadersConfig(tableConfig));
    }

    /**
     * 获取表头
     */
    @SuppressWarnings("unchecked")
    private List<List<String>> getHeaders(Map<String, Object> tableConfig) {
        Object headers = tableConfig.get("headers");
        if (headers instanceof List) {
            return (List<List<String>>) headers;
        }
        return getDefaultTableConfig().get("headers") != null ?
                (List<List<String>>) getDefaultTableConfig().get("headers") : new ArrayList<>();
    }

    /**
     * 获取表头合并配置
     */
    @SuppressWarnings("unchecked")
    private List<JsonTableExportRequest.MergeConfig> getHeaderMerges(Map<String, Object> tableConfig) {
        Object headerMerges = tableConfig.get("headerMerges");
        if (headerMerges instanceof List) {
            List<Map<String, Object>> mergeList = (List<Map<String, Object>>) headerMerges;
            return mergeList.stream().map(this::convertToMergeConfig).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 获取表头宽度配置
     */
    @SuppressWarnings("unchecked")
    private JsonTableExportRequest.HeaderWidthConfig getHeaderWidthConfig(Map<String, Object> tableConfig) {
        Object widthConfig = tableConfig.get("headerWidthConfig");
        if (widthConfig instanceof Map) {
            Map<String, Object> config = (Map<String, Object>) widthConfig;
            JsonTableExportRequest.HeaderWidthConfig headerWidthConfig = new JsonTableExportRequest.HeaderWidthConfig();

            Object columnWidths = config.get("columnWidths");
            if (columnWidths instanceof List) {
                headerWidthConfig.setColumnWidths((List<Integer>) columnWidths);
            }

            Object headerHeights = config.get("headerHeights");
            if (headerHeights instanceof List) {
                headerWidthConfig.setHeaderHeights((List<Integer>) headerHeights);
            }

            return headerWidthConfig;
        }
        return null;
    }

    /**
     * 获取纵向文字配置
     */
    @SuppressWarnings("unchecked")
    private List<Boolean> getVerticalHeadersConfig(Map<String, Object> tableConfig) {
        Object verticalConfig = tableConfig.get("verticalHeadersConfig");
        if (verticalConfig instanceof List) {
            return (List<Boolean>) verticalConfig;
        }
        return new ArrayList<>();
    }

    /**
     * 获取合并单元格配置
     */
    @SuppressWarnings("unchecked")
    private List<JsonTableExportRequest.MergeConfig> getMerges(Map<String, Object> tableConfig) {
        Object merges = tableConfig.get("merges");
        if (merges instanceof List) {
            List<Map<String, Object>> mergeList = (List<Map<String, Object>>) merges;
            return mergeList.stream().map(this::convertToMergeConfig).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 转换合并单元格配置
     */
    private JsonTableExportRequest.MergeConfig convertToMergeConfig(Map<String, Object> mergeMap) {
        JsonTableExportRequest.MergeConfig mergeConfig = new JsonTableExportRequest.MergeConfig();
        mergeConfig.setStartRow((Integer) mergeMap.get("startRow"));
        mergeConfig.setStartCol((Integer) mergeMap.get("startCol"));
        mergeConfig.setEndRow((Integer) mergeMap.get("endRow"));
        mergeConfig.setEndCol((Integer) mergeMap.get("endCol"));
        mergeConfig.setContent((String) mergeMap.get("content"));
        return mergeConfig;
    }

    /**
     * 构建数据行
     * 解析绑定关系后替换tableData里cellRows的数据
     * 支持一行对应多个模板ID的情况，将多条记录合并到一行中
     */
    private List<List<JsonTableExportRequest.CellData>> buildCellRows(
            DesignWord designWord,
            List<WorkCheckRecordVo> dataRecords,
            Map<String, Integer> fieldBindingConfig,
            Map<Integer, List<Integer>> rowTemplateBindings) {

        List<List<JsonTableExportRequest.CellData>> cellRows = new ArrayList<>();

        // 1. 先解析设计表中的现有tableData，作为基础数据
        JSONObject existingTableData = parseExistingTableData(designWord.getTableData());
        JSONArray originCellRows = existingTableData.getJSONArray("cellRows");

        // 2. 根据行模板绑定关系，构建模板ID到数据记录的映射
        Map<Integer, List<WorkCheckRecordVo>> templateDataMap = dataRecords.stream()
                .collect(Collectors.groupingBy(WorkCheckRecordVo::getCheckId));

        // 3. 遍历原始行数据，替换绑定关系的列数据，转换为CellData对象
        for (int rowIndex = 0; rowIndex < originCellRows.size(); rowIndex++) {
            JSONArray rowCells = originCellRows.getJSONArray(rowIndex);

            // 获取当前行绑定的模板ID列表
            List<Integer> templateIds = rowTemplateBindings.get(rowIndex);

            // 合并当前行所有模板ID对应的数据记录
            Map<Integer, String> fieldColMap = mergeRecordsForRow(templateIds, templateDataMap, fieldBindingConfig);

            List<JsonTableExportRequest.CellData> cellRow = new ArrayList<>();

            for (int colIndex = 0; colIndex < rowCells.size(); colIndex++) {
                JSONObject cellData = rowCells.getJSONObject(colIndex);
                String colValue = fieldColMap.get(colIndex);

                if (!StringUtils.hasText(colValue)) {
                    colValue = cellData.getString("content");
                }

                cellRow.add(createCellDataWithFormula(
                        colValue,
                        cellData.getInteger("width"),
                        cellData.getInteger("height"),
                        cellData
                ));
            }
            cellRows.add(cellRow);
        }

        logger.info("构建数据行完成，共 {} 行", cellRows.size());
        return cellRows;
    }

    /**
     * 合并一行中多个模板ID对应的数据记录
     * @param templateIds 模板ID列表
     * @param templateDataMap 模板ID到数据记录的映射
     * @param fieldBindingConfig 字段绑定配置
     * @return 合并后的字段映射（列索引 -> 字段值）
     */
    private Map<Integer, String> mergeRecordsForRow(List<Integer> templateIds,
                                                   Map<Integer, List<WorkCheckRecordVo>> templateDataMap,
                                                   Map<String, Integer> fieldBindingConfig) {
        Map<Integer, String> fieldColMap = new HashMap<>();

        if (templateIds == null || templateIds.isEmpty()) {
            return fieldColMap;
        }

        // 遍历当前行的所有模板ID
        for (int i = 0; i < templateIds.size(); i++) {
            Integer templateId = templateIds.get(i);
            List<WorkCheckRecordVo> records = templateDataMap.get(templateId);

            if (records != null && !records.isEmpty()) {
                // 对于每个模板ID，取第一条记录（可以根据需要调整合并策略）
                WorkCheckRecordVo record = records.get(0);

                // 根据字段绑定配置，将记录字段映射到对应的列
                mapRecordToColumns(record, fieldBindingConfig, fieldColMap, i);
            }
        }

        return fieldColMap;
    }

    /**
     * 将单条记录的字段映射到列索引
     * @param record 数据记录
     * @param fieldBindingConfig 字段绑定配置
     * @param fieldColMap 字段列映射（会更新此映射）
     */
    private void mapRecordToColumns(WorkCheckRecordVo record,
                                   Map<String, Integer> fieldBindingConfig,
                                   Map<Integer, String> fieldColMap, int num) {

        // 结果字段
        if (fieldBindingConfig.containsKey("result") && record.getResult() != null) {
            int colIndex = fieldBindingConfig.get("result");
            String resultColumn = fieldColMap.get(colIndex);
            resultColumn = resultColumn != null ? resultColumn + "\n" + record.getResult() : record.getResult();
            fieldColMap.put(colIndex, resultColumn);
        }

        // 月份字段
        if (fieldBindingConfig.containsKey("month_str") && record.getMonthStr() != null) {
            int colIndex = fieldBindingConfig.get("month_str");
            fieldColMap.put(colIndex, record.getMonthStr());
        }

        // 日期字段
        if (fieldBindingConfig.containsKey("day_str") && record.getDayStr() != null) {
            int colIndex = fieldBindingConfig.get("day_str");
            fieldColMap.put(colIndex, record.getDayStr());
        }

        // 操作员字段
        if (fieldBindingConfig.containsKey("check_user_name") && record.getCheckUserName() != null) {
            int colIndex = fieldBindingConfig.get("check_user_name");
            String checkUserNameColumn = fieldColMap.get(colIndex);
            if (checkUserNameColumn != null && !checkUserNameColumn.contains(record.getCheckUserName())) {
                checkUserNameColumn += "\n" + record.getCheckUserName();
            }else {
                checkUserNameColumn = record.getCheckUserName();
            }
            fieldColMap.put(colIndex, checkUserNameColumn);
        }

        // 班组长字段
        if (fieldBindingConfig.containsKey("bzz") && record.getBzz() != null) {
            int colIndex = fieldBindingConfig.get("bzz");
            fieldColMap.put(colIndex, record.getBzz());
        }

        // 检验员字段
        if (fieldBindingConfig.containsKey("jyy") && record.getTakeUserName() != null) {
            int colIndex = fieldBindingConfig.get("jyy");
            fieldColMap.put(colIndex, record.getTakeUserName());
        }
    }

    /**
     * 解析现有表格数据
     */
    private JSONObject parseExistingTableData(String tableData) {
        return JSON.parseObject(tableData);
    }


    /**
     * 获取默认列宽
     */
    private int getDefaultColumnWidth(int colIndex) {
        int[] defaultWidths = {100, 460, 160, 32, 32, 32, 32, 32};
        return colIndex < defaultWidths.length ? defaultWidths[colIndex] : 100;
    }


    /**
     * 创建单元格数据
     */
    private JsonTableExportRequest.CellData createCellData(String content, Integer width, Integer height) {
        JsonTableExportRequest.CellData cellData = new JsonTableExportRequest.CellData();
        cellData.setContent(content != null ? content : "");
        cellData.setWidth(width);
        cellData.setHeight(height);
        cellData.setHasMath(false);
        return cellData;
    }

    /**
     * 创建包含公式信息的单元格数据
     */
    @SuppressWarnings("unchecked")
    private JsonTableExportRequest.CellData createCellDataWithFormula(
            String content, Integer width, Integer height, JSONObject cellMap) {

        JsonTableExportRequest.CellData cellData =
            new JsonTableExportRequest.CellData();
        cellData.setContent(content != null ? content : "");
        cellData.setWidth(width);
        cellData.setHeight(height);

        // 处理公式相关属性
        Boolean hasMath = getBoolean(cellMap, "hasMath");
        cellData.setHasMath(hasMath != null ? hasMath : false);

        // 设置MathML（纯公式内容）
        String mathML = getString(cellMap, "mathML");
        if (mathML != null && !mathML.trim().isEmpty()) {
            cellData.setMathML(mathML);
        }

        // 设置混合内容标识
        Boolean hasMultipleContent = getBoolean(cellMap, "hasMultipleContent");
        cellData.setHasMultipleContent(hasMultipleContent != null ? hasMultipleContent : false);

        // 设置公式占位符映射
        Object mathMLMapObj = cellMap.get("mathMLMap");
        if (mathMLMapObj instanceof Map) {
            Map<String, String> mathMLMap = (Map<String, String>) mathMLMapObj;
            if (!mathMLMap.isEmpty()) {
                cellData.setMathMLMap(mathMLMap);
            }
        }

        JSONObject nestedTable = cellMap.getJSONObject("nestedTable");

        if (nestedTable != null) {
            cellData.setNestedTable(nestedTable.toJavaObject(JsonTableExportRequest.NestedTableConfig.class));
        }

        return cellData;
    }

    /**
     * 安全获取字符串值
     */
    private String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    /**
     * 安全获取布尔值
     */
    private Boolean getBoolean(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return null;
    }

    @Override
    public byte[] exportWordByDesignIdList(List<Long> designWordIdList) throws Exception {

        return generateWordByDesignIdList(designWordIdList);
    }

    @Override
    public byte[] generateWordByDesignIdList(List<Long> designWordIdList) throws Exception {
        if (designWordIdList == null || designWordIdList.isEmpty()) {
            throw new RuntimeException("设计表ID列表不能为空");
        }

        // 查询所有设计表数据
        List<DesignWord> designWords = new ArrayList<>();
        for (Long designWordId : designWordIdList) {
            DesignWord designWord = designWordService.selectDesignWordById(designWordId);
            if (designWord != null) {
                designWords.add(designWord);
            } else {
                logger.warn("设计表数据不存在，ID: {}", designWordId);
            }
        }

        if (designWords.isEmpty()) {
            throw new RuntimeException("没有找到有效的设计表数据");
        }

        // 合并所有设计表数据到一个Word文档中
        return mergeDesignWordsToSingleWord(designWords);
    }


    @Override
    public byte[] generateAllDesignWords(String carId) throws Exception {
        // 查询所有设计表数据
        DesignWord queryParam = new DesignWord();
        queryParam.setStatus(1); // 只查询启用状态的设计表
        queryParam.setCarId(carId);
        List<DesignWord> designWords = designWordService.selectDesignWordList(queryParam);

        if (designWords.isEmpty()) {
            logger.warn("没有找到任何设计表数据");
            // 返回一个空文档
            return createEmptyWordDocument();
        }

        // 合并所有设计表数据到一个Word文档中
        return mergeDesignWordsToSingleWord(designWords);
    }

    /**
     * 合并多个设计表数据到一个Word文档中
     *
     * @param designWords 设计表数据列表
     * @return 合并后的Word文档字节数组
     * @throws Exception 合并异常
     */
    public byte[] mergeDesignWordsToSingleWord(List<DesignWord> designWords) throws Exception {

        try (XWPFDocument document = new XWPFDocument()) {
            wordExportService.setPageOrientation(document, "");
            wordExportService.setPageMargins(document, 50, 50, 50, 50);
            // 为每个设计表创建一个章节
            for (int i = 0; i < designWords.size(); i++) {
                DesignWord designWord = designWords.get(i);

                // 解析绑定的模板ID列表
                List<Integer> templateIds = parseTemplateIds(designWord.getBoundTemplateIds());
                if (templateIds.isEmpty()) {
                    logger.warn("设计表ID: {} 没有绑定任何模板ID", designWord.getId());
                }

                // 查询检验记录数据
                List<WorkCheckRecordVo> dataRecords = queryDataRecords(designWord.getCarId(), templateIds);

                // 解析字段绑定配置和行模板绑定关系
                //Map<String, Integer> fieldBindingConfig = parseFieldBindingConfig(designWord.getFieldBindingConfig());
                //Map<Integer, List<Integer>> rowTemplateBindings = parseRowTemplateBindings(designWord.getRowTemplateBindings());
                Map<String, Integer> fieldBindingConfig = new HashMap<>();
                Map<Integer, List<Integer>> rowTemplateBindings = new HashMap<>();

                byte[] wordBytes;

                String coverData = designWord.getCoverData();
                if (StringUtils.hasText(coverData)) {
                    SimpleWordExportRequest request = JSON.parseObject(coverData, SimpleWordExportRequest.class);
                    // 导出Word文档
                    wordBytes = simpleWordExportService.exportSimpleWord(request);
                }else {
                    // 构建JSON导出请求并生成Word文档内容
                    wordBytes = generateWordWithJsonFormat(designWord, dataRecords, fieldBindingConfig, rowTemplateBindings);
                }

                // 将单个Word文档的内容合并到主文档中
                mergeSingleWordToMainDocument(document, wordBytes);

                // 在合并内容后立即添加分页符（除了最后一个表）
                if (i < designWords.size() - 1) {
                    addPageBreakToDocument(document);
                }
            }

            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);
            return outputStream.toByteArray();

        }
    }

    // 添加分页符
    private void addPageBreakToDocument(XWPFDocument document) {
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.addBreak(BreakType.PAGE);
    }

    /**
     * 将单个Word文档内容合并到主文档中
     *
     * @param mainDocument 主文档
     * @param singleWordBytes 单个Word文档字节数组
     * @throws IOException IO异常
     */
    private void mergeSingleWordToMainDocument(XWPFDocument mainDocument, byte[] singleWordBytes) throws IOException {
        try (XWPFDocument singleDocument = new XWPFDocument(new ByteArrayInputStream(singleWordBytes))) {
            // 复制段落内容
            for (IBodyElement element : singleDocument.getBodyElements()) {
                if (element instanceof XWPFParagraph) {
                    XWPFParagraph paragraph = (XWPFParagraph) element;
                    XWPFParagraph newParagraph = mainDocument.createParagraph();
                    // 复制段落属性
                    newParagraph.getCTP().set(paragraph.getCTP());
                    // 处理段落中的图片
                    mergeParagraphImages(paragraph, newParagraph, singleDocument, mainDocument);
                } else if (element instanceof XWPFTable) {
                    XWPFTable table = (XWPFTable) element;
                    XWPFTable newTable = mainDocument.createTable();
                    newTable.getCTTbl().set(table.getCTTbl());
                }
            }
        }
    }

    /**
     * 合并段落中的图片到主文档
     *
     * @param sourceParagraph 源段落
     * @param targetParagraph 目标段落
     * @param sourceDocument 源文档
     * @param targetDocument 目标文档
     * @throws IOException IO异常
     */
    private void mergeParagraphImages(XWPFParagraph sourceParagraph, XWPFParagraph targetParagraph,
                                      XWPFDocument sourceDocument, XWPFDocument targetDocument) throws IOException {
        try {
            // 获取源段落中的所有运行
            List<XWPFRun> sourceRuns = sourceParagraph.getRuns();
            if (sourceRuns == null || sourceRuns.isEmpty()) {
                logger.debug("源段落中没有运行，跳过图片合并");
                return;
            }

            // 获取目标段落中的运行
            List<XWPFRun> targetRuns = targetParagraph.getRuns();
            if (targetRuns == null || targetRuns.isEmpty()) {
                logger.debug("目标段落中没有运行，需要重新创建运行来处理图片");
                // 重新处理段落复制和图片合并
                handleParagraphWithImages(sourceParagraph, targetParagraph, sourceDocument, targetDocument);
                return;
            }

            // 遍历源运行，查找图片
            for (int i = 0; i < sourceRuns.size(); i++) {
                XWPFRun sourceRun = sourceRuns.get(i);

                // 检查运行中是否包含图片
                List<XWPFPicture> pictures = sourceRun.getEmbeddedPictures();
                if (pictures != null && !pictures.isEmpty()) {
                    logger.debug("发现段落中包含 {} 张图片，开始合并", pictures.size());

                    // 确保目标段落有足够的运行
                    XWPFRun targetRun;
                    if (i < targetRuns.size()) {
                        targetRun = targetRuns.get(i);
                    } else {
                        // 创建新的运行
                        targetRun = targetParagraph.createRun();
                        // 复制运行的文本和格式
                        copyRunProperties(sourceRun, targetRun);
                    }

                    // 清空目标运行的图片，重新添加
                    clearRunDrawings(targetRun);

                    // 复制每张图片
                    for (XWPFPicture picture : pictures) {
                        copyPictureToTargetRun(picture, targetRun, sourceDocument, targetDocument);
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("合并段落图片时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理包含图片的段落复制
     *
     * @param sourceParagraph 源段落
     * @param targetParagraph 目标段落
     * @param sourceDocument 源文档
     * @param targetDocument 目标文档
     * @throws IOException IO异常
     */
    private void handleParagraphWithImages(XWPFParagraph sourceParagraph, XWPFParagraph targetParagraph,
                                          XWPFDocument sourceDocument, XWPFDocument targetDocument) throws IOException {
        try {
            // 清空目标段落的运行，重新构建
            /*while (!targetParagraph.getRuns().isEmpty()) {
                targetParagraph.removeRun(0);
            }*/

            // 遍历源段落的所有运行
            List<XWPFRun> sourceRuns = sourceParagraph.getRuns();
            for (XWPFRun sourceRun : sourceRuns) {
                XWPFRun targetRun = targetParagraph.createRun();

                // 复制运行的基本属性和文本
                //copyRunProperties(sourceRun, targetRun);

                // 处理图片
                List<XWPFPicture> pictures = sourceRun.getEmbeddedPictures();
                if (pictures != null && !pictures.isEmpty()) {
                    logger.debug("在新运行中复制 {} 张图片", pictures.size());
                    for (XWPFPicture picture : pictures) {
                        copyPictureToTargetRun(picture, targetRun, sourceDocument, targetDocument);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("处理包含图片的段落时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 复制运行的属性
     *
     * @param sourceRun 源运行
     * @param targetRun 目标运行
     */
    private void copyRunProperties(XWPFRun sourceRun, XWPFRun targetRun) {
        try {
            // 复制文本内容
            String text = sourceRun.getText(0);
            if (text != null && !text.isEmpty()) {
                targetRun.setText(text);
            }

            // 复制格式属性
            if (sourceRun.isBold()) {
                targetRun.setBold(true);
            }
            if (sourceRun.isItalic()) {
                targetRun.setItalic(true);
            }
            if (sourceRun.getUnderline() != UnderlinePatterns.NONE) {
                targetRun.setUnderline(sourceRun.getUnderline());
            }
            if (sourceRun.getFontSize() != -1) {
                targetRun.setFontSize(sourceRun.getFontSize());
            }
            if (sourceRun.getFontFamily() != null) {
                targetRun.setFontFamily(sourceRun.getFontFamily());
            }
            if (sourceRun.getColor() != null) {
                targetRun.setColor(sourceRun.getColor());
            }
        } catch (Exception e) {
            logger.warn("复制运行属性时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 清空运行中的绘图元素
     *
     * @param run 运行对象
     */
    private void clearRunDrawings(XWPFRun run) {
        try {
            // 清空绘图数组
            run.getCTR().getDrawingArray();
        } catch (Exception e) {
            logger.debug("清空运行绘图元素时发生错误: {}", e.getMessage());
        }
    }

    /**
     * 复制图片到目标运行
     *
     * @param sourcePicture 源图片
     * @param targetRun 目标运行
     * @param sourceDocument 源文档
     * @param targetDocument 目标文档
     * @throws IOException IO异常
     */
    private void copyPictureToTargetRun(XWPFPicture sourcePicture, XWPFRun targetRun,
                                        XWPFDocument sourceDocument, XWPFDocument targetDocument) throws IOException {
        try {
            // 获取图片数据
            XWPFPictureData sourcePictureData = sourcePicture.getPictureData();
            if (sourcePictureData == null) {
                logger.warn("源图片数据为空，跳过复制");
                return;
            }

            byte[] imageBytes = sourcePictureData.getData();
            if (imageBytes == null || imageBytes.length == 0) {
                logger.warn("图片字节数据为空，跳过复制");
                return;
            }

            // 获取图片格式
            int pictureType = sourcePictureData.getPictureType();
            String fileName = sourcePictureData.getFileName();
            if (fileName == null || fileName.isEmpty()) {
                fileName = "image_" + System.currentTimeMillis() + getImageExtension(pictureType);
            }

            // 获取图片尺寸
            int width = (int) sourcePicture.getWidth();
            int height = (int) sourcePicture.getDepth();

            // 在目标文档中添加图片
            try (ByteArrayInputStream imageStream = new ByteArrayInputStream(imageBytes)) {
                targetRun.addPicture(imageStream, pictureType, fileName,
                        Units.toEMU(width), Units.toEMU(height));
                logger.debug("成功复制图片: {}, 尺寸: {}x{}", fileName, width, height);
            }

        } catch (Exception e) {
            logger.error("复制图片到目标运行失败: {}", e.getMessage(), e);
            // 添加错误提示文本
            targetRun.setText("[图片复制失败]");
            targetRun.setColor("FF0000");
        }
    }

    /**
     * 根据图片类型获取文件扩展名
     *
     * @param pictureType 图片类型
     * @return 文件扩展名
     */
    private String getImageExtension(int pictureType) {
        PictureType type = PictureType.findByOoxmlId(pictureType);
        switch (type) {
            case JPEG:
                return ".jpg";
            case PNG:
                return ".png";
            case GIF:
                return ".gif";
            case BMP:
                return ".bmp";
            case WMF:
                return ".wmf";
            case EMF:
                return ".emf";
            default:
                return ".img";
        }
    }

    /**
     * 生成批量导出的文件名
     *
     * @param designWordIdList 设计表ID列表
     * @return 文件名
     */
    private String generateBatchFileName(List<Long> designWordIdList) {
        StringBuilder fileName = new StringBuilder();
        fileName.append("检验记录表批量导出_");

        // 添加前几个ID作为标识
        for (int i = 0; i < Math.min(3, designWordIdList.size()); i++) {
            if (i > 0) {
                fileName.append("_");
            }
            fileName.append(designWordIdList.get(i));
        }

        if (designWordIdList.size() > 3) {
            fileName.append("_等").append(designWordIdList.size()).append("个表");
        }

        fileName.append(".docx");
        return fileName.toString();
    }

    /**
     * 创建空Word文档
     *
     * @return 空Word文档字节数组
     * @throws IOException IO异常
     */
    private byte[] createEmptyWordDocument() throws IOException {
        XWPFDocument document = new XWPFDocument();

        try {
            // 添加标题
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setText("暂无检验记录表数据");
            titleRun.setBold(true);
            titleRun.setFontSize(16);

            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);
            return outputStream.toByteArray();

        } finally {
            document.close();
        }
    }
}
