package com.logictrue.word.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 检验模板表
 */
@Data
@TableName(value = "drl_work_check")
public class DrlWorkCheck {
    /**
     * 主键
     */
    @TableId(value = "check_id", type = IdType.AUTO)
    private Integer checkId;

    /**
     * 标题id
     */
    @TableField(value = "parent_id")
    private Integer parentId;

    /**
     * 检验项
     */
    @TableField(value = "check_name")
    private String checkName;

    /**
     * 内容
     */
    @TableField(value = "check_content")
    private String checkContent;

    /**
     * 车型
     */
    @TableField(value = "car_type")
    private Integer carType;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 层级(1:标题.2:检验项)如果是1则结果模板,必填,需要替换值必须为null
     */
    @TableField(value = "check_level")
    private Integer checkLevel;

    /**
     * 班组
     */
    @TableField(value = "team")
    private Integer team;

    /**
     * 检验工序id
     */
    @TableField(value = "check_type_id")
    private Integer checkTypeId;

    /**
     * 结果模板
     */
    @TableField(value = "result_template")
    private String resultTemplate;

    /**
     * 必填
     */
    @TableField(value = "is_required")
    private Integer isRequired;

    /**
     * 需要替换值个数
     */
    @TableField(value = "replace_num")
    private Integer replaceNum;

    /**
     * 版本id
     */
    @TableField(value = "version_id")
    private Integer versionId;

    /**
     * 填写值的范围
     */
    @TableField(value = "replace_rules")
    private String replaceRules;

    /**
     * 导入编码
     */
    @TableField(value = "check_code")
    private String checkCode;
}
