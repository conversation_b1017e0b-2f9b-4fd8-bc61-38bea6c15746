package com.logictrue.word.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.logictrue.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 检验记录表设计对象 design_word
 */
@Data
@TableName("design_word")
public class DesignWord {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 车辆ID
     */
    @TableField("car_id")
    private String carId;

    /**
     * 表格标题
     */
    @TableField("title")
    private String title;

    /**
     * 表格配置信息(JSON格式)
     */
    @TableField("table_config")
    private String tableConfig;

    /**
     * 表格数据(JSON格式)
     */
    @TableField("table_data")
    private String tableData;

    @TableField("cover_data")
    private String coverData;

    /**
     * 状态(0:禁用 1:启用)
     */
    @TableField("status")
    private Integer status;

    /**
     * 页面名称
     */
    @TableField("page_name")
    private String pageName;

    /**
     * 页面排序
     */
    @TableField("page_order")
    private Integer pageOrder;

    /**
     * 总页数
     */
    @TableField("total_pages")
    private Integer totalPages;

    /**
     * 是否为当前活动页面
     */
    @TableField("is_active")
    private Integer isActive;

    /**
     * 字段绑定配置(JSON格式)
     */
    @TableField("field_binding_config")
    private String fieldBindingConfig;

    /**
     * 行与模板ID的绑定关系(JSON格式)
     */
    @TableField("row_template_bindings")
    private String rowTemplateBindings;

    /**
     * 绑定的模板ID列表(逗号分隔)
     */
    @TableField("bound_template_ids")
    private String boundTemplateIds;

    @TableField("type")
    private int type;

    @TableField("has_nested_table")
    private int hasNestedTable;

}
