package com.logictrue.word.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 检验卡片记录表实体类
 */
@ApiModel(description = "检验卡片记录表")
@TableName("drl_data_records")
public class DrlDataRecords {

    /**
     * 记录id
     */
    @ApiModelProperty(value = "记录id")
    @TableId(value = "record_id", type = IdType.AUTO)
    private Integer recordId;

    /**
     * 车号
     */
    @ApiModelProperty(value = "车号")
    @TableField("car_id")
    private String carId;

    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板id")
    @TableField("template_id")
    private Integer templateId;

    /**
     * 检查项目及技术条件
     */
    @ApiModelProperty(value = "检查项目及技术条件")
    @TableField("check_name")
    private String checkName;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    @TableField("check_content")
    private String checkContent;

    /**
     * 实际检查结果
     */
    @ApiModelProperty(value = "实际检查结果")
    @TableField("result")
    private String result;

    /**
     * 月
     */
    @ApiModelProperty(value = "月")
    @TableField("month_str")
    private String monthStr;

    /**
     * 日
     */
    @ApiModelProperty(value = "日")
    @TableField("day_str")
    private String dayStr;

    /**
     * 操作员
     */
    @ApiModelProperty(value = "操作员")
    @TableField("check_user_name")
    private String checkUserName;

    /**
     * 班组长
     */
    @ApiModelProperty(value = "班组长")
    @TableField("bzz")
    private String bzz;

    /**
     * 检验员
     */
    @ApiModelProperty(value = "检验员")
    @TableField("jyy")
    private String jyy;

    public Integer getRecordId() {
        return recordId;
    }

    public void setRecordId(Integer recordId) {
        this.recordId = recordId;
    }

    public String getCarId() {
        return carId;
    }

    public void setCarId(String carId) {
        this.carId = carId;
    }

    public Integer getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Integer templateId) {
        this.templateId = templateId;
    }

    public String getCheckName() {
        return checkName;
    }

    public void setCheckName(String checkName) {
        this.checkName = checkName;
    }

    public String getCheckContent() {
        return checkContent;
    }

    public void setCheckContent(String checkContent) {
        this.checkContent = checkContent;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getMonthStr() {
        return monthStr;
    }

    public void setMonthStr(String monthStr) {
        this.monthStr = monthStr;
    }

    public String getDayStr() {
        return dayStr;
    }

    public void setDayStr(String dayStr) {
        this.dayStr = dayStr;
    }

    public String getCheckUserName() {
        return checkUserName;
    }

    public void setCheckUserName(String checkUserName) {
        this.checkUserName = checkUserName;
    }

    public String getBzz() {
        return bzz;
    }

    public void setBzz(String bzz) {
        this.bzz = bzz;
    }

    public String getJyy() {
        return jyy;
    }

    public void setJyy(String jyy) {
        this.jyy = jyy;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("recordId", getRecordId())
                .append("carId", getCarId())
                .append("templateId", getTemplateId())
                .append("checkName", getCheckName())
                .append("checkContent", getCheckContent())
                .append("result", getResult())
                .append("monthStr", getMonthStr())
                .append("dayStr", getDayStr())
                .append("checkUserName", getCheckUserName())
                .append("bzz", getBzz())
                .append("jyy", getJyy())
                .toString();
    }
}
