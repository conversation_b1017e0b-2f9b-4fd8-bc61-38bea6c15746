package com.logictrue.word.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logictrue.word.entity.DrlWorkCheckRecords;
import com.logictrue.word.entity.vo.WorkCheckRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DrlWorkCheckRecordsMapper extends BaseMapper<DrlWorkCheckRecords> {

    List<WorkCheckRecordVo> getByTemplateIds(@Param("templateIds") List<Integer> templateIds);

    List<WorkCheckRecordVo> getByCarIdAndTemplateIds(@Param("carId") String carId, @Param("templateIds") List<Integer> templateIds);
}
