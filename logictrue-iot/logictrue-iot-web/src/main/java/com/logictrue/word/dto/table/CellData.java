package com.logictrue.word.dto.table;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 单元格数据
 */
@Data
public class CellData {

    /**
     * 单元格内容
     */
    private String content;

    /**
     * 原始内容
     */
    private String originContent;

    /**
     * 单元格宽度
     */
    private Integer width;

    /**
     * 单元格高度
     */
    private Double height;

    /**
     * 是否包含数学公式
     */
    private Boolean hasMath;

    /**
     * 是否包含多重内容
     */
    private Boolean hasMultipleContent;

    /**
     * 数学公式ML映射
     */
    private Map<String, String> mathMLMap;

    /**
     * 公式列表
     */
    private List<Formula> formulas;

    private NestedTableConfig nestedTable;

}
