package com.logictrue.word.dto;

import com.logictrue.word.dto.table.CellData;
import com.logictrue.word.dto.table.TableData;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class TablePageDto {

    // 创建当前页面的表格数据对象
    public TableData tableData;

    // 创建当前页面的行数据列表
    public List<List<CellData>> rows;

    // 创建当前行的单元格数据列表
    public List<CellData> row;

    public double totalHeight; // 当前页面已累积的总高度
    public double rowHeight; // 当前行的高度

    public double totalH;
    public double rowH;

    public int lastRemainingHeight; // 上次插入后剩余的页面高度，初始为510px
    public int spaceHeight;
    public int processHeight;
    public boolean processFirst;

    public Map<Integer, List<Integer>> checkIdMap;
    public List<Integer> checkIdList;

    public int rowIndex;

}
