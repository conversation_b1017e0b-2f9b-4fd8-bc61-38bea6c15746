package com.logictrue.word.dto.table;

import lombok.Data;

import java.util.List;

/**
 * 元数据
 */
@Data
public class Metadata {

    /**
     * 标题
     */
    private String title;

    /**
     * 是否使用动态表头
     */
    private Boolean useDynamicHeader;

    /**
     * 是否有自定义宽度
     */
    private Boolean hasCustomWidth;

    /**
     * 总行数
     */
    private Integer totalRows;

    /**
     * 总列数
     */
    private Integer totalColumns;

    /**
     * 表头行数
     */
    private Integer headerRows;

    /**
     * 导出时间
     */
    private String exportTime;

    /**
     * 是否有嵌套表格
     */
    private Boolean hasNestedTables;

    /**
     * 嵌套层级
     */
    private Integer nestedLevel;

    /**
     * 最大嵌套层级
     */
    private Integer maxNestedLevel;

    /**
     * 是否有合并单元格
     */
    private Boolean hasMergedCells;

    /**
     * 是否有表头合并
     */
    private Boolean hasHeaderMerges;

    /**
     * 是否有LaTeX处理
     */
    private Boolean hasLatexProcessing;

    /**
     * 是否有垂直表头
     */
    private Boolean hasVerticalHeaders;

    /**
     * 创建时间
     */
    private String created;

    /**
     * 修改时间
     */
    private String modified;

    /**
     * 版本号
     */
    private String version;

    /**
     * 作者
     */
    private String author;

    /**
     * 描述
     */
    private String description;


}
