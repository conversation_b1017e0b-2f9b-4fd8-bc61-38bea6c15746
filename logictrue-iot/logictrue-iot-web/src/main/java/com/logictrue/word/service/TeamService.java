package com.logictrue.word.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.logictrue.word.entity.DrlTeam;
import com.logictrue.word.mapper.DrlTeamMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class TeamService extends ServiceImpl<DrlTeamMapper, DrlTeam> {


    public Map<Integer, DrlTeam> getTeamMap(){
        LambdaQueryWrapper<DrlTeam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DrlTeam::getDelFlag, false);
        List<DrlTeam> list = list(queryWrapper);
        return list.stream().collect(Collectors.toMap(DrlTeam::getId, drlTeam -> drlTeam));

    }

}
