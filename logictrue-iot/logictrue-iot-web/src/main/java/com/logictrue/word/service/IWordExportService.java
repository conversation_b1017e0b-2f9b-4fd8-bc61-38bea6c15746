package com.logictrue.word.service;

import com.logictrue.word.entity.DesignWord;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Word导出服务接口
 */
public interface IWordExportService {

    /**
     * 根据设计表ID生成Word文档字节数组
     *
     * @param designWordId 设计表ID
     * @return Word文档字节数组
     * @throws Exception 生成异常
     */
    byte[] generateWordByDesignId(Long designWordId) throws Exception;

    /**
     * 根据设计表ID列表导出Word文档
     *
     * @param designWordIdList 设计表ID列表
     * @throws Exception 导出异常
     */
    byte[] exportWordByDesignIdList(List<Long> designWordIdList) throws Exception;

    /**
     * 根据设计表ID列表生成Word文档字节数组
     *
     * @param designWordIdList 设计表ID列表
     * @return Word文档字节数组
     * @throws Exception 生成异常
     */
    byte[] generateWordByDesignIdList(List<Long> designWordIdList) throws Exception;

    /**
     * 生成所有设计表Word文档字节数组
     *
     * @return Word文档字节数组
     * @throws Exception 生成异常
     */
    byte[] generateAllDesignWords(String carId) throws Exception;

    /**
     * 合并多个设计表数据到一个Word文档中
     *
     * @param designWords 设计表数据列表
     * @return 合并后的Word文档字节数组
     * @throws Exception 合并异常
     */
    byte[] mergeDesignWordsToSingleWord(List<DesignWord> designWords) throws Exception;
}
