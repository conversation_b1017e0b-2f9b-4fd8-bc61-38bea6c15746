package com.logictrue.word.service.impl;

import com.logictrue.word.entity.vo.WorkCheckVo;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * WorkCheckServiceImpl 行高计算逻辑测试
 */
public class WorkCheckServiceImplTest {

    private WorkCheckServiceImpl workCheckService = new WorkCheckServiceImpl();

    @Test
    public void testCalculateActualLineCount_EmptyText() {
        // 测试空文本
        int lineCount = workCheckService.calculateActualLineCount("", 50);
        assertEquals(1, lineCount, "空文本应该占一行");
    }

    @Test
    public void testCalculateActualLineCount_ShortText() {
        // 测试短文本（不超过单行容量）
        int lineCount = workCheckService.calculateActualLineCount("短文本", 50);
        assertEquals(1, lineCount, "短文本应该占一行");
    }

    @Test
    public void testCalculateActualLineCount_LongTextNoNewlines() {
        // 测试长文本（超过单行容量，无换行符）
        String longText = "这是一个很长的文本内容，它会超过单行的容量限制，需要自动换行显示。";
        int lineCount = workCheckService.calculateActualLineCount(longText, 20);
        assertTrue(lineCount > 1, "长文本应该占多行");
    }

    @Test
    public void testCalculateActualLineCount_WithNewlines() {
        // 测试带换行符的文本
        String textWithNewlines = "第一行\n第二行\n第三行";
        int lineCount = workCheckService.calculateActualLineCount(textWithNewlines, 50);
        assertEquals(3, lineCount, "三行文本应该占三行");
    }

    @Test
    public void testCalculateActualLineCount_MixedCase() {
        // 测试混合情况（换行符 + 长文本）
        String mixedText = "第一行\n这是一个很长的第二行文本内容，它会超过单行的容量限制，需要自动换行显示。\n第三行";
        int lineCount = workCheckService.calculateActualLineCount(mixedText, 20);
        assertTrue(lineCount > 3, "混合情况应该占更多行");
    }

    @Test
    public void testCalculateActualLineCount_EmptyLines() {
        // 测试包含空行的文本
        String textWithEmptyLines = "第一行\n\n第三行";
        int lineCount = workCheckService.calculateActualLineCount(textWithEmptyLines, 50);
        assertEquals(3, lineCount, "包含空行的文本应该正确计算行数");
    }

    @Test
    public void testCalculateActualLineCount_TrailingNewline() {
        // 测试末尾有换行符的文本
        String textWithTrailingNewline = "第一行\n第二行\n";
        int lineCount = workCheckService.calculateActualLineCount(textWithTrailingNewline, 50);
        assertEquals(3, lineCount, "末尾换行符应该被计算为一行");
    }

    @Test
    public void testCalculateActualLineCount_OnlyNewlines() {
        // 测试只有换行符的文本
        String onlyNewlines = "\n\n\n";
        int lineCount = workCheckService.calculateActualLineCount(onlyNewlines, 50);
        assertEquals(4, lineCount, "只有换行符的文本应该正确计算行数");
    }

    @Test
    public void testCalculateActualLineCount_BufferCharacters() {
        // 测试字符缓冲的影响
        String text = "123456789012345678"; // 18个字符
        int lineCount20 = workCheckService.calculateActualLineCount(text, 20); // 单行容量20
        int lineCount16 = workCheckService.calculateActualLineCount(text, 16); // 单行容量16

        // 18 + 2 = 20个字符，在容量20时应该占1行，在容量16时应该占2行
        assertEquals(1, lineCount20, "20字符容量下20字符应该占1行");
        assertEquals(2, lineCount16, "16字符容量下20字符应该占2行");
    }

    @Test
    public void testCalculateCheckItemHeight() {
        // 测试calculateCheckItemHeight方法
        WorkCheckVo checkItem = new WorkCheckVo();
        checkItem.setCheckName("这是一个测试检查项目");
        checkItem.setRowHeight(null);

        String checkTypeName = "检查类型名称";
        double lineHeight = 14.5;
        double oneLineSize = 50;
        double checkProcessLineSize = 20;

        // 使用反射调用私有方法
        try {
            java.lang.reflect.Method method = WorkCheckServiceImpl.class.getDeclaredMethod(
                "calculateCheckItemHeight",
                WorkCheckVo.class,
                String.class,
                double.class,
                double.class,
                double.class
            );
            method.setAccessible(true);

            double height = (double) method.invoke(workCheckService, checkItem, checkTypeName, lineHeight, oneLineSize, checkProcessLineSize);

            // 验证返回的高度大于0
            assertTrue(height > 0, "计算的高度应该大于0");

            // 验证高度是基于行高计算的
            assertEquals(lineHeight, height, 0.01, "短文本的高度应该等于行高");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testCalculateCheckItemHeightWithCustomRowHeight() {
        // 测试带有自定义行高的检查项目
        WorkCheckVo checkItem = new WorkCheckVo();
        checkItem.setCheckName("短文本");
        checkItem.setRowHeight(30); // 自定义行高

        String checkTypeName = "检查类型";
        double lineHeight = 14.5;
        double oneLineSize = 50;
        double checkProcessLineSize = 20;

        try {
            java.lang.reflect.Method method = WorkCheckServiceImpl.class.getDeclaredMethod(
                "calculateCheckItemHeight",
                WorkCheckVo.class,
                String.class,
                double.class,
                double.class,
                double.class
            );
            method.setAccessible(true);

            double height = (double) method.invoke(workCheckService, checkItem, checkTypeName, lineHeight, oneLineSize, checkProcessLineSize);

            // 验证使用自定义行高
            assertEquals(30.0, height, 0.01, "应该使用自定义行高");

        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    @Test
    public void testCalculateCheckItemHeightWithLongText() {
        // 测试长文本的检查项目
        WorkCheckVo checkItem = new WorkCheckVo();
        checkItem.setCheckName("这是一个很长的检查项目文本内容，它会超过单行的容量限制，需要自动换行显示。这个测试用例验证了长文本的高度计算逻辑。");
        checkItem.setRowHeight(null);

        String checkTypeName = "检查类型名称";
        double lineHeight = 14.5;
        double oneLineSize = 20; // 较小的单行容量
        double checkProcessLineSize = 20;

        try {
            java.lang.reflect.Method method = WorkCheckServiceImpl.class.getDeclaredMethod(
                "calculateCheckItemHeight",
                WorkCheckVo.class,
                String.class,
                double.class,
                double.class,
                double.class
            );
            method.setAccessible(true);

            double height = (double) method.invoke(workCheckService, checkItem, checkTypeName, lineHeight, oneLineSize, checkProcessLineSize);

            // 验证长文本的高度大于单行高度
            assertTrue(height > lineHeight, "长文本的高度应该大于单行高度");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试新的isLastItemOnPage判断逻辑
     * 验证只有当外层循环也是最后一个分组且内层循环也是最后一个项目时，才设置为true
     */
    @Test
    public void testIsLastItemOnPageLogic() {
        // 创建测试数据：多个分组，每个分组有多个项目
        Map<Integer, List<WorkCheckVo>> groupedByType = new LinkedHashMap<>();

        // 第一个分组（非最后一个分组）
        List<WorkCheckVo> group1 = new ArrayList<>();
        WorkCheckVo item1_1 = new WorkCheckVo();
        item1_1.setCheckId(1);
        item1_1.setCheckName("项目1-1");
        item1_1.setCheckTypeId(1);
        item1_1.setCheckTypeName("检查类型1");
        group1.add(item1_1);

        WorkCheckVo item1_2 = new WorkCheckVo();
        item1_2.setCheckId(2);
        item1_2.setCheckName("项目1-2");
        item1_2.setCheckTypeId(1);
        item1_2.setCheckTypeName("检查类型1");
        group1.add(item1_2);

        // 第二个分组（最后一个分组）
        List<WorkCheckVo> group2 = new ArrayList<>();
        WorkCheckVo item2_1 = new WorkCheckVo();
        item2_1.setCheckId(3);
        item2_1.setCheckName("项目2-1");
        item2_1.setCheckTypeId(2);
        item2_1.setCheckTypeName("检查类型2");
        group2.add(item2_1);

        WorkCheckVo item2_2 = new WorkCheckVo();
        item2_2.setCheckId(4);
        item2_2.setCheckName("项目2-2");
        item2_2.setCheckTypeId(2);
        item2_2.setCheckTypeName("检查类型2");
        group2.add(item2_2);

        groupedByType.put(1, group1);
        groupedByType.put(2, group2);

        // 验证分组键的获取和索引判断
        List<Integer> groupKeys = new ArrayList<>(groupedByType.keySet());
        assertEquals(2, groupKeys.size());
        assertEquals(Integer.valueOf(1), groupKeys.get(0));
        assertEquals(Integer.valueOf(2), groupKeys.get(1));

        // 验证第一个分组不是最后一个分组
        int firstGroupIndex = groupKeys.indexOf(1);
        boolean isFirstGroupLast = (firstGroupIndex == groupKeys.size() - 1);
        assertFalse(isFirstGroupLast, "第一个分组不应该被识别为最后一个分组");

        // 验证第二个分组是最后一个分组
        int secondGroupIndex = groupKeys.indexOf(2);
        boolean isSecondGroupLast = (secondGroupIndex == groupKeys.size() - 1);
        assertTrue(isSecondGroupLast, "第二个分组应该被识别为最后一个分组");

        // 验证内层循环的最后一个项目判断
        boolean isLastItemInGroup1 = !(1 < group1.size()); // i=1, hasNext=false
        assertTrue(isLastItemInGroup1, "group1的第二个项目应该是最后一个项目");

        boolean isLastItemInGroup2 = !(1 < group2.size()); // i=1, hasNext=false
        assertTrue(isLastItemInGroup2, "group2的第二个项目应该是最后一个项目");

        // 验证新的isLastItemOnPage逻辑
        // 第一个分组的最后一个项目不应该被标记为页面最后一个项目
        boolean isLastItemOnPageForGroup1 = isFirstGroupLast && isLastItemInGroup1;
        assertFalse(isLastItemOnPageForGroup1, "第一个分组的最后一个项目不应该被标记为页面最后一个项目");

        // 第二个分组的最后一个项目应该被标记为页面最后一个项目
        boolean isLastItemOnPageForGroup2 = isSecondGroupLast && isLastItemInGroup2;
        assertTrue(isLastItemOnPageForGroup2, "第二个分组的最后一个项目应该被标记为页面最后一个项目");
    }

    /**
     * 测试单个分组的情况
     */
    @Test
    public void testSingleGroupScenario() {
        // 创建测试数据：只有一个分组
        Map<Integer, List<WorkCheckVo>> groupedByType = new LinkedHashMap<>();

        List<WorkCheckVo> group1 = new ArrayList<>();
        WorkCheckVo item1_1 = new WorkCheckVo();
        item1_1.setCheckId(1);
        item1_1.setCheckName("项目1-1");
        item1_1.setCheckTypeId(1);
        item1_1.setCheckTypeName("检查类型1");
        group1.add(item1_1);

        WorkCheckVo item1_2 = new WorkCheckVo();
        item1_2.setCheckId(2);
        item1_2.setCheckName("项目1-2");
        item1_2.setCheckTypeId(1);
        item1_2.setCheckTypeName("检查类型1");
        group1.add(item1_2);

        groupedByType.put(1, group1);

        // 验证单个分组的情况
        List<Integer> groupKeys = new ArrayList<>(groupedByType.keySet());
        assertEquals(1, groupKeys.size());

        int groupIndex = groupKeys.indexOf(1);
        boolean isLastGroup = (groupIndex == groupKeys.size() - 1);
        assertTrue(isLastGroup, "单个分组的情况下，该分组应该被识别为最后一个分组");

        // 验证最后一个项目的判断
        boolean isLastItemInGroup = !(1 < group1.size()); // i=1, hasNext=false
        assertTrue(isLastItemInGroup, "最后一个项目应该被正确识别");

        // 验证isLastItemOnPage逻辑
        boolean isLastItemOnPage = isLastGroup && isLastItemInGroup;
        assertTrue(isLastItemOnPage, "单个分组中最后一个项目应该被标记为页面最后一个项目");
    }
}
