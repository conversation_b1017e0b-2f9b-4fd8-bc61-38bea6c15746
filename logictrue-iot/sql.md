```sql
alter table drl_work_check
    add cover_data longtext null comment '封面json数据';

alter table drl_work_check
    add coordinate longtext null comment '表格json数据';

alter table drl_work_check
    add valueMap longtext null comment '表格实际数据';

alter table drl_work_check
    add add_new_page int(2) null comment '标记当前记录为页尾 1为标记';

alter table drl_work_check
    add row_height int(11) null comment '行高';

alter table design_word
    add has_nested_table int(2) null comment '是否有嵌套表格 1为有';


```
