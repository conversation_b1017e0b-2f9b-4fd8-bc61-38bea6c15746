import request from '@/utils/request'

// 查询检验记录表设计列表
export function listDesignWord(query) {
  return request({
    url: '/word/designWord/list',
    method: 'get',
    params: query
  })
}

// 分页查询检验记录表设计列表
export function pageDesignWord(params) {
  return request({
    url: '/word/designWord/page',
    method: 'get',
    params
  })
}

// 查询检验记录表设计详细
export function getDesignWord(id) {
  return request({
    url: '/word/designWord/' + id,
    method: 'get'
  })
}

// 根据车辆ID查询检验记录表设计
export function getDesignWordByCarId(carId) {
  return request({
    url: '/word/designWord/byCarId/' + carId,
    method: 'get'
  })
}

// 根据车辆ID获取所有页面
export function getDesignWordPagesByCarId(carId) {
  return request({
    url: '/word/designWord/pages/' + carId,
    method: 'get'
  })
}

// 根据车辆ID和页面顺序获取特定页面
export function getDesignWordByCarIdAndPage(carId, pageId) {
  return request({
    url: '/word/designWord/page/' + carId + '/' + pageId,
    method: 'get'
  })
}

// 根据车辆ID获取当前活动页面
export function getActiveDesignWordByCarId(carId) {
  return request({
    url: '/word/designWord/activePage/' + carId,
    method: 'get'
  })
}

// 设置活动页面
export function setActivePage(carId, pageId) {
  return request({
    url: '/word/designWord/setActivePage',
    method: 'post',
    params: { carId, pageId }
  })
}

// 更新总页数
export function updateTotalPages(carId, totalPages) {
  return request({
    url: '/word/designWord/updateTotalPages',
    method: 'post',
    params: { carId, totalPages }
  })
}

// 新增检验记录表设计
export function addDesignWord(data) {
  return request({
    url: '/word/designWord',
    method: 'post',
    data: data
  })
}

// 修改检验记录表设计
export function updateDesignWord(data) {
  return request({
    url: '/word/designWord',
    method: 'put',
    data: data
  })
}

// 保存或更新检验记录表设计
export function saveOrUpdateDesignWord(data) {
  return request({
    url: '/word/designWord/saveOrUpdate',
    method: 'post',
    data: data
  })
}

// 删除检验记录表设计
export function delDesignWord(ids) {
  return request({
    url: '/word/designWord/' + ids,
    method: 'delete'
  })
}

// 批量更新页面顺序
export function batchUpdatePageOrder(pages) {
  return request({
    url: '/word/designWord/batchUpdatePageOrder',
    method: 'post',
    data: pages
  })
}

// 交换两个页面的顺序
export function swapPageOrder(page1, page2) {
  return request({
    url: '/word/designWord/swapPageOrder',
    method: 'post',
    data: [page1, page2]
  })
}
