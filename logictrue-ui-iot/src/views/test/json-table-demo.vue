<template>
  <div class="json-table-demo">
    <!-- 控制面板 -->
    <div class="control-panel">
      <!-- JSON数据输入区域 -->
      <div class="json-input-section">
        <h3>JSON数据输入</h3>
        <div class="json-controls">
          <!-- 上半部分：左右布局 -->
          <div class="top-section">
            <!-- 左侧：预设选择器 -->
            <div class="preset-selector">
              <div class="selector-group">
                <label for="preset-select">选择预设数据:</label>
                <select
                    id="preset-select"
                    v-model="selectedPreset"
                    @change="onPresetChange"
                    class="preset-select"
                >
                  <option value="">-- 请选择预设数据 --</option>
                  <option
                      v-for="option in presetOptions"
                      :key="option.value"
                      :value="option.value"
                      :title="option.description"
                  >
                    {{ option.label }}
                  </option>
                </select>
                <button @click="loadSelectedPreset" class="load-btn" :disabled="!selectedPreset">
                  加载数据
                </button>
                <button @click="clearJsonInput" class="clear-btn">
                  清空
                </button>
              </div>
              <div class="preset-description" v-if="selectedPresetInfo">
                <small>{{ selectedPresetInfo.description }}</small>
              </div>
              <div class="action-buttons">
                <button @click="insertJsonData" class="action-btn insert" :disabled="!jsonInput.trim()">
                  插入数据
                </button>
                <button @click="exportJsonData" class="action-btn export">
                  导出JSON
                </button>
                <button @click="validateJson" class="action-btn validate">
                  验证格式
                </button>
                <button @click="exportToWord" class="action-btn word">
                  导出Word
                </button>
                <button @click="showAddPresetDialog" class="action-btn add-preset">
                  添加预设
                </button>
                <button @click="loadNestedTableData" class="action-btn nested-table">
                  加载嵌套表格示例
                </button>
              </div>
            </div>

            <!-- 右侧：选项和结果 -->
            <div class="right-section">
              <!-- 插入选项 -->
              <div class="json-options">
                <b style="font-size: 14px">插入选项</b>
                <div class="options-row">
                  <label class="option-item">
                    <input type="checkbox" v-model="insertOptions.clearExisting">
                    清空现有数据
                  </label>
                  <label class="option-item">
                    <input type="checkbox" v-model="insertOptions.validateData">
                    验证数据格式
                  </label>
                  <div class="option-item">
                    <label>开始行:</label>
                    <input
                        type="number"
                        v-model.number="insertOptions.startRow"
                        min="0"
                        class="number-input"
                    >
                  </div>
                </div>
              </div>

              <!-- 操作结果显示 -->
              <div class="result-section" v-if="operationResult">
                <b style="font-size: 14px">操作结果</b>
                <div class="result-content" :class="operationResult.success ? 'success' : 'error'">
                  <p><strong>状态:</strong> {{ operationResult.success ? '成功' : '失败' }}</p>
                  <p><strong>消息:</strong> {{ operationResult.message }}</p>
                  <p v-if="operationResult.insertedRows"><strong>插入行数:</strong> {{ operationResult.insertedRows }}
                  </p>
                  <pre v-if="operationResult.error" class="error-details">{{ operationResult.error }}</pre>
                </div>
              </div>
            </div>
          </div>

          <!-- 下半部分：JSON编辑器 -->
          <div class="json-editor">
            <h4>JSON数据编辑器</h4>
            <textarea
                v-model="jsonInput"
                placeholder="请输入JSON数据..."
                class="json-textarea"
                rows="12"
            ></textarea>
          </div>
        </div>


      </div>


    </div>

    <!-- 表格容器 -->
    <div class="table-section">
      <TableContainer
          ref="tableContainer"
          :table-width="'100%'"
          :table-height="'500px'"
          :data-rows="tableData"
          :enable-nested-tables="true"
          :nested-level="0"
          :max-nested-level="2"
          @data-inserted="handleDataInserted"
          @table-updated="handleTableUpdated"
          @nested-table-created="handleNestedTableCreated"
          @nested-table-removed="handleNestedTableRemoved"
      />
    </div>

  </div>
</template>

<script>
import TableContainer from '@/components/TableContainer.vue'
import mathFormulaUtils from '@/utils/math-formula-utils'
import {getPresetOptions, getPresetData, addPresetData} from '@/data/table-presets'

export default {
  name: 'JsonTableDemo',
  components: {
    TableContainer
  },
  data() {
    return {
      // JSON输入
      jsonInput: '',

      // 预设数据选择
      selectedPreset: '',
      presetOptions: [],

      // 插入选项
      insertOptions: {
        clearExisting: true,
        validateData: true,
        startRow: 0
      },

      // 表格数据
      tableData: [
        Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false
        }))
      ],

      // 操作结果
      operationResult: null,

      // 添加预设对话框状态
      showAddDialog: false,
      newPresetForm: {
        key: '',
        name: '',
        description: ''
      }
    }
  },
  computed: {
    // 获取选中预设的信息
    selectedPresetInfo() {
      if (!this.selectedPreset) return null
      return this.presetOptions.find(option => option.value === this.selectedPreset)
    }
  },
  mounted() {
    // 初始化预设选项
    this.initializePresetOptions()
    // 加载默认数据
    this.selectedPreset = 'basic'
    this.loadSelectedPreset()
  },
  methods: {
    /**
     * 初始化预设选项
     */
    initializePresetOptions() {
      this.presetOptions = getPresetOptions()
    },

    /**
     * 预设选择改变事件
     */
    onPresetChange() {
      // 选择改变时不自动加载，等待用户点击加载按钮
      this.operationResult = null
    },

    /**
     * 加载选中的预设数据
     */
    loadSelectedPreset() {
      if (!this.selectedPreset) {
        this.operationResult = {
          success: false,
          message: '请先选择一个预设数据'
        }
        return
      }

      const presetData = getPresetData(this.selectedPreset)
      if (presetData) {
        this.jsonInput = JSON.stringify(presetData, null, 2)
        this.operationResult = {
          success: true,
          message: `已加载预设数据: ${this.selectedPresetInfo?.label || this.selectedPreset}`
        }
      } else {
        this.operationResult = {
          success: false,
          message: '预设数据不存在'
        }
      }
    },

    /**
     * 加载预设数据（兼容旧方法）
     */
    loadPresetData(type) {
      this.selectedPreset = type
      this.loadSelectedPreset()
    },

    /**
     * 添加新的预设数据
     */
    addNewPresetData(key, name, description, data) {
      try {
        addPresetData(key, {
          name: name,
          description: description,
          data: data
        })

        // 重新初始化选项列表
        this.initializePresetOptions()

        this.operationResult = {
          success: true,
          message: `成功添加新预设数据: ${name}`
        }
      } catch (error) {
        this.operationResult = {
          success: false,
          message: `添加预设数据失败: ${error.message}`
        }
      }
    },

    /**
     * 显示添加预设对话框
     */
    showAddPresetDialog() {
      if (!this.jsonInput.trim()) {
        this.operationResult = {
          success: false,
          message: '请先输入JSON数据再添加为预设'
        }
        return
      }

      // 简单的提示方式添加预设（实际项目中可以使用更好的UI组件）
      const key = prompt('请输入预设数据的唯一标识（英文）:')
      if (!key) return

      const name = prompt('请输入预设数据的显示名称:')
      if (!name) return

      const description = prompt('请输入预设数据的描述（可选）:') || ''

      try {
        const jsonData = JSON.parse(this.jsonInput)
        this.addNewPresetData(key, name, description, jsonData)
      } catch (error) {
        this.operationResult = {
          success: false,
          message: 'JSON格式错误，无法添加预设数据'
        }
      }
    },

    /**
     * 清空JSON输入
     */
    clearJsonInput() {
      this.jsonInput = ''
      this.operationResult = null
    },

    /**
     * 验证JSON格式
     */
    validateJson() {
      try {
        if (!this.jsonInput.trim()) {
          this.operationResult = {
            success: false,
            message: 'JSON输入为空'
          }
          return
        }

        const jsonData = JSON.parse(this.jsonInput)
        const tableContainer = this.$refs.tableContainer

        if (tableContainer && typeof tableContainer.validateJSONData === 'function') {
          const isValid = tableContainer.validateJSONData(jsonData)
          this.operationResult = {
            success: isValid,
            message: isValid ? 'JSON格式验证通过' : 'JSON格式不正确'
          }
        } else {
          this.operationResult = {
            success: true,
            message: 'JSON语法正确'
          }
        }
      } catch (error) {
        this.operationResult = {
          success: false,
          message: 'JSON格式错误',
          error: error.message
        }
      }
    },

    /**
     * 插入JSON数据
     */
    insertJsonData() {
      try {
        if (!this.jsonInput.trim()) {
          this.operationResult = {
            success: false,
            message: 'JSON输入为空'
          }
          return
        }

        const jsonData = JSON.parse(this.jsonInput)
        const tableContainer = this.$refs.tableContainer

        if (!tableContainer) {
          this.operationResult = {
            success: false,
            message: '表格组件未找到'
          }
          return
        }

        // 检查是否有表头配置（支持新格式和旧格式）
        let headerConfig = null
        let headerWidthConfig = null
        let verticalHeadersConfig = null

        if (jsonData.headers && jsonData.headerMerges) {
          // 新格式：直接使用headers和headerMerges
          headerConfig = {
            headers: jsonData.headers,
            merges: jsonData.headerMerges
          }
          headerWidthConfig = jsonData.headerWidthConfig
          verticalHeadersConfig = jsonData.verticalHeadersConfig
        } else if (jsonData.headerConfig && jsonData.headerWidthConfig) {
          // 旧格式：使用headerConfig
          headerConfig = jsonData.headerConfig
          headerWidthConfig = jsonData.headerWidthConfig
          verticalHeadersConfig = jsonData.verticalHeadersConfig
        }

        if (headerConfig && headerWidthConfig) {
          // 使用方法设置动态表头配置（避免直接修改props）
          tableContainer.setDynamicHeaderConfig(
              true,
              headerConfig,
              headerWidthConfig,
              verticalHeadersConfig
          )

          console.log('应用表头配置:', {
            headerConfig: headerConfig,
            headerWidthConfig: headerWidthConfig,
            verticalHeadersConfig: verticalHeadersConfig
          })
        } else {
          // 使用默认表头
          tableContainer.setDynamicHeaderConfig(false, null, null, null)
        }

        // 准备合并单元格配置
        const mergeCells = jsonData.merges || []
        const options = {
          ...this.insertOptions,
          mergeCells
        }

        // 准备插入数据（确保使用cellRows格式，优先使用originContent）
        let insertData = {}
        if (jsonData.cellRows && Array.isArray(jsonData.cellRows)) {
          // 新格式：处理cellRows，优先使用originContent
          insertData = {
            cellRows: jsonData.cellRows.map(row =>
              row.map(cellData => {
                // 优先使用originContent，如果没有则使用content
                const content = cellData.originContent || cellData.content || ''
                return {
                  content: content,
                  originContent: cellData.originContent || content,
                  hasMath: cellData.hasMath || false,
                  mathML: cellData.mathML || null,
                  hasMultipleContent: cellData.hasMultipleContent || false,
                  mathMLMap: cellData.mathMLMap || null,
                  width: cellData.width,
                  height: cellData.height,
                  nestedTable: cellData.nestedTable || null // 支持嵌套表格数据
                }
              })
            )
          }
        } else if (jsonData.rows && Array.isArray(jsonData.rows)) {
          // 旧格式：将rows转换为cellRows格式
          insertData = {
            cellRows: jsonData.rows.map(row =>
              row.map(cellContent => ({
                content: cellContent || '',
                originContent: cellContent || '',
                hasMath: false
              }))
            )
          }
        } else {
          throw new Error('未找到有效的数据行格式（cellRows或rows）')
        }

        console.log('准备插入的数据:', insertData)
        console.log('插入选项:', options)

        const result = tableContainer.insertDataFromJSON(insertData, options)
        this.operationResult = result

        // 更新本地表格数据引用
        if (result.success) {
          this.tableData = tableContainer.dataRows
        }

      } catch (error) {
        this.operationResult = {
          success: false,
          message: '数据插入失败',
          error: error.message
        }
      }
    },

    /**
     * 导出JSON数据
     * 使用与导出Word相同的数据格式
     */
    async exportJsonData() {
      const tableContainer = this.$refs.tableContainer
      if (!tableContainer) {
        this.operationResult = {
          success: false,
          message: '表格组件未找到'
        }
        return
      }

      try {
        // 使用与导出Word相同的数据准备方法
        const exportData = await this.prepareExportDataWithMerges()

        // 格式化JSON输出
        this.jsonInput = JSON.stringify(exportData, null, 2)

        // 检查导出数据中是否包含嵌套表格
        let nestedTableCount = 0
        if (exportData.cellRows) {
          exportData.cellRows.forEach(row => {
            row.forEach(cell => {
              if (cell.nestedTable && cell.nestedTable.enabled) {
                nestedTableCount++
              }
            })
          })
        }

        const message = nestedTableCount > 0
          ? `成功导出 ${exportData.cellRows ? exportData.cellRows.length : 0} 行数据，包含 ${nestedTableCount} 个嵌套表格`
          : `成功导出 ${exportData.cellRows ? exportData.cellRows.length : 0} 行数据（含实际行高度和列宽度）`

        this.operationResult = {
          success: true,
          message: message
        }

        console.log('导出的JSON数据（与Word格式一致）:', exportData)
        console.log('导出数据中的嵌套表格数量:', nestedTableCount)

        // 输出行高度信息用于调试
        if (exportData.cellRows && exportData.cellRows.length > 0) {
          console.log('各行高度信息:')
          exportData.cellRows.forEach((row, index) => {
            if (row.length > 0) {
              console.log(`  第${index}行高度: ${row[0].height}px`)
            }
          })
        }
      } catch (error) {
        console.error('JSON数据导出失败:', error)
        this.operationResult = {
          success: false,
          message: '数据导出失败',
          error: error.message
        }
      }
    },

    /**
     * 导出Word文档
     */
    async exportToWord() {

      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '正在生成Word文档，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        console.log('开始导出Word文档...')

        // 准备导出数据（包含合并单元格信息）
        const exportData = await this.prepareExportDataWithMerges()
        console.log('导出数据:', exportData)

        // 动态导入API
        const {exportTableToWord} = await import('@/api/word/export')

        // 调用导出API
        const response = await exportTableToWord(exportData)

        console.log('API响应:', response)

        // 检查响应
        if (!response || !response.data) {
          throw new Error('服务器返回数据为空')
        }

        if (response.status !== 200) {
          throw new Error(`服务器响应错误: ${response.status}`)
        }

        const dataSize = response.data.size || response.data.byteLength || response.data.length
        if (!dataSize || dataSize === 0) {
          throw new Error('返回的文件数据为空')
        }

        // 处理文件下载
        this.downloadWordFile(response, '检验记录表（含合并单元格）')

        loading.close()
        this.operationResult = {
          success: true,
          message: 'Word文档导出成功！'
        }

      } catch (error) {
        loading.close()
        console.error('导出Word文档失败:', error)

        let errorMessage = '导出失败'
        if (error.response) {
          if (error.response.status === 404) {
            errorMessage = '导出服务不可用，请检查后端服务'
          } else if (error.response.status === 500) {
            errorMessage = '服务器内部错误，请稍后重试'
          } else {
            errorMessage = `服务器错误: ${error.response.status}`
          }
        } else if (error.message) {
          errorMessage = `导出失败: ${error.message}`
        }

        this.operationResult = {
          success: false,
          message: errorMessage,
          error: error.message
        }
      }
    },

    /**
     * 准备包含合并单元格信息的导出数据
     */
    async prepareExportDataWithMerges() {
      const tableContainer = this.$refs.tableContainer
      if (!tableContainer) {
        throw new Error('表格组件未找到')
      }

      // 获取完整的表格数据（包含合并信息、表头配置和嵌套表格）
      const tableData = tableContainer.getDataAsJSON({
        includeEmpty: false,
        includeMergeInfo: true,
        includeNestedTables: true
      })

      console.log("表格组件导出数据:", tableData)

      // 使用TableContainer中的当前表头配置
      const headers = (tableData.headerConfig && tableData.headerConfig.headers) || [
        // 默认表头（如果没有动态配置）
        ['检查项目', '技术要求', '检查结果', '完工', '', '检查员', '组长', '检验员'],
        ['', '', '', '月', '日', '', '', '']
      ]

      // 使用TableContainer中的当前表头合并配置
      const headerMerges = (tableData.headerConfig && tableData.headerConfig.merges) || [
        // 默认表头合并配置（如果没有动态配置）
        {startRow: 0, startCol: 0, endRow: 1, endCol: 0, content: '检查项目'},
        {startRow: 0, startCol: 1, endRow: 1, endCol: 1, content: '技术要求'},
        {startRow: 0, startCol: 2, endRow: 1, endCol: 2, content: '检查结果'},
        {startRow: 0, startCol: 3, endRow: 0, endCol: 4, content: '完工'},
        {startRow: 0, startCol: 5, endRow: 1, endCol: 5, content: '检查员'},
        {startRow: 0, startCol: 6, endRow: 1, endCol: 6, content: '组长'},
        {startRow: 0, startCol: 7, endRow: 1, endCol: 7, content: '检验员'}
      ]

      // 使用TableContainer中的当前表头宽度配置
      const headerWidthConfig = tableData.headerWidthConfig || {
        columnWidths: [150, 200, 150, 50, 50, 80, 80, 80],
        headerHeights: [50, 50],
        verticalHeaders: [false, false, false, false, false, true, true, true]
      }

      // 获取表头纵向文字配置
      const verticalHeadersConfig = tableContainer.verticalHeadersConfig ||
          [false, false, false, false, false, true, true, true]

      console.log('使用的表头配置:', {
        headers,
        headerMerges,
        headerWidthConfig,
        verticalHeadersConfig,
        useDynamicHeader: tableData.metadata?.useDynamicHeader
      })

      // 处理数据行，转换LaTeX公式为MathML（传递正确的宽度配置）
      const cellRows = await this.processTableDataWithLatex(tableData.cellRows, headerWidthConfig)

      // 构建导出数据结构
      const exportData = {
        title: tableData.metadata?.title || '检验记录表',
        headers: headers,
        cellRows: cellRows,              // 使用处理后的复杂格式
        merges: tableData.merges || [], // 数据行的合并
        headerMerges: headerMerges,     // 表头的合并
        headerWidthConfig: headerWidthConfig, // 表头宽度配置
        verticalHeadersConfig: verticalHeadersConfig, // 表头纵向文字配置
        metadata: {
          exportTime: new Date().toISOString(),
          totalRows: tableData.cellRows ? tableData.cellRows.length : 0,
          totalColumns: headers[0].length, // 使用第一行表头的列数
          headerRows: headers.length,       // 表头行数
          hasMergedCells: (tableData.merges || []).length > 0,
          hasHeaderMerges: headerMerges.length > 0,
          hasLatexProcessing: true,        // 标记已进行LaTeX处理
          useDynamicHeader: tableData.metadata?.useDynamicHeader || false,
          hasCustomWidth: headerWidthConfig && headerWidthConfig.columnWidths,
          hasVerticalHeaders: verticalHeadersConfig && verticalHeadersConfig.some(v => v === true)
        }
      }

      console.log('准备的导出数据:', exportData)
      return exportData
    },

    /**
     * 处理表格数据，转换LaTeX公式为MathML
     */
    async processTableDataWithLatex(cellRows, headerWidthConfig = null) {
      console.log('开始处理表格数据，转换LaTeX公式，宽度配置:', headerWidthConfig)
      console.log('输入的cellRows:', cellRows)

      try {
        // 确保MathJax已初始化
        await mathFormulaUtils.initializeMathJax()

        // 创建新的数组来存储处理后的数据，避免修改原始数据
        const processedCellRows = []

        for (let rowIndex = 0; rowIndex < cellRows.length; rowIndex++) {
          const row = cellRows[rowIndex]
          const processedRow = []

          for (let colIndex = 0; colIndex < row.length; colIndex++) {
            const cellItem = row[colIndex]
            const cellContent = (typeof cellItem === 'string') ? cellItem : (cellItem?.content || '')

            try {
              // 构建基础CellData对象
              const cellData = {
                content: cellContent,
                originContent: cellContent, // 保存原始内容（处理前的内容）
                hasMath: false,
                mathML: null,
                hasMultipleContent: false,
                mathMLMap: null,
                width: this.getCellWidthFromConfig(rowIndex, colIndex, headerWidthConfig),
                height: this.getCellHeightFromConfig(rowIndex, colIndex, headerWidthConfig)
              }

              // 保留嵌套表格数据（如果存在）
              if (cellItem && typeof cellItem === 'object' && cellItem.nestedTable) {
                console.log(`单元格(${rowIndex},${colIndex})包含嵌套表格:`, cellItem.nestedTable)

                // 处理嵌套表格中的公式
                const processedNestedTable = await this.processNestedTableWithLatex(cellItem.nestedTable)
                cellData.nestedTable = processedNestedTable
              }

              // 检查是否包含数学公式
              if (mathFormulaUtils.containsMath(cellContent)) {
                console.log(`单元格(${rowIndex},${colIndex})包含公式:`, cellContent)

                // 分离公式和普通文本
                const separationResult = mathFormulaUtils.separateFormulaAndText(cellContent)

                if (separationResult.hasFormula) {
                  // 处理混合内容（文本 + 公式）
                  cellData.hasMath = true
                  cellData.hasMultipleContent = true
                  cellData.content = separationResult.processedContent

                  // 转换所有公式为MathML
                  const mathMLMap = {}
                  for (const formula of separationResult.formulas) {
                    try {
                      const mathML = await mathFormulaUtils.latexToMathML(formula.content)
                      if (mathML) {
                        mathMLMap[formula.placeholder] = mathML
                        console.log(`公式转换成功: ${formula.content} -> MathML`)
                      }
                    } catch (error) {
                      console.warn(`公式转换失败: ${formula.content}`, error)
                    }
                  }

                  cellData.mathMLMap = mathMLMap

                } else {
                  // 处理纯公式内容
                  const mathML = await mathFormulaUtils.latexToMathML(cellContent)
                  if (mathML) {
                    cellData.hasMath = true
                    cellData.mathML = mathML
                    console.log(`纯公式转换成功: ${cellContent} -> MathML`)
                  }
                }
              }

              processedRow.push(cellData)

            } catch (error) {
              console.error(`处理单元格(${rowIndex},${colIndex})失败:`, error)
              // 处理失败时使用原始内容，但保留嵌套表格数据
              const fallbackCellData = {
                content: cellContent,
                originContent: cellContent, // 保存原始内容
                hasMath: false,
                width: this.getCellWidthFromConfig(rowIndex, colIndex, headerWidthConfig),
                height: this.getCellHeightFromConfig(rowIndex, colIndex, headerWidthConfig)
              }

              // 即使处理失败，也要保留嵌套表格数据
              if (cellItem && typeof cellItem === 'object' && cellItem.nestedTable) {
                console.log(`处理失败但保留嵌套表格数据 (${rowIndex},${colIndex}):`, cellItem.nestedTable)
                fallbackCellData.nestedTable = cellItem.nestedTable
              }

              processedRow.push(fallbackCellData)
            }
          }

          processedCellRows.push(processedRow)
        }

        console.log('表格数据处理完成，转换的行数:', processedCellRows.length)
        return processedCellRows

      } catch (error) {
        console.error('处理表格数据失败:', error)
        // 如果处理失败，返回原始数据的简单格式，但保留嵌套表格数据
        return cellRows.map((row, rowIndex) =>
            row.map(cell => {
              const content = cell?.content || cell || ''
              const fallbackData = {
                content: content,
                originContent: content, // 保存原始内容
                hasMath: false,
                width: 100,
                height: this.getCellHeight(rowIndex, 0) // 尝试获取实际高度
              }

              // 保留嵌套表格数据
              if (cell && typeof cell === 'object' && cell.nestedTable) {
                console.log(`全局错误处理中保留嵌套表格数据:`, cell.nestedTable)
                fallbackData.nestedTable = cell.nestedTable
              }

              return fallbackData
            })
        )
      }
    },

    /**
     * 处理嵌套表格中的LaTeX公式
     * @param {Object} nestedTable - 嵌套表格配置对象
     * @returns {Object} 处理后的嵌套表格配置
     */
    async processNestedTableWithLatex(nestedTable) {
      try {
        console.log('开始处理嵌套表格中的LaTeX公式:', nestedTable)

        if (!nestedTable || !nestedTable.enabled || !nestedTable.config) {
          console.log('嵌套表格未启用或配置为空，直接返回')
          return nestedTable
        }

        const config = nestedTable.config
        if (!config.cellRows || !Array.isArray(config.cellRows)) {
          console.log('嵌套表格没有cellRows数据，直接返回')
          return nestedTable
        }

        // 创建处理后的嵌套表格配置，保留合并单元格信息
        const processedNestedTable = {
          enabled: nestedTable.enabled,
          config: {
            ...config,
            cellRows: []
          }
        }

        // 处理嵌套表格中的每个单元格
        for (let rowIndex = 0; rowIndex < config.cellRows.length; rowIndex++) {
          const row = config.cellRows[rowIndex]
          const processedRow = []

          for (let colIndex = 0; colIndex < row.length; colIndex++) {
            const cellItem = row[colIndex]
            const cellContent = cellItem?.content || ''

            try {
              // 构建嵌套单元格数据对象
              const nestedCellData = {
                content: cellContent,
                originContent: cellContent,
                hasMath: false,
                mathML: null,
                hasMultipleContent: false,
                mathMLMap: null
              }

              // 检查嵌套单元格是否包含数学公式
              if (mathFormulaUtils.containsMath(cellContent)) {
                console.log(`嵌套单元格(${rowIndex},${colIndex})包含公式:`, cellContent)

                // 分离公式和普通文本
                const separationResult = mathFormulaUtils.separateFormulaAndText(cellContent)

                if (separationResult.hasFormula) {
                  // 处理混合内容（文本 + 公式）
                  nestedCellData.hasMath = true
                  nestedCellData.hasMultipleContent = true
                  nestedCellData.content = separationResult.processedContent

                  // 转换所有公式为MathML
                  const mathMLMap = {}
                  for (const formula of separationResult.formulas) {
                    try {
                      const mathML = await mathFormulaUtils.latexToMathML(formula.content)
                      if (mathML) {
                        mathMLMap[formula.placeholder] = mathML
                        console.log(`嵌套表格公式转换成功: ${formula.content} -> MathML`)
                      }
                    } catch (error) {
                      console.warn(`嵌套表格公式转换失败: ${formula.content}`, error)
                    }
                  }

                  nestedCellData.mathMLMap = mathMLMap

                } else {
                  // 处理纯公式内容
                  const mathML = await mathFormulaUtils.latexToMathML(cellContent)
                  if (mathML) {
                    nestedCellData.hasMath = true
                    nestedCellData.mathML = mathML
                    console.log(`嵌套表格纯公式转换成功: ${cellContent} -> MathML`)
                  }
                }
              }

              processedRow.push(nestedCellData)

            } catch (error) {
              console.error(`处理嵌套单元格(${rowIndex},${colIndex})失败:`, error)
              // 处理失败时使用原始内容
              processedRow.push({
                content: cellContent,
                originContent: cellContent,
                hasMath: false
              })
            }
          }

          processedNestedTable.config.cellRows.push(processedRow)
        }

        console.log('嵌套表格LaTeX处理完成:', processedNestedTable)
        return processedNestedTable

      } catch (error) {
        console.error('处理嵌套表格LaTeX失败:', error)
        // 如果处理失败，返回原始嵌套表格数据
        return nestedTable
      }
    },

    /**
     * 获取单元格宽度配置（从TableContainer）
     */
    getCellWidth(rowIndex, colIndex) {
      const tableContainer = this.$refs.tableContainer
      console.log(`getCellWidth调用: 行${rowIndex} 列${colIndex}`)
      console.log('tableContainer存在:', !!tableContainer)

      if (tableContainer && tableContainer.currentHeaderWidthConfig) {
        const widthConfig = tableContainer.currentHeaderWidthConfig
        console.log('当前宽度配置:', widthConfig)

        if (widthConfig.columnWidths && widthConfig.columnWidths[colIndex] !== undefined) {
          const width = widthConfig.columnWidths[colIndex]
          console.log(`获取第${colIndex}列宽度: ${width}像素 (来源: 动态配置)`)
          return width
        } else {
          console.log(`第${colIndex}列在动态配置中未找到`)
        }
      } else {
        console.log('tableContainer或currentHeaderWidthConfig不存在')
      }

      // 默认宽度配置
      const defaultColumnWidths = [120, 200, 80, 60, 60, 80, 80, 80]
      const width = defaultColumnWidths[colIndex] || 150
      console.log(`获取第${colIndex}列宽度: ${width}像素 (来源: 默认配置)`)
      return width
    },

    /**
     * 获取单元格宽度配置（从传入的配置）
     */
    getCellWidthFromConfig(rowIndex, colIndex, headerWidthConfig) {
      console.log(`getCellWidthFromConfig调用: 行${rowIndex} 列${colIndex}`)
      console.log('传入的宽度配置:', headerWidthConfig)

      if (headerWidthConfig && headerWidthConfig.columnWidths && headerWidthConfig.columnWidths[colIndex] !== undefined) {
        const width = headerWidthConfig.columnWidths[colIndex]
        console.log(`获取第${colIndex}列宽度: ${width}像素 (来源: 传入配置)`)
        return width
      }

      // 回退到TableContainer配置
      return this.getCellWidth(rowIndex, colIndex)
    },

    /**
     * 获取单元格高度配置（从TableContainer）
     */
    getCellHeight(rowIndex, colIndex) {
      const tableContainer = this.$refs.tableContainer
      if (tableContainer) {
        // 优先使用实际的行高度
        const actualHeight = tableContainer.getActualRowHeight(rowIndex)
        if (actualHeight && actualHeight > 0) {
          console.log(`获取第${rowIndex}行实际高度: ${actualHeight}px`)
          return actualHeight
        }

        // 回退到表头配置
        if (tableContainer.currentHeaderWidthConfig) {
          const widthConfig = tableContainer.currentHeaderWidthConfig
          if (widthConfig.headerHeights && widthConfig.headerHeights.length > 0) {
            // 对于数据行，使用第一个表头行的高度作为参考
            return widthConfig.headerHeights[0] || 50
          }
        }
      }

      // 默认高度
      return 50
    },

    /**
     * 获取单元格高度配置（从传入的配置）
     */
    getCellHeightFromConfig(rowIndex, colIndex, headerWidthConfig) {
      // 优先使用TableContainer的实际高度
      const actualHeight = this.getCellHeight(rowIndex, colIndex)
      if (actualHeight && actualHeight > 0) {
        return actualHeight
      }

      if (headerWidthConfig && headerWidthConfig.headerHeights && headerWidthConfig.headerHeights.length > 0) {
        // 对于数据行，使用第一个表头行的高度作为参考
        return headerWidthConfig.headerHeights[0] || 50
      }

      // 默认高度
      return 50
    },

    /**
     * 下载Word文件
     */
    downloadWordFile(response, filename = '检验记录表') {
      try {
        // 创建Blob对象
        const blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        })

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        // 设置文件名（添加时间戳避免重复）
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
        link.download = `${filename}_${timestamp}.docx`

        // 触发下载
        document.body.appendChild(link)
        link.click()

        // 清理
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        console.log('文件下载完成:', link.download)
      } catch (error) {
        console.error('文件下载失败:', error)
        throw new Error('文件下载失败: ' + error.message)
      }
    },

    /**
     * 处理数据插入事件
     */
    handleDataInserted(event) {
      console.log('数据插入事件:', event)
    },

    /**
     * 处理表格更新事件
     */
    handleTableUpdated() {
      console.log('表格更新事件')
    },

    /**
     * 处理嵌套表格创建事件
     */
    handleNestedTableCreated(event) {
      console.log('嵌套表格创建事件:', event)
      this.operationResult = {
        success: true,
        message: `成功创建嵌套表格 (${event.config.config.columns}x${event.config.config.rows})`
      }
    },

    /**
     * 处理嵌套表格移除事件
     */
    handleNestedTableRemoved(event) {
      console.log('嵌套表格移除事件:', event)
      this.operationResult = {
        success: true,
        message: '成功移除嵌套表格'
      }
    },

    /**
     * 加载嵌套表格示例数据
     */
    loadNestedTableData() {
      try {
        console.log('开始加载嵌套表格示例数据...')

        // 直接加载嵌套表格预设数据
        const presetData = getPresetData('nestedTable')
        console.log('获取到的嵌套表格预设数据:', presetData)

        if (presetData) {
          // 设置JSON输入框内容
          this.jsonInput = JSON.stringify(presetData, null, 2)

          // 自动插入数据到表格
          this.insertOptions.clearExisting = true
          this.insertOptions.validateData = true

          // 直接调用insertJsonData方法
          this.insertJsonData()

          // 等待插入完成后验证嵌套表格
          this.$nextTick(() => {
            if (this.operationResult && this.operationResult.success) {
              // 验证是否包含嵌套表格
              this.verifyNestedTableData()
            } else {
              console.warn('数据插入失败，无法验证嵌套表格')
            }
          })
        } else {
          this.operationResult = {
            success: false,
            message: '嵌套表格预设数据不存在'
          }
        }
      } catch (error) {
        console.error('加载嵌套表格数据失败:', error)
        this.operationResult = {
          success: false,
          message: `加载失败: ${error.message}`
        }
      }
    },

    /**
     * 验证嵌套表格数据是否正确加载
     */
    verifyNestedTableData() {
      try {
        const tableContainer = this.$refs.tableContainer
        if (!tableContainer) {
          console.warn('表格组件未找到')
          return
        }

        console.log('开始验证嵌套表格数据...')
        console.log('表格数据行数:', tableContainer.dataRows.length)
        console.log('表格数据:', tableContainer.dataRows)

        // 检查表格数据中是否包含嵌套表格
        let hasNestedTables = false
        let nestedTableCount = 0
        let nestedTableDetails = []

        tableContainer.dataRows.forEach((row, rowIndex) => {
          row.forEach((cell, cellIndex) => {
            console.log(`检查单元格 [${rowIndex}][${cellIndex}]:`, cell)

            // 检查单元格是否有nestedTable属性
            if (cell.nestedTable) {
              console.log(`单元格 [${rowIndex}][${cellIndex}] 有nestedTable属性:`, cell.nestedTable)

              if (tableContainer.hasNestedTable && tableContainer.hasNestedTable(cell)) {
                hasNestedTables = true
                nestedTableCount++
                nestedTableDetails.push({
                  position: `(${rowIndex}, ${cellIndex})`,
                  content: cell.content,
                  nestedTable: cell.nestedTable
                })
                console.log(`发现嵌套表格 - 位置: (${rowIndex}, ${cellIndex}), 配置:`, cell.nestedTable)
              } else {
                console.warn(`单元格 [${rowIndex}][${cellIndex}] 有nestedTable但hasNestedTable返回false`)
              }
            }
          })
        })

        console.log('嵌套表格验证结果:', {
          hasNestedTables,
          nestedTableCount,
          nestedTableDetails
        })

        if (hasNestedTables) {
          console.log(`验证成功：表格中包含 ${nestedTableCount} 个嵌套表格`)
          this.operationResult = {
            success: true,
            message: `验证成功：表格中包含 ${nestedTableCount} 个嵌套表格，现在可以测试导出功能`
          }
        } else {
          console.warn('验证失败：表格中没有发现嵌套表格')

          // 提供更详细的调试信息
          let debugInfo = '调试信息：\n'
          debugInfo += `- 表格行数: ${tableContainer.dataRows.length}\n`
          debugInfo += `- 嵌套表格功能启用: ${tableContainer.enableNestedTables}\n`
          debugInfo += `- hasNestedTable方法存在: ${typeof tableContainer.hasNestedTable === 'function'}\n`

          // 检查是否有任何单元格包含nestedTable属性
          let cellsWithNestedTable = 0
          tableContainer.dataRows.forEach(row => {
            row.forEach(cell => {
              if (cell.nestedTable) {
                cellsWithNestedTable++
              }
            })
          })
          debugInfo += `- 包含nestedTable属性的单元格数: ${cellsWithNestedTable}`

          console.log(debugInfo)

          this.operationResult = {
            success: false,
            message: '验证失败：表格中没有发现嵌套表格。' + debugInfo
          }
        }
      } catch (error) {
        console.error('验证嵌套表格数据失败:', error)
        this.operationResult = {
          success: false,
          message: `验证失败: ${error.message}`
        }
      }
    }
  }
}
</script>

<style scoped>
.json-table-demo {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
  max-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  color: #333;
  margin-bottom: 10px;
}

.page-header p {
  color: #666;
  font-size: 16px;
}

.control-panel {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  max-height: 60vh;
  overflow-y: auto;
}

.json-input-section h3 {
  margin-bottom: 15px;
  color: #333;
}

/* 新的布局样式 */
.top-section {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: flex-start;
}

.preset-selector {
  flex: 1;
  min-width: 300px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #fafafa;
}

.right-section {
  flex: 1;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.selector-group {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 8px;
}

.selector-group label {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.preset-select {
  min-width: 200px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.preset-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.load-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background: #007bff;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.load-btn:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.load-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.clear-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background: #6c757d;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.clear-btn:hover {
  background: #545b62;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.preset-description {
  color: #666;
  font-style: italic;
  margin-left: 10px;
  margin-top: 5px;
}

.json-editor {
  margin-top: 10px;
}

.json-editor h4 {
  margin-bottom: 10px;
  color: #333;
  font-size: 16px;
}

.json-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.4;
  resize: vertical;
}

.json-options {
  padding: 5px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #f8f9fa;
}

.options-row {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  white-space: nowrap;
}

.number-input {
  width: 80px;
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-top: 10px;
}

.action-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-btn.insert {
  background: #28a745;
  color: white;
}

.action-btn.export {
  background: #17a2b8;
  color: white;
}

.action-btn.validate {
  background: #ffc107;
  color: #212529;
}

.action-btn.word {
  background: #6f42c1;
  color: white;
}

.action-btn.add-preset {
  background: #fd7e14;
  color: white;
}

.action-btn.nested-table {
  background: #17a2b8;
  color: white;
}

.action-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.result-section {
  padding: 5px 15px 0;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.result-section p {
  margin: 10px 0;
}

.result-content.success {
  background: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.result-content.error {
  background: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.error-details {
  margin-top: 10px;
  padding: 10px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

.table-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.table-section h3 {
  margin-bottom: 15px;
  color: #333;
}

.format-rules ul {
  padding-left: 20px;
}

.format-rules li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.format-rules code {
  background: #f8f9fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

@media (max-width: 768px) {
  .help-content {
    grid-template-columns: 1fr;
  }

  /* 响应式布局：小屏幕时改为垂直布局 */
  .top-section {
    flex-direction: column;
    gap: 15px;
  }

  .preset-selector,
  .right-section {
    min-width: auto;
    width: 100%;
  }

  .selector-group,
  .action-buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .preset-select,
  .load-btn,
  .clear-btn,
  .action-btn {
    width: 100%;
    margin-bottom: 8px;
  }

  .selector-group label {
    margin-bottom: 5px;
  }

  .options-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .option-item {
    margin-bottom: 5px;
  }
}
</style>
