<template>
  <div class="nested-table-designer">
    <!-- 主要内容区域 -->
    <div class="designer-content">
      <!-- 左侧设计面板 -->
      <div class="design-panel">
        <div class="panel-section">
          <h3>基本配置</h3>
          <div class="form-group">
            <label>表格标题:</label>
            <input
              v-model="tableConfig.title"
              type="text"
              placeholder="请输入表格标题"
              class="form-input"
            >
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>列数:</label>
              <input
                v-model.number="tableConfig.columns"
                type="number"
                min="1"
                max="10"
                class="form-input"
                @change="updateTableStructure"
              >
            </div>
            <div class="form-group">
              <label>行数:</label>
              <input
                v-model.number="tableConfig.rows"
                type="number"
                min="1"
                max="20"
                class="form-input"
                @change="updateTableStructure"
              >
            </div>
          </div>
        </div>

        <!-- 列宽配置 -->
        <div class="panel-section">
          <h3>列宽配置</h3>
          <div class="column-width-config">
            <div
              v-for="(column, index) in columnConfigs"
              :key="index"
              class="width-control-item"
            >
              <div class="width-control-header">
                <label>列 {{ index + 1 }} 宽度:</label>
                <span class="width-value">{{ column.width }}px</span>
              </div>
              <div class="width-control-body">
                <!-- 减少按钮 -->
                <button
                  @click="adjustColumnWidth(index, -10)"
                  class="adjust-btn decrease-btn"
                  :disabled="column.width <= 50"
                >
                  -
                </button>
                <!-- 数字输入框 -->
                <input
                  v-model.number="column.width"
                  type="number"
                  min="50"
                  max="500"
                  class="width-input"
                  @input="updatePreview"
                >
                <!-- 增加按钮 -->
                <button
                  @click="adjustColumnWidth(index, 10)"
                  class="adjust-btn increase-btn"
                  :disabled="column.width >= 500"
                >
                  +
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 行高配置 -->
        <div class="panel-section">
          <h3>行高配置</h3>
          <div class="row-height-config">
            <div
              v-for="(row, index) in rowConfigs"
              :key="index"
              class="height-control-item"
            >
              <div class="height-control-header">
                <label>行 {{ index + 1 }} 高度:</label>
                <span class="height-value">{{ row.height }}px</span>
              </div>
              <div class="height-control-body">
                <!-- 减少按钮 -->
                <button
                  @click="adjustRowHeight(index, -5)"
                  class="adjust-btn decrease-btn"
                  :disabled="row.height <= 30"
                >
                  -
                </button>
                <!-- 数字输入框 -->
                <input
                  v-model.number="row.height"
                  type="number"
                  min="30"
                  max="120"
                  class="height-input"
                  @input="updatePreview"
                >
                <!-- 增加按钮 -->
                <button
                  @click="adjustRowHeight(index, 5)"
                  class="adjust-btn increase-btn"
                  :disabled="row.height >= 120"
                >
                  +
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="panel-section">
          <div class="action-buttons">
            <button @click="resetConfig" class="btn btn-secondary">
              重置配置
            </button>
            <button @click="loadTemplate" class="btn btn-info">
              加载模板
            </button>
            <button @click="saveTemplate" class="btn btn-success">
              保存模板
            </button>
          </div>
        </div>
      </div>

      <!-- 右侧预览和导出区域 -->
      <div class="preview-panel">
        <!-- 预览区域 -->
        <div class="preview-section">
          <h3>表格预览</h3>
          <div class="table-preview-container">
            <div class="table-preview" ref="tablePreview">
              <table class="preview-table" :style="previewTableStyle">
                <!-- 数据行 -->
                <tbody>
                  <tr v-for="rowIndex in tableConfig.rows" :key="'row-' + rowIndex">
                    <td
                      v-for="(column, colIndex) in columnConfigs"
                      :key="'cell-' + rowIndex + '-' + colIndex"
                      :style="{
                        width: column.width + 'px',
                        height: getRowHeight(rowIndex - 1) + 'px'
                      }"
                      class="preview-data-cell"
                      @click="selectCell(rowIndex - 1, colIndex)"
                      :class="{
                        'selected': selectedCell.row === rowIndex - 1 && selectedCell.col === colIndex
                      }"
                    >
                      <CellEditor
                        :content="getCellContent(rowIndex - 1, colIndex)"
                        :width="column.width"
                        :height="getRowHeight(rowIndex - 1)"
                        :min-height="getRowHeight(rowIndex - 1)"
                        @content-change="updateCellContent(rowIndex - 1, colIndex, $event)"
                        @start-edit="onCellStartEdit(rowIndex - 1, colIndex)"
                        @finish-edit="onCellFinishEdit(rowIndex - 1, colIndex, $event)"
                      />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- 导出区域 -->
        <div class="export-section">
          <h3>导出配置</h3>

          <!-- JSON预览 -->
          <div class="json-preview">
            <h4>JSON配置预览</h4>
            <textarea
              v-model="jsonOutput"
              readonly
              class="json-textarea"
              rows="12"
            ></textarea>
          </div>

          <!-- 导出操作 -->
          <div class="export-actions">
            <button @click="copyJsonToClipboard" class="btn btn-primary">
              复制JSON配置
            </button>
            <button @click="downloadJsonFile" class="btn btn-info">
              下载JSON文件
            </button>
            <button @click="applyToDemo" class="btn btn-success">
              应用到演示页面
            </button>
          </div>

          <!-- 操作结果提示 -->
          <div v-if="operationResult" class="operation-result" :class="operationResult.type">
            {{ operationResult.message }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CellEditor from '@/components/CellEditor.vue'

export default {
  name: 'NestedTableDesigner',
  components: {
    CellEditor
  },
  data() {
    return {
      // 表格基本配置
      tableConfig: {
        title: '嵌套表格示例',
        columns: 3,
        rows: 3,
        defaultRowHeight: 40
      },

      // 列配置
      columnConfigs: [],

      // 行配置
      rowConfigs: [],

      // 单元格数据
      cellData: {},

      // 选中的单元格
      selectedCell: {
        row: -1,
        col: -1
      },

      // 操作结果
      operationResult: null,

      // JSON输出
      jsonOutput: ''
    }
  },
  computed: {
    // 预览表格样式
    previewTableStyle() {
      const totalWidth = this.columnConfigs.reduce((sum, col) => sum + col.width, 0)
      return {
        width: totalWidth + 'px',
        tableLayout: 'fixed' // 使用固定表格布局，确保列宽严格按照设置显示
      }
    }
  },
  mounted() {
    this.initializeColumnConfigs()
    this.initializeRowConfigs()
    this.updatePreview()
  },
  methods: {
    // 初始化列配置
    initializeColumnConfigs() {
      this.columnConfigs = []
      for (let i = 0; i < this.tableConfig.columns; i++) {
        this.columnConfigs.push({
          width: 120 // 只保留宽度配置
        })
      }
    },

    // 初始化行配置
    initializeRowConfigs() {
      this.rowConfigs = []
      for (let i = 0; i < this.tableConfig.rows; i++) {
        this.rowConfigs.push({
          height: this.tableConfig.defaultRowHeight || 40
        })
      }
    },

    // 更新表格结构
    updateTableStructure() {
      // 调整列配置数组长度
      const currentLength = this.columnConfigs.length
      const targetLength = this.tableConfig.columns

      if (targetLength > currentLength) {
        // 添加新列
        for (let i = currentLength; i < targetLength; i++) {
          this.columnConfigs.push({
            width: 120 // 只保留宽度配置
          })
        }
      } else if (targetLength < currentLength) {
        // 删除多余列时，清理对应的单元格数据
        for (let row = 0; row < this.tableConfig.rows; row++) {
          for (let col = targetLength; col < currentLength; col++) {
            const cellKey = `${row}-${col}`
            this.$delete(this.cellData, cellKey)
          }
        }
        // 删除多余列
        this.columnConfigs.splice(targetLength)
      }

      // 调整行配置数组长度
      const currentRowLength = this.rowConfigs.length
      const targetRowLength = this.tableConfig.rows

      if (targetRowLength > currentRowLength) {
        // 添加新行
        for (let i = currentRowLength; i < targetRowLength; i++) {
          this.rowConfigs.push({
            height: this.tableConfig.defaultRowHeight || 40
          })
        }
      } else if (targetRowLength < currentRowLength) {
        // 删除多余行
        this.rowConfigs.splice(targetRowLength)
      }

      // 清理超出行数的单元格数据
      const currentRows = Object.keys(this.cellData).map(key => parseInt(key.split('-')[0])).filter(row => !isNaN(row))
      const maxRow = Math.max(...currentRows, -1)
      if (maxRow >= this.tableConfig.rows) {
        for (let row = this.tableConfig.rows; row <= maxRow; row++) {
          for (let col = 0; col < this.tableConfig.columns; col++) {
            const cellKey = `${row}-${col}`
            this.$delete(this.cellData, cellKey)
          }
        }
      }

      this.updatePreview()
    },



    // 选择单元格
    selectCell(row, col) {
      this.selectedCell = { row, col }
    },

    // 获取单元格内容
    getCellContent(row, col) {
      const cellKey = `${row}-${col}`
      if (this.cellData[cellKey]) {
        return this.cellData[cellKey]
      }

      return `R${row + 1}C${col + 1}`
    },

    // 更新单元格内容
    updateCellContent(row, col, content) {
      const cellKey = `${row}-${col}`
      this.$set(this.cellData, cellKey, content)
      this.updatePreview()
    },

    // 单元格开始编辑
    onCellStartEdit(row, col) {
      this.selectCell(row, col)
      console.log(`开始编辑单元格 [${row}, ${col}]`)
    },

    // 单元格完成编辑
    onCellFinishEdit(row, col, content) {
      console.log(`完成编辑单元格 [${row}, ${col}]:`, content)
      this.updateCellContent(row, col, content)
    },

    // 获取单元格预览内容（保留原方法以兼容其他地方的调用）
    getCellPreviewContent(row, col) {
      return this.getCellContent(row, col)
    },

    // 获取行高度
    getRowHeight(rowIndex) {
      if (this.rowConfigs[rowIndex]) {
        return this.rowConfigs[rowIndex].height
      }
      return this.tableConfig.defaultRowHeight || 40
    },

    // 调整列宽度
    adjustColumnWidth(colIndex, delta) {
      if (this.columnConfigs[colIndex]) {
        const newWidth = this.columnConfigs[colIndex].width + delta
        if (newWidth >= 50 && newWidth <= 500) {
          this.columnConfigs[colIndex].width = newWidth
          this.updatePreview()
        }
      }
    },

    // 调整行高度
    adjustRowHeight(rowIndex, delta) {
      if (this.rowConfigs[rowIndex]) {
        const newHeight = this.rowConfigs[rowIndex].height + delta
        if (newHeight >= 30 && newHeight <= 120) {
          this.rowConfigs[rowIndex].height = newHeight
          this.updatePreview()
        }
      }
    },

    // 更新预览
    updatePreview() {
      this.generateJsonOutput()
    },

    // 生成JSON输出
    generateJsonOutput() {
      const nestedTableConfig = {
        enabled: true,
        config: {
          columnWidths: this.columnConfigs.map(col => col.width),
          rowHeights: this.rowConfigs.map(row => row.height),
          cellRows: this.generateCellRows(),
          metadata: {
            title: this.tableConfig.title,
            level: 1,
            parentCell: { row: 0, col: 0 },
            columns: this.tableConfig.columns,
            rows: this.tableConfig.rows
          }
        }
      }

      this.jsonOutput = JSON.stringify(nestedTableConfig, null, 2)
    },

    // 生成单元格行数据
    generateCellRows() {
      const cellRows = []

      // 添加数据行
      for (let row = 0; row < this.tableConfig.rows; row++) {
        const dataRow = this.columnConfigs.map((col, colIndex) => {
          const content = this.getCellContent(row, colIndex)
          return {
            content: content,
            originContent: content,
            hasMath: this.containsMath(content)
          }
        })
        cellRows.push(dataRow)
      }

      return cellRows
    },

    // 检测是否包含数学公式
    containsMath(content) {
      if (!content) return false

      const mathPatterns = [
        /\$.*?\$/,
        /\$\$.*?\$\$/,
        /\\\(.*?\\\)/,
        /\\\[.*?\\\]/,
        /\\begin\{.*?\}.*?\\end\{.*?\}/,
        /\\[a-zA-Z]+/
      ]

      return mathPatterns.some(pattern => pattern.test(content))
    },

    // 复制JSON到剪贴板
    async copyJsonToClipboard() {
      try {
        await navigator.clipboard.writeText(this.jsonOutput)
        this.showOperationResult('success', 'JSON配置已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        this.showOperationResult('error', '复制失败，请手动复制')
      }
    },

    // 下载JSON文件
    downloadJsonFile() {
      try {
        const blob = new Blob([this.jsonOutput], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `nested-table-config-${Date.now()}.json`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        this.showOperationResult('success', 'JSON文件下载成功')
      } catch (error) {
        console.error('下载失败:', error)
        this.showOperationResult('error', '下载失败')
      }
    },

    // 应用到演示页面
    applyToDemo() {
      try {
        // 将配置保存到localStorage，供演示页面使用
        localStorage.setItem('nestedTableDesignerConfig', this.jsonOutput)
        this.showOperationResult('success', '配置已保存，可在演示页面中使用')

        // 可以选择跳转到演示页面
        if (confirm('是否跳转到演示页面查看效果？')) {
          this.$router.push('/test/json-table-demo')
        }
      } catch (error) {
        console.error('应用失败:', error)
        this.showOperationResult('error', '应用失败')
      }
    },

    // 重置配置
    resetConfig() {
      if (confirm('确定要重置所有配置吗？')) {
        this.tableConfig = {
          title: '嵌套表格示例',
          columns: 3,
          rows: 3,
          defaultRowHeight: 40
        }
        this.initializeColumnConfigs()
        this.initializeRowConfigs()
        this.cellData = {} // 清空单元格数据
        this.selectedCell = { row: -1, col: -1 }
        this.updatePreview()
        this.showOperationResult('success', '配置已重置')
      }
    },

    // 加载模板
    loadTemplate() {
      const templates = [
        {
          name: '基础表格',
          config: {
            title: '基础表格模板',
            columns: 3,
            rows: 3,
            defaultRowHeight: 40
          },
          columnConfigs: [
            { width: 120 },
            { width: 100 },
            { width: 150 }
          ],
          rowConfigs: [
            { height: 40 },
            { height: 40 },
            { height: 40 }
          ]
        },
        {
          name: '宽表格',
          config: {
            title: '宽表格模板',
            columns: 5,
            rows: 4,
            defaultRowHeight: 50
          },
          columnConfigs: [
            { width: 100 },
            { width: 120 },
            { width: 80 },
            { width: 100 },
            { width: 110 }
          ],
          rowConfigs: [
            { height: 50 },
            { height: 50 },
            { height: 50 },
            { height: 50 }
          ]
        }
      ]

      const templateNames = templates.map(t => t.name)
      const selectedTemplate = prompt(`请选择模板:\n${templateNames.map((name, index) => `${index + 1}. ${name}`).join('\n')}\n\n请输入序号:`)

      if (selectedTemplate && !isNaN(selectedTemplate)) {
        const index = parseInt(selectedTemplate) - 1
        if (index >= 0 && index < templates.length) {
          const template = templates[index]
          this.tableConfig = { ...template.config }
          this.columnConfigs = template.columnConfigs.map(col => ({ ...col }))
          this.rowConfigs = template.rowConfigs ? template.rowConfigs.map(row => ({ ...row })) : []
          // 如果模板没有行配置，则初始化默认行配置
          if (this.rowConfigs.length === 0) {
            this.initializeRowConfigs()
          }
          this.updatePreview()
          this.showOperationResult('success', `已加载模板: ${template.name}`)
        }
      }
    },

    // 保存模板
    saveTemplate() {
      const templateName = prompt('请输入模板名称:')
      if (templateName) {
        const template = {
          name: templateName,
          config: { ...this.tableConfig },
          columnConfigs: this.columnConfigs.map(col => ({ ...col })),
          rowConfigs: this.rowConfigs.map(row => ({ ...row }))
        }

        // 保存到localStorage
        const savedTemplates = JSON.parse(localStorage.getItem('nestedTableTemplates') || '[]')
        savedTemplates.push(template)
        localStorage.setItem('nestedTableTemplates', JSON.stringify(savedTemplates))

        this.showOperationResult('success', `模板 "${templateName}" 已保存`)
      }
    },

    // 显示操作结果
    showOperationResult(type, message) {
      this.operationResult = { type, message }
      setTimeout(() => {
        this.operationResult = null
      }, 3000)
    }
  }
}
</script>

<style scoped>
.nested-table-designer {
  padding: 20px;
  background: linear-gradient(180deg, #124B9A, #0D438D);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 动态背景效果 */
.nested-table-designer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 50%, rgba(18, 75, 154, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, rgba(13, 67, 141, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 40% 20%, rgba(0, 150, 255, 0.2) 0%, transparent 50%);
  animation: floatBackground 20s ease-in-out infinite;
  z-index: 1;
}

@keyframes floatBackground {
  0%, 100% { transform: translateX(0) translateY(0); }
  33% { transform: translateX(20px) translateY(-10px); }
  66% { transform: translateX(-10px) translateY(10px); }
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.page-header h1 {
  background: linear-gradient(135deg, #17d2ef 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.page-header p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.designer-content {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  position: relative;
  z-index: 2;
}

.design-panel {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-height: 80vh;
  overflow-y: auto;
}

.preview-panel {
  flex: 1.5;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.panel-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.panel-section:last-child {
  border-bottom: none;
}

.panel-section h3 {
  background: linear-gradient(135deg, #17d2ef 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 15px;
  font-size: 18px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.form-group {
  margin-bottom: 15px;
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-row .form-group {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.form-input, .form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  font-size: 14px;
  background: linear-gradient(0deg, #0D3A8D, #03256B);
  color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
}

.form-input:focus, .form-select:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(0, 150, 255, 0.3);
}

.form-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* 列宽配置样式 */
.column-width-config {
  max-height: 400px;
  overflow-y: auto;
}

.width-control-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(5px);
}

.width-control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.width-control-header label {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.width-value {
  font-weight: 600;
  color: #0096FF;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.width-control-body {
  display: flex;
  align-items: center;
  gap: 10px;
}

.width-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  text-align: center;
  font-size: 14px;
  min-width: 80px;
  background: linear-gradient(0deg, #0D3A8D, #03256B);
  color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
}

.width-input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(0, 150, 255, 0.3);
}

.adjust-btn {
  width: 32px;
  height: 32px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  background: linear-gradient(0deg, #0D3A8D, #03256B);
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
}

.adjust-btn:hover:not(:disabled) {
  background: linear-gradient(0deg, #0096FF, #043475);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.adjust-btn:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.4);
  cursor: not-allowed;
  border-color: rgba(255, 255, 255, 0.1);
}

.decrease-btn {
  color: #ff6b6b;
}

.decrease-btn:hover:not(:disabled) {
  border-color: #ff6b6b;
  color: #ff6b6b;
}

.increase-btn {
  color: #4ecdc4;
}

.increase-btn:hover:not(:disabled) {
  border-color: #4ecdc4;
  color: #4ecdc4;
}

/* 行高配置样式 */
.row-height-config {
  max-height: 400px;
  overflow-y: auto;
}

.height-control-item {
  margin-bottom: 15px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(5px);
}

.height-control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.height-control-header label {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.height-value {
  font-weight: 600;
  color: #4ecdc4;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.height-control-body {
  display: flex;
  align-items: center;
  gap: 10px;
}

.height-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  text-align: center;
  font-size: 14px;
  min-width: 80px;
  background: linear-gradient(0deg, #0D3A8D, #03256B);
  color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
}

.height-input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(0, 150, 255, 0.3);
}



.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-primary {
  background: linear-gradient(0deg, #0096FF, #043475);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary {
  background: linear-gradient(0deg, #6c757d, #495057);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-success {
  background: linear-gradient(0deg, #28a745, #1e7e34);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-info {
  background: linear-gradient(0deg, #17a2b8, #138496);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
}

.btn:active {
  transform: translateY(0);
}

.preview-section, .export-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.preview-section h3, .export-section h3 {
  background: linear-gradient(135deg, #17d2ef 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 15px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.table-preview-container {
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  overflow: auto;
  max-height: 400px;
  background: rgba(255, 255, 255, 0.05);
}

.preview-table {
  border-collapse: collapse;
  font-size: 14px;
  table-layout: fixed; /* 确保使用固定表格布局 */
}

.preview-header-cell, .preview-data-cell {
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px;
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
}

.preview-header-cell {
  background: linear-gradient(90deg, #0d438d 0%, rgba(13, 67, 141, 0.4) 100%);
  font-weight: 500;
  color: rgba(255, 255, 255, 0.95);
}

.preview-data-cell {
  cursor: pointer;
  transition: background-color 0.2s ease;
  padding: 0;
  position: relative;
  background: rgba(255, 255, 255, 0.05);
}

.preview-data-cell:hover {
  background: rgba(0, 150, 255, 0.2);
}

.preview-data-cell.selected {
  background: rgba(33, 150, 243, 0.3);
  border-color: rgba(33, 150, 243, 0.6);
}

/* 确保 CellEditor 在表格单元格中正确显示 */
.preview-data-cell .cell-editor-wrapper {
  width: 100%;
  height: 100%;
  min-height: inherit;
}

.preview-data-cell .cell-display {
  padding: 8px;
  min-height: inherit;
  box-sizing: border-box;
}

.preview-data-cell .cell-editor {
  padding: 8px;
  min-height: inherit;
  box-sizing: border-box;
}

.json-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  resize: vertical;
  background: linear-gradient(0deg, #0D3A8D, #03256B);
  color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
}

.json-textarea:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(0, 150, 255, 0.3);
}

.export-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  flex-wrap: wrap;
}

.operation-result {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
  backdrop-filter: blur(5px);
}

.operation-result.success {
  background: rgba(40, 167, 69, 0.2);
  color: #d4edda;
  border: 1px solid rgba(40, 167, 69, 0.4);
}

.operation-result.error {
  background: rgba(220, 53, 69, 0.2);
  color: #f8d7da;
  border: 1px solid rgba(220, 53, 69, 0.4);
}

/* 滚动条样式优化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(0deg, #70a7cb, #5a8db3);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(0deg, #5a8db3, #4a7ba3);
}

::-webkit-scrollbar-corner {
  background: rgba(255, 255, 255, 0.1);
}

/* 调整大小手柄优化 */
::-webkit-resizer {
  background: linear-gradient(135deg, #70a7cb, #5a8db3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0 0 4px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Firefox 滚动条支持 */
* {
  scrollbar-width: thin;
  scrollbar-color: #70a7cb rgba(255, 255, 255, 0.1);
}

@media (max-width: 1200px) {
  .designer-content {
    flex-direction: column;
  }

  .design-panel {
    max-height: none;
  }
}
</style>
