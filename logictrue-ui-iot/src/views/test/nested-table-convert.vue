<template>
  <div class="nested-table-designer">
    <!-- 主要内容区域 -->
    <div class="designer-content">
      <!-- 左侧设计面板 -->
      <div class="design-panel">
        <div class="panel-section">
          <h3>基本配置</h3>
          <div class="form-group">
            <label>表格标题:</label>
            <input
                v-model="tableConfig.title"
                type="text"
                placeholder="请输入表格标题"
                class="form-input"
            >
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>列数:</label>
              <input
                  v-model.number="tableConfig.columns"
                  type="number"
                  min="1"
                  max="10"
                  class="form-input"
                  @change="updateTableStructure"
              >
            </div>
            <div class="form-group">
              <label>行数:</label>
              <input
                  v-model.number="tableConfig.rows"
                  type="number"
                  min="1"
                  max="20"
                  class="form-input"
                  @change="updateTableStructure"
              >
            </div>
          </div>
        </div>

        <!-- 列宽配置 -->
        <div class="panel-section">
          <h3>列宽配置</h3>
          <div class="column-width-config">
            <div
                v-for="(column, index) in columnConfigs"
                :key="index"
                class="width-control-item"
            >
              <div class="width-control-header">
                <label>列 {{ index + 1 }} 宽度:</label>
                <span class="width-value">{{ column.width }}px</span>
              </div>
              <div class="width-control-body">
                <!-- 减少按钮 -->
                <button
                    @click="adjustColumnWidth(index, -10)"
                    class="adjust-btn decrease-btn"
                    :disabled="column.width <= 50"
                >
                  -
                </button>
                <!-- 数字输入框 -->
                <input
                    v-model.number="column.width"
                    type="number"
                    min="50"
                    max="500"
                    class="width-input"
                    @input="updatePreview"
                >
                <!-- 增加按钮 -->
                <button
                    @click="adjustColumnWidth(index, 10)"
                    class="adjust-btn increase-btn"
                    :disabled="column.width >= 500"
                >
                  +
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 行高配置 -->
        <div class="panel-section">
          <h3>行高配置</h3>
          <div class="row-height-config">
            <div
                v-for="(row, index) in rowConfigs"
                :key="index"
                class="height-control-item"
            >
              <div class="height-control-header">
                <label>行 {{ index + 1 }} 高度:</label>
                <span class="height-value">{{ row.height }}px</span>
              </div>
              <div class="height-control-body">
                <!-- 减少按钮 -->
                <button
                    @click="adjustRowHeight(index, -5)"
                    class="adjust-btn decrease-btn"
                    :disabled="row.height <= 30"
                >
                  -
                </button>
                <!-- 数字输入框 -->
                <input
                    v-model.number="row.height"
                    type="number"
                    min="30"
                    max="120"
                    class="height-input"
                    @input="updatePreview"
                >
                <!-- 增加按钮 -->
                <button
                    @click="adjustRowHeight(index, 5)"
                    class="adjust-btn increase-btn"
                    :disabled="row.height >= 120"
                >
                  +
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="panel-section">
          <div class="action-buttons">
            <button @click="resetConfig" class="btn btn-secondary">
              重置配置
            </button>
            <button @click="loadTemplate" class="btn btn-info">
              加载模板
            </button>
            <button @click="saveTemplate" class="btn btn-success">
              保存模板
            </button>
            <button @click="showImportDialog" class="btn btn-primary">
              导入数据
            </button>
            <button @click="showExportDialog" class="btn btn-info">
              导出数据
            </button>
          </div>
        </div>
      </div>

      <!-- 右侧预览和导出区域 -->
      <div class="preview-panel">
        <!-- 预览区域 -->
        <div class="preview-section">
          <h3>表格预览</h3>
          <div class="table-preview-container">
            <div class="table-preview" ref="tablePreview">
              <table class="preview-table" :style="previewTableStyle">
                <!-- 数据行 -->
                <tbody>
                <tr v-for="rowIndex in tableConfig.rows" :key="'row-' + rowIndex">
                  <td
                      v-for="(column, colIndex) in columnConfigs"
                      :key="'cell-' + rowIndex + '-' + colIndex"
                      :style="getCellStyle(rowIndex - 1, colIndex)"
                      :colspan="getCellColspan(rowIndex - 1, colIndex)"
                      :rowspan="getCellRowspan(rowIndex - 1, colIndex)"
                      :class="getCellClass(rowIndex - 1, colIndex)"
                      @click="selectCell(rowIndex - 1, colIndex)"
                  >
                    <CellEditor
                        v-if="shouldShowCell(rowIndex - 1, colIndex)"
                        :content="getCellContent(rowIndex - 1, colIndex)"
                        :width="getEffectiveCellWidth(rowIndex - 1, colIndex)"
                        :height="getEffectiveCellHeight(rowIndex - 1, colIndex)"
                        :min-height="getEffectiveCellHeight(rowIndex - 1, colIndex)"
                        @content-change="updateCellContent(rowIndex - 1, colIndex, $event)"
                        @start-edit="onCellStartEdit(rowIndex - 1, colIndex)"
                        @finish-edit="onCellFinishEdit(rowIndex - 1, colIndex, $event)"
                    />
                  </td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- 导出区域 -->
        <div class="export-section">
          <h3>导出配置</h3>

          <!-- JSON预览 -->
          <div class="json-preview">
            <h4>JSON配置预览</h4>
            <textarea
                v-model="jsonOutput"
                readonly
                class="json-textarea"
                rows="12"
            ></textarea>
          </div>

          <!-- 导出操作 -->
          <div class="export-actions">
            <button @click="copyJsonToClipboard" class="btn btn-primary">
              复制JSON配置
            </button>
            <button @click="downloadJsonFile" class="btn btn-info">
              下载JSON文件
            </button>
            <button @click="applyToDemo" class="btn btn-success">
              应用到演示页面
            </button>
          </div>

          <!-- 操作结果提示 -->
          <div v-if="operationResult" class="operation-result" :class="operationResult.type">
            {{ operationResult.message }}
          </div>
        </div>
      </div>
    </div>
    <!-- 数据导入/导出模态框 -->
    <el-dialog
      :title="isExportMode ? '导出表格数据' : '导入表格数据'"
      :visible.sync="showImportModal"
      width="600px"
      :close-on-click-modal="false"
      :append-to-body="true"
      @close="closeImportDialog"
    >
      <div class="import-dialog-content">
        <div class="form-group">
          <label>{{ isExportMode ? '导出数据格式 (JSON):' : '数据格式 (JSON):' }}</label>
          <el-input
            v-if="isExportMode"
            v-model="exportData"
            type="textarea"
            :rows="8"
            readonly
            placeholder="导出的数据将显示在这里"
            resize="vertical"
          ></el-input>
          <el-input
            v-else
            v-model="importData"
            type="textarea"
            :rows="8"
            placeholder='请输入数据格式，例如：[["1-1",{"colspan":2,"rowspan":2,"value":"合并单元格"}],["1-2",{"colspan":0,"rowspan":0}],["2-1",{"colspan":0,"rowspan":0}],["2-2",{"colspan":1,"rowspan":1,"value":"普通单元格"}]]'
            resize="vertical"
          ></el-input>
        </div>
        <div class="format-help" v-if="!isExportMode">
          <h4>格式说明：</h4>
          <ul>
            <li>行和列从1开始编号</li>
            <li>1-1表示第一行第一列</li>
            <li>1-2表示第一行第二列</li>
            <li>colspan: 列跨度（大于1表示合并列）</li>
            <li>rowspan: 行跨度（大于1表示合并行）</li>
            <li>value: 单元格内容</li>
            <li>colspan=0或rowspan=0表示被合并的单元格</li>
          </ul>
          <p><strong>合并单元格示例：</strong></p>
          <pre style="background: rgba(0,0,0,0.2); padding: 8px; border-radius: 4px; font-size: 12px;">
[["1-1",{"colspan":2,"rowspan":1,"value":"横向合并"}],
 ["2-1",{"colspan":1,"rowspan":2,"value":"纵向合并"}],
 ["2-2",{"colspan":0,"rowspan":0}],
 ["3-1",{"colspan":0,"rowspan":0}]]</pre>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeImportDialog">关闭</el-button>
        <el-button v-if="isExportMode" type="primary" @click="copyExportData">复制数据</el-button>
        <el-button v-if="!isExportMode" type="primary" @click="importTableData">确认导入</el-button>
      </span>
    </el-dialog>
  </div>

</template>


<script>
import CellEditor from '@/components/CellEditor.vue'

export default {
  name: 'NestedTableDesigner',
  components: {
    CellEditor
  },
  data() {
    return {
      // 表格基本配置
      tableConfig: {
        title: '嵌套表格示例',
        columns: 3,
        rows: 3,
        defaultRowHeight: 40
      },

      // 列配置
      columnConfigs: [],

      // 行配置
      rowConfigs: [],

      // 单元格数据
      cellData: {},

      // 合并单元格数据
      mergedCells: {},

      // 选中的单元格
      selectedCell: {
        row: -1,
        col: -1
      },

      // 操作结果
      operationResult: null,

      // JSON输出
      jsonOutput: '',

      // 导入数据相关
      showImportModal: false,
      importData: '',

      // 导出数据相关
      isExportMode: false,
      exportData: ''
    }
  },
  computed: {
    // 预览表格样式
    previewTableStyle() {
      const totalWidth = this.columnConfigs.reduce((sum, col) => sum + col.width, 0)
      return {
        width: totalWidth + 'px',
        tableLayout: 'fixed' // 使用固定表格布局，确保列宽严格按照设置显示
      }
    }
  },
  mounted() {
    this.initializeColumnConfigs()
    this.initializeRowConfigs()
    this.updatePreview()
  },
  methods: {
    // 初始化列配置
    initializeColumnConfigs() {
      this.columnConfigs = []
      for (let i = 0; i < this.tableConfig.columns; i++) {
        this.columnConfigs.push({
          width: 120 // 只保留宽度配置
        })
      }
    },

    // 初始化行配置
    initializeRowConfigs() {
      this.rowConfigs = []
      for (let i = 0; i < this.tableConfig.rows; i++) {
        this.rowConfigs.push({
          height: this.tableConfig.defaultRowHeight || 40
        })
      }
    },

    // 更新表格结构
    updateTableStructure() {
      // 调整列配置数组长度
      const currentLength = this.columnConfigs.length
      const targetLength = this.tableConfig.columns

      if (targetLength > currentLength) {
        // 添加新列
        for (let i = currentLength; i < targetLength; i++) {
          this.columnConfigs.push({
            width: 120 // 只保留宽度配置
          })
        }
      } else if (targetLength < currentLength) {
        // 删除多余列时，清理对应的单元格数据
        for (let row = 0; row < this.tableConfig.rows; row++) {
          for (let col = targetLength; col < currentLength; col++) {
            const cellKey = `${row}-${col}`
            this.$delete(this.cellData, cellKey)
          }
        }
        // 删除多余列
        this.columnConfigs.splice(targetLength)
      }

      // 调整行配置数组长度
      const currentRowLength = this.rowConfigs.length
      const targetRowLength = this.tableConfig.rows

      if (targetRowLength > currentRowLength) {
        // 添加新行
        for (let i = currentRowLength; i < targetRowLength; i++) {
          this.rowConfigs.push({
            height: this.tableConfig.defaultRowHeight || 40
          })
        }
      } else if (targetRowLength < currentRowLength) {
        // 删除多余行
        this.rowConfigs.splice(targetRowLength)
      }

      // 清理超出行数的单元格数据
      const currentRows = Object.keys(this.cellData).map(key => parseInt(key.split('-')[0])).filter(row => !isNaN(row))
      const maxRow = Math.max(...currentRows, -1)
      if (maxRow >= this.tableConfig.rows) {
        for (let row = this.tableConfig.rows; row <= maxRow; row++) {
          for (let col = 0; col < this.tableConfig.columns; col++) {
            const cellKey = `${row}-${col}`
            this.$delete(this.cellData, cellKey)
          }
        }
      }

      this.updatePreview()
    },



    // 选择单元格
    selectCell(row, col) {
      this.selectedCell = { row, col }
    },

    // 获取单元格内容
    getCellContent(row, col) {
      const cellKey = `${row}-${col}`
      if (this.cellData[cellKey]) {
        return this.cellData[cellKey]
      }

      return `R${row + 1}C${col + 1}`
    },

    // 更新单元格内容
    updateCellContent(row, col, content) {
      const cellKey = `${row}-${col}`
      this.$set(this.cellData, cellKey, content)
      this.updatePreview()
    },

    // 单元格开始编辑
    onCellStartEdit(row, col) {
      this.selectCell(row, col)
      console.log(`开始编辑单元格 [${row}, ${col}]`)
    },

    // 单元格完成编辑
    onCellFinishEdit(row, col, content) {
      console.log(`完成编辑单元格 [${row}, ${col}]:`, content)
      this.updateCellContent(row, col, content)
    },

    // 获取单元格预览内容（保留原方法以兼容其他地方的调用）
    getCellPreviewContent(row, col) {
      return this.getCellContent(row, col)
    },

    // 获取行高度
    getRowHeight(rowIndex) {
      if (this.rowConfigs[rowIndex]) {
        return this.rowConfigs[rowIndex].height
      }
      return this.tableConfig.defaultRowHeight || 40
    },

    // 调整列宽度
    adjustColumnWidth(colIndex, delta) {
      if (this.columnConfigs[colIndex]) {
        const newWidth = this.columnConfigs[colIndex].width + delta
        if (newWidth >= 50 && newWidth <= 500) {
          this.columnConfigs[colIndex].width = newWidth
          this.updatePreview()
        }
      }
    },

    // 调整行高度
    adjustRowHeight(rowIndex, delta) {
      if (this.rowConfigs[rowIndex]) {
        const newHeight = this.rowConfigs[rowIndex].height + delta
        if (newHeight >= 30 && newHeight <= 120) {
          this.rowConfigs[rowIndex].height = newHeight
          this.updatePreview()
        }
      }
    },

    // 更新预览
    updatePreview() {
      this.generateJsonOutput()
    },

    // 生成JSON输出
    generateJsonOutput() {
      const nestedTableConfig = {
        enabled: true,
        config: {
          columnWidths: this.columnConfigs.map(col => col.width),
          rowHeights: this.rowConfigs.map(row => row.height),
          cellRows: this.generateCellRows(),
          merges: this.generateMerges(),
          metadata: {
            title: this.tableConfig.title,
            level: 1,
            parentCell: { row: 0, col: 0 },
            columns: this.tableConfig.columns,
            rows: this.tableConfig.rows
          }
        }
      }

      this.jsonOutput = JSON.stringify(nestedTableConfig, null, 2)
    },

    // 生成合并单元格信息
    generateMerges() {
      const merges = []

      // 遍历所有合并单元格
      for (const [cellKey, mergeInfo] of Object.entries(this.mergedCells)) {
        const [row, col] = cellKey.split('-').map(Number)
        const colspan = mergeInfo.colspan
        const rowspan = mergeInfo.rowspan

        // 只有当colspan > 1 或 rowspan > 1时才添加到merges中
        if (colspan > 1 || rowspan > 1) {
          merges.push({
            startRow: row,
            startCol: col,
            endRow: row + rowspan - 1,
            endCol: col + colspan - 1,
            content: this.getCellContent(row, col)
          })
        }
      }

      return merges
    },

    // 生成单元格行数据
    generateCellRows() {
      const cellRows = []

      // 添加数据行
      for (let row = 0; row < this.tableConfig.rows; row++) {
        const dataRow = this.columnConfigs.map((col, colIndex) => {
          let content = this.getCellContent(row, colIndex)

          // 检查当前单元格是否被其他单元格合并
          if (this.isMergedByOtherCell(row, colIndex)) {
            content = '' // 被合并的单元格设置为空字符串
          }

          return {
            content: content,
            originContent: content,
            hasMath: this.containsMath(content)
          }
        })
        cellRows.push(dataRow)
      }

      return cellRows
    },

    // 检测是否包含数学公式
    containsMath(content) {
      if (!content) return false

      const mathPatterns = [
        /\$.*?\$/,
        /\$\$.*?\$\$/,
        /\\\(.*?\\\)/,
        /\\\[.*?\\\]/,
        /\\begin\{.*?\}.*?\\end\{.*?\}/,
        /\\[a-zA-Z]+/
      ]

      return mathPatterns.some(pattern => pattern.test(content))
    },

    // 复制JSON到剪贴板
    async copyJsonToClipboard() {
      try {
        await navigator.clipboard.writeText(this.jsonOutput)
        this.showOperationResult('success', 'JSON配置已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        this.showOperationResult('error', '复制失败，请手动复制')
      }
    },

    // 下载JSON文件
    downloadJsonFile() {
      try {
        const blob = new Blob([this.jsonOutput], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `nested-table-config-${Date.now()}.json`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        this.showOperationResult('success', 'JSON文件下载成功')
      } catch (error) {
        console.error('下载失败:', error)
        this.showOperationResult('error', '下载失败')
      }
    },

    // 应用到演示页面
    applyToDemo() {
      try {
        // 将配置保存到localStorage，供演示页面使用
        localStorage.setItem('nestedTableDesignerConfig', this.jsonOutput)
        this.showOperationResult('success', '配置已保存，可在演示页面中使用')

        // 可以选择跳转到演示页面
        if (confirm('是否跳转到演示页面查看效果？')) {
          this.$router.push('/test/json-table-demo')
        }
      } catch (error) {
        console.error('应用失败:', error)
        this.showOperationResult('error', '应用失败')
      }
    },

    // 重置配置
    resetConfig() {
      if (confirm('确定要重置所有配置吗？')) {
        this.tableConfig = {
          title: '嵌套表格示例',
          columns: 3,
          rows: 3,
          defaultRowHeight: 40
        }
        this.initializeColumnConfigs()
        this.initializeRowConfigs()
        this.cellData = {} // 清空单元格数据
        this.mergedCells = {} // 清空合并单元格数据
        this.selectedCell = { row: -1, col: -1 }
        this.updatePreview()
        this.showOperationResult('success', '配置已重置')
      }
    },

    // 加载模板
    loadTemplate() {
      const templates = [
        {
          name: '基础表格',
          config: {
            title: '基础表格模板',
            columns: 3,
            rows: 3,
            defaultRowHeight: 40
          },
          columnConfigs: [
            { width: 120 },
            { width: 100 },
            { width: 150 }
          ],
          rowConfigs: [
            { height: 40 },
            { height: 40 },
            { height: 40 }
          ]
        },
        {
          name: '宽表格',
          config: {
            title: '宽表格模板',
            columns: 5,
            rows: 4,
            defaultRowHeight: 50
          },
          columnConfigs: [
            { width: 100 },
            { width: 120 },
            { width: 80 },
            { width: 100 },
            { width: 110 }
          ],
          rowConfigs: [
            { height: 50 },
            { height: 50 },
            { height: 50 },
            { height: 50 }
          ]
        }
      ]

      const templateNames = templates.map(t => t.name)
      const selectedTemplate = prompt(`请选择模板:\n${templateNames.map((name, index) => `${index + 1}. ${name}`).join('\n')}\n\n请输入序号:`)

      if (selectedTemplate && !isNaN(selectedTemplate)) {
        const index = parseInt(selectedTemplate) - 1
        if (index >= 0 && index < templates.length) {
          const template = templates[index]
          this.tableConfig = { ...template.config }
          this.columnConfigs = template.columnConfigs.map(col => ({ ...col }))
          this.rowConfigs = template.rowConfigs ? template.rowConfigs.map(row => ({ ...row })) : []
          // 如果模板没有行配置，则初始化默认行配置
          if (this.rowConfigs.length === 0) {
            this.initializeRowConfigs()
          }
          this.updatePreview()
          this.showOperationResult('success', `已加载模板: ${template.name}`)
        }
      }
    },

    // 保存模板
    saveTemplate() {
      const templateName = prompt('请输入模板名称:')
      if (templateName) {
        const template = {
          name: templateName,
          config: { ...this.tableConfig },
          columnConfigs: this.columnConfigs.map(col => ({ ...col })),
          rowConfigs: this.rowConfigs.map(row => ({ ...row }))
        }

        // 保存到localStorage
        const savedTemplates = JSON.parse(localStorage.getItem('nestedTableTemplates') || '[]')
        savedTemplates.push(template)
        localStorage.setItem('nestedTableTemplates', JSON.stringify(savedTemplates))

        this.showOperationResult('success', `模板 "${templateName}" 已保存`)
      }
    },

    // 显示操作结果
    showOperationResult(type, message) {
      this.operationResult = { type, message }
      setTimeout(() => {
        this.operationResult = null
      }, 3000)
    },

    // 显示导入对话框
    showImportDialog() {
      this.showImportModal = true
      this.importData = ''
    },

    // 关闭导入对话框
    closeImportDialog() {
      this.showImportModal = false
      this.importData = ''
      this.exportData = ''
      this.isExportMode = false
    },

    // 显示导出对话框
    showExportDialog() {
      this.isExportMode = true
      this.exportData = this.generateExportData()
      this.showImportModal = true
    },

    // 生成导出数据
    generateExportData() {
      const exportData = []

      // 遍历所有单元格
      for (let row = 0; row < this.tableConfig.rows; row++) {
        for (let col = 0; col < this.tableConfig.columns; col++) {
          const cellKey = `${row}-${col}`
          const position = `${row + 1}-${col + 1}`

          // 检查是否是合并单元格的主单元格
          if (this.mergedCells[cellKey]) {
            const mergeInfo = this.mergedCells[cellKey]
            exportData.push([
              position,
              {
                colspan: mergeInfo.colspan,
                rowspan: mergeInfo.rowspan,
                value: this.cellData[cellKey] || ''
              }
            ])

            // 添加被合并的单元格（colspan=0或rowspan=0）
            for (let r = row; r < row + mergeInfo.rowspan; r++) {
              for (let c = col; c < col + mergeInfo.colspan; c++) {
                if (r !== row || c !== col) {
                  const mergedCellKey = `${r}-${c}`
                  const mergedPosition = `${r + 1}-${c + 1}`

                  // 被合并的单元格都设置为colspan=0, rowspan=0
                  const colspan = 0
                  const rowspan = 0

                  exportData.push([
                    mergedPosition,
                    {
                      colspan: colspan,
                      rowspan: rowspan,
                      value: ''
                    }
                  ])
                }
              }
            }
          } else if (!this.isMergedByOtherCell(row, col)) {
            // 普通单元格
            exportData.push([
              position,
              {
                colspan: 1,
                rowspan: 1,
                value: this.cellData[cellKey] || ''
              }
            ])
          }
        }
      }

      return JSON.stringify(exportData, null, 2)
    },

    // 复制导出数据
    async copyExportData() {
      try {
        await navigator.clipboard.writeText(this.exportData)
        this.showOperationResult('success', '导出数据已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        this.showOperationResult('error', '复制失败，请手动复制')
      }
    },

    // 导入表格数据
    importTableData() {
      try {
        if (!this.importData.trim()) {
          this.showOperationResult('error', '请输入要导入的数据')
          return
        }

        // 解析JSON数据
        const parsedData = JSON.parse(this.importData)

        if (!Array.isArray(parsedData)) {
          this.showOperationResult('error', '数据格式错误：必须是数组格式')
          return
        }

        // 验证数据格式
        const validationResult = this.validateImportData(parsedData)
        if (!validationResult.valid) {
          this.showOperationResult('error', validationResult.message)
          return
        }

        // 转换数据并更新表格
        this.convertImportData(parsedData)

        // 关闭对话框
        this.closeImportDialog()

        this.showOperationResult('success', '数据导入成功')

      } catch (error) {
        console.error('导入数据失败:', error)
        this.showOperationResult('error', '数据格式错误：' + error.message)
      }
    },

    // 验证导入数据格式
    validateImportData(data) {
      if (!Array.isArray(data)) {
        return { valid: false, message: '数据必须是数组格式' }
      }

      for (let i = 0; i < data.length; i++) {
        const item = data[i]
        if (!Array.isArray(item) || item.length !== 2) {
          return { valid: false, message: `第${i + 1}项格式错误：必须是[position, config]格式` }
        }

        const [position, config] = item

        // 验证位置格式
        if (typeof position !== 'string' || !/^\d+-\d+$/.test(position)) {
          return { valid: false, message: `第${i + 1}项位置格式错误：必须是"行-列"格式` }
        }

        // 验证配置对象
        if (typeof config !== 'object' || config === null) {
          return { valid: false, message: `第${i + 1}项配置错误：必须是对象格式` }
        }

        if (typeof config.colspan !== 'number' || config.colspan < 0) {
          return { valid: false, message: `第${i + 1}项colspan错误：必须是非负整数` }
        }

        if (typeof config.rowspan !== 'number' || config.rowspan < 0) {
          return { valid: false, message: `第${i + 1}项rowspan错误：必须是非负整数` }
        }
        if (config.colspan === 0 && config.rowspan === 0) {
          continue // 忽略被合并的单元格
        }

        if (typeof config.value === 'undefined') {
          config.value = '' // 设置为空字符串而不是返回错误
        }
      }

      return { valid: true }
    },

    // 转换导入数据并更新表格
    convertImportData(data) {
      // 清空现有单元格数据
      this.cellData = {}
      this.mergedCells = {}

      // 分析数据，确定需要的行列数
      let maxRow = 0
      let maxCol = 0

      data.forEach(([position, config]) => {
        const [rowStr, colStr] = position.split('-')
        const row = parseInt(rowStr) - 1 // 转换为0-based
        const col = parseInt(colStr) - 1 // 转换为0-based

        // 只有非合并单元格才计算行列数
        if (config.colspan > 0 && config.rowspan > 0) {
          maxRow = Math.max(maxRow, row + config.rowspan)
          maxCol = Math.max(maxCol, col + config.colspan)
        }
      })

      // 更新表格配置
      this.tableConfig.rows = Math.max(maxRow, 1)
      this.tableConfig.columns = Math.max(maxCol, 1)

      // 重新初始化行列配置
      this.initializeColumnConfigs()
      this.initializeRowConfigs()

      // 填充单元格数据和合并单元格信息
      data.forEach(([position, config]) => {
        const [rowStr, colStr] = position.split('-')
        const row = parseInt(rowStr) - 1 // 转换为0-based
        const col = parseInt(colStr) - 1 // 转换为0-based

        const cellKey = `${row}-${col}`

        // 存储单元格内容
        this.cellData[cellKey] = config.value || ''

        // 如果是合并单元格，存储合并信息
        if (config.colspan > 1 || config.rowspan > 1) {
          this.mergedCells[cellKey] = {
            colspan: config.colspan,
            rowspan: config.rowspan
          }
        }
      })

      // 更新预览
      this.updatePreview()
    },

    // 获取单元格样式
    getCellStyle(row, col) {
      const style = {
        width: this.columnConfigs[col] ? this.columnConfigs[col].width + 'px' : '120px',
        height: this.getRowHeight(row) + 'px'
      }

      // 如果是合并单元格的主单元格，设置特殊样式
      const cellKey = `${row}-${col}`
      if (this.mergedCells[cellKey]) {
        style.backgroundColor = 'rgba(0, 150, 255, 0.1)'
        style.border = '2px solid rgba(0, 150, 255, 0.5)'
      }

      return style
    },

    // 获取单元格colspan
    getCellColspan(row, col) {
      const cellKey = `${row}-${col}`
      if (this.mergedCells[cellKey]) {
        return this.mergedCells[cellKey].colspan
      }
      return 1
    },

    // 获取单元格rowspan
    getCellRowspan(row, col) {
      const cellKey = `${row}-${col}`
      if (this.mergedCells[cellKey]) {
        return this.mergedCells[cellKey].rowspan
      }
      return 1
    },

    // 获取单元格类名
    getCellClass(row, col) {
      const classes = ['preview-data-cell']

      // 检查是否被其他单元格合并
      if (this.isMergedByOtherCell(row, col)) {
        classes.push('merged-cell')
        classes.push('hidden-cell')
      }

      // 检查是否是合并单元格的主单元格
      const cellKey = `${row}-${col}`
      if (this.mergedCells[cellKey]) {
        classes.push('merge-master-cell')
      }

      // 检查是否被选中
      if (this.selectedCell.row === row && this.selectedCell.col === col) {
        classes.push('selected')
      }

      return classes
    },

    // 检查单元格是否被其他单元格合并
    isMergedByOtherCell(row, col) {
      for (const [cellKey, mergeInfo] of Object.entries(this.mergedCells)) {
        const [masterRow, masterCol] = cellKey.split('-').map(Number)
        const colspan = mergeInfo.colspan
        const rowspan = mergeInfo.rowspan

        // 检查当前单元格是否在合并范围内
        if (row >= masterRow && row < masterRow + rowspan &&
            col >= masterCol && col < masterCol + colspan &&
            !(row === masterRow && col === masterCol)) {
          return true
        }
      }
      return false
    },

    // 判断是否应该显示单元格
    shouldShowCell(row, col) {
      // 如果是被其他单元格合并的单元格，不显示
      return !this.isMergedByOtherCell(row, col)
    },

    // 获取单元格的有效宽度（考虑合并）
    getEffectiveCellWidth(row, col) {
      const cellKey = `${row}-${col}`
      if (this.mergedCells[cellKey]) {
        const colspan = this.mergedCells[cellKey].colspan
        let totalWidth = 0
        for (let i = 0; i < colspan; i++) {
          if (this.columnConfigs[col + i]) {
            totalWidth += this.columnConfigs[col + i].width
          }
        }
        return totalWidth
      }
      return this.columnConfigs[col] ? this.columnConfigs[col].width : 120
    },

    // 获取单元格的有效高度（考虑合并）
    getEffectiveCellHeight(row, col) {
      const cellKey = `${row}-${col}`
      if (this.mergedCells[cellKey]) {
        const rowspan = this.mergedCells[cellKey].rowspan
        let totalHeight = 0
        for (let i = 0; i < rowspan; i++) {
          totalHeight += this.getRowHeight(row + i)
        }
        return totalHeight
      }
      return this.getRowHeight(row)
    }
  }
}
</script>

<style scoped>
.nested-table-designer {
  padding: 20px;
  background: linear-gradient(180deg, #124B9A, #0D438D);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 动态背景效果 */
.nested-table-designer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 50%, rgba(18, 75, 154, 0.3) 0%, transparent 50%),
  radial-gradient(circle at 80% 80%, rgba(13, 67, 141, 0.3) 0%, transparent 50%),
  radial-gradient(circle at 40% 20%, rgba(0, 150, 255, 0.2) 0%, transparent 50%);
  animation: floatBackground 20s ease-in-out infinite;
  z-index: 1;
}

@keyframes floatBackground {
  0%, 100% { transform: translateX(0) translateY(0); }
  33% { transform: translateX(20px) translateY(-10px); }
  66% { transform: translateX(-10px) translateY(10px); }
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.page-header h1 {
  background: linear-gradient(135deg, #17d2ef 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.page-header p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.designer-content {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  position: relative;
  z-index: 2;
}

.design-panel {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-height: 80vh;
  overflow-y: auto;
}

.preview-panel {
  flex: 1.5;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.panel-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.panel-section:last-child {
  border-bottom: none;
}

.panel-section h3 {
  background: linear-gradient(135deg, #17d2ef 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 15px;
  font-size: 18px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.form-group {
  margin-bottom: 15px;
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-row .form-group {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.form-input, .form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  font-size: 14px;
  background: linear-gradient(0deg, #0D3A8D, #03256B);
  color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
}

.form-input:focus, .form-select:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(0, 150, 255, 0.3);
}

.form-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* 列宽配置样式 */
.column-width-config {
  max-height: 400px;
  overflow-y: auto;
}

.width-control-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(5px);
}

.width-control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.width-control-header label {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.width-value {
  font-weight: 600;
  color: #0096FF;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.width-control-body {
  display: flex;
  align-items: center;
  gap: 10px;
}

.width-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  text-align: center;
  font-size: 14px;
  min-width: 80px;
  background: linear-gradient(0deg, #0D3A8D, #03256B);
  color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
}

.width-input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(0, 150, 255, 0.3);
}

.adjust-btn {
  width: 32px;
  height: 32px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  background: linear-gradient(0deg, #0D3A8D, #03256B);
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
}

.adjust-btn:hover:not(:disabled) {
  background: linear-gradient(0deg, #0096FF, #043475);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.adjust-btn:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.4);
  cursor: not-allowed;
  border-color: rgba(255, 255, 255, 0.1);
}

.decrease-btn {
  color: #ff6b6b;
}

.decrease-btn:hover:not(:disabled) {
  border-color: #ff6b6b;
  color: #ff6b6b;
}

.increase-btn {
  color: #4ecdc4;
}

.increase-btn:hover:not(:disabled) {
  border-color: #4ecdc4;
  color: #4ecdc4;
}

/* 行高配置样式 */
.row-height-config {
  max-height: 400px;
  overflow-y: auto;
}

.height-control-item {
  margin-bottom: 15px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(5px);
}

.height-control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.height-control-header label {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.height-value {
  font-weight: 600;
  color: #4ecdc4;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.height-control-body {
  display: flex;
  align-items: center;
  gap: 10px;
}

.height-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  text-align: center;
  font-size: 14px;
  min-width: 80px;
  background: linear-gradient(0deg, #0D3A8D, #03256B);
  color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
}

.height-input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(0, 150, 255, 0.3);
}



.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-primary {
  background: linear-gradient(0deg, #0096FF, #043475);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary {
  background: linear-gradient(0deg, #6c757d, #495057);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-success {
  background: linear-gradient(0deg, #28a745, #1e7e34);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-info {
  background: linear-gradient(0deg, #17a2b8, #138496);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
}

.btn:active {
  transform: translateY(0);
}

.preview-section, .export-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.preview-section h3, .export-section h3 {
  background: linear-gradient(135deg, #17d2ef 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 15px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.table-preview-container {
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  overflow: auto;
  max-height: 400px;
  background: rgba(255, 255, 255, 0.05);
}

.preview-table {
  border-collapse: collapse;
  font-size: 14px;
  table-layout: fixed; /* 确保使用固定表格布局 */
}

.preview-header-cell, .preview-data-cell {
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px;
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
}

.preview-header-cell {
  background: linear-gradient(90deg, #0d438d 0%, rgba(13, 67, 141, 0.4) 100%);
  font-weight: 500;
  color: rgba(255, 255, 255, 0.95);
}

.preview-data-cell {
  cursor: pointer;
  transition: background-color 0.2s ease;
  padding: 0;
  position: relative;
  background: rgba(255, 255, 255, 0.05);
}

.preview-data-cell:hover {
  background: rgba(0, 150, 255, 0.2);
}

.preview-data-cell.selected {
  background: rgba(33, 150, 243, 0.3);
  border-color: rgba(33, 150, 243, 0.6);
}

/* 确保 CellEditor 在表格单元格中正确显示 */
.preview-data-cell .cell-editor-wrapper {
  width: 100%;
  height: 100%;
  min-height: inherit;
}

.preview-data-cell .cell-display {
  padding: 8px;
  min-height: inherit;
  box-sizing: border-box;
}

.preview-data-cell .cell-editor {
  padding: 8px;
  min-height: inherit;
  box-sizing: border-box;
}

/* 合并单元格样式 */
.preview-data-cell.merge-master-cell {
  background: rgba(0, 150, 255, 0.1);
  border: 2px solid rgba(0, 150, 255, 0.5);
  font-weight: 500;
}

.preview-data-cell.merged-cell.hidden-cell {
  display: none;
}

.preview-data-cell.merged-cell:not(.hidden-cell) {
  background: rgba(255, 255, 255, 0.05);
  border: 1px dashed rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.5);
}

.json-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  resize: vertical;
  background: linear-gradient(0deg, #0D3A8D, #03256B);
  color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
}

.json-textarea:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(0, 150, 255, 0.3);
}

.export-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  flex-wrap: wrap;
}

.operation-result {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
  backdrop-filter: blur(5px);
}

.operation-result.success {
  background: rgba(40, 167, 69, 0.2);
  color: #d4edda;
  border: 1px solid rgba(40, 167, 69, 0.4);
}

.operation-result.error {
  background: rgba(220, 53, 69, 0.2);
  color: #f8d7da;
  border: 1px solid rgba(220, 53, 69, 0.4);
}

/* 滚动条样式优化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(0deg, #70a7cb, #5a8db3);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(0deg, #5a8db3, #4a7ba3);
}

::-webkit-scrollbar-corner {
  background: rgba(255, 255, 255, 0.1);
}

/* 调整大小手柄优化 */
::-webkit-resizer {
  background: linear-gradient(135deg, #70a7cb, #5a8db3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0 0 4px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Firefox 滚动条支持 */
* {
  scrollbar-width: thin;
  scrollbar-color: #70a7cb rgba(255, 255, 255, 0.1);
}

@media (max-width: 1200px) {
  .designer-content {
    flex-direction: column;
  }

  .design-panel {
    max-height: none;
  }
}
</style>


/* Element UI Dialog 样式适配 */
.import-dialog-content {
  color: rgba(255, 255, 255, 0.9);
}

.import-dialog-content .form-group {
  margin-bottom: 20px;
}

.import-dialog-content .form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.import-dialog-content .format-help {
  margin-top: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.import-dialog-content .format-help h4 {
  background: linear-gradient(135deg, #17d2ef 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 10px 0;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.import-dialog-content .format-help ul {
  margin: 0;
  padding-left: 20px;
  color: rgba(255, 255, 255, 0.9);
}

.import-dialog-content .format-help li {
  margin-bottom: 5px;
  font-size: 14px;
}

/* 自定义 Element UI Dialog 样式以适配深色主题 */
::v-deep .el-dialog {
  background: linear-gradient(180deg, #124B9A, #0D438D) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px) !important;
}

::v-deep .el-dialog__header {
  background: transparent !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
  padding: 20px !important;
}

::v-deep .el-dialog__title {
  background: linear-gradient(135deg, #17d2ef 0%, #ffffff 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  color: transparent !important;
  font-size: 18px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

::v-deep .el-dialog__headerbtn .el-dialog__close {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 20px !important;
}

::v-deep .el-dialog__headerbtn:hover .el-dialog__close {
  color: rgba(255, 255, 255, 1) !important;
}

::v-deep .el-dialog__body {
  background: transparent !important;
  padding: 20px !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

::v-deep .el-dialog__footer {
  background: transparent !important;
  border-top: 1px solid rgba(255, 255, 255, 0.2) !important;
  padding: 20px !important;
}

::v-deep .el-textarea__inner {
  background: linear-gradient(0deg, #0D3A8D, #03256B) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(5px) !important;
  font-family: 'Courier New', monospace !important;
  resize: vertical !important;
}

::v-deep .el-textarea__inner:focus {
  border-color: rgba(255, 255, 255, 0.5) !important;
  box-shadow: 0 0 0 2px rgba(0, 150, 255, 0.3) !important;
}

::v-deep .el-textarea__inner::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}
