<template>
  <div class="word-designer">
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-row :gutter="10">
        <!-- 文件操作 -->
        <el-col :span="4">
          <el-button-group>
            <el-button size="small" icon="el-icon-document" @click="newDocument">新建</el-button>
            <el-button size="small" icon="el-icon-download" @click="exportDocument">导出</el-button>
          </el-button-group>
        </el-col>

        <!-- 字体设置 -->
        <el-col :span="12">
          <el-row :gutter="5">
            <el-col :span="8">
              <el-select
                v-model="fontFamily"
                size="small"
                placeholder="字体"
                @focus="saveSelection"
                @visible-change="onFontFamilySelectVisibleChange"
                @change="applyFontFamily">
                <el-option label="宋体" value="SimSun"></el-option>
                <el-option label="黑体" value="SimHei"></el-option>
                <el-option label="微软雅黑" value="Microsoft YaHei"></el-option>
                <el-option label="Arial" value="Arial"></el-option>
                <el-option label="Times New Roman" value="Times New Roman"></el-option>
              </el-select>
            </el-col>
            <el-col :span="4">
            </el-col>
            <el-col :span="3">
              <el-select
                v-model="fontSize"
                size="small"
                @change="selectFontSize"
                style="width: 80px;">
                <el-option label="8" value="8px"></el-option>
                <el-option label="9" value="9px"></el-option>
                <el-option label="10" value="10px"></el-option>
                <el-option label="11" value="11px"></el-option>
                <el-option label="12" value="12px"></el-option>
                <el-option label="14" value="14px"></el-option>
                <el-option label="16" value="16px"></el-option>
                <el-option label="18" value="18px"></el-option>
                <el-option label="20" value="20px"></el-option>
                <el-option label="22" value="22px"></el-option>
                <el-option label="24" value="24px"></el-option>
                <el-option label="26" value="26px"></el-option>
                <el-option label="28" value="28px"></el-option>
                <el-option label="30" value="30px"></el-option>
                <el-option label="32" value="32px"></el-option>
                <el-option label="36" value="36px"></el-option>
                <el-option label="42" value="42px"></el-option>
                <el-option label="48" value="48px"></el-option>
                <el-option label="54" value="54px"></el-option>
                <el-option label="60" value="60px"></el-option>
                <el-option label="72" value="72px"></el-option>
              </el-select>
            </el-col>
            <el-col :span="5">
              <el-color-picker
                v-model="fontColor"
                size="small"
                @focus="saveSelection"
                @active-change="saveSelection"
                @change="applyFontColor">
              </el-color-picker>
            </el-col>
            <el-col :span="4">
              <el-button-group>
                <el-button size="small" @mousedown="saveSelection" @click="decreaseFontSize" title="减小字体">A-</el-button>
                <el-button size="small" @mousedown="saveSelection" @click="increaseFontSize" title="增大字体">A+</el-button>
              </el-button-group>
            </el-col>
          </el-row>
        </el-col>

        <!-- 格式化按钮 -->
        <el-col :span="4">
          <el-button-group>
            <el-button size="small" :class="{'is-active': isBold}" @mousedown="saveSelection" @click="toggleBold">
              <strong>B</strong>
            </el-button>
            <el-button size="small" :class="{'is-active': isItalic}" @mousedown="saveSelection" @click="toggleItalic">
              <em>I</em>
            </el-button>
            <el-button size="small" :class="{'is-active': isUnderline}" @mousedown="saveSelection" @click="toggleUnderline">
              <u>U</u>
            </el-button>
            <el-button size="small" :class="{'is-active': isStrikethrough}" @mousedown="saveSelection" @click="toggleStrikethrough">
              <s>S</s>
            </el-button>
          </el-button-group>
        </el-col>

        <!-- 对齐方式 -->
        <el-col :span="4">
          <el-button-group>
            <el-button size="small" icon="el-icon-align-left" @mousedown="saveSelection" @click="setAlignment('left')"></el-button>
            <el-button size="small" icon="el-icon-align-center" @mousedown="saveSelection" @click="setAlignment('center')"></el-button>
            <el-button size="small" icon="el-icon-align-right" @mousedown="saveSelection" @click="setAlignment('right')"></el-button>
          </el-button-group>
        </el-col>
      </el-row>

      <!-- 第二行工具栏 -->
      <el-row :gutter="10" style="margin-top: 10px;">
        <!-- 缩进和间距 -->
        <el-col :span="6">
          <el-row :gutter="5">
            <el-col :span="8">
              <el-button size="small" icon="el-icon-back" @click="decreaseIndent" title="减少缩进"></el-button>
              <el-button size="small" icon="el-icon-right" @click="increaseIndent" title="增加缩进"></el-button>
            </el-col>
            <el-col :span="8">
              <el-input-number v-model="lineHeight" size="small" :min="1" :max="3" :step="0.1"
                               @change="applyLineHeight" placeholder="行间距"></el-input-number>
            </el-col>
            <el-col :span="8">
              <el-input-number v-model="paragraphSpacing" size="small" :min="0" :max="50"
                               @change="applyParagraphSpacing" placeholder="段落间距"></el-input-number>
            </el-col>
          </el-row>
        </el-col>

        <!-- 段落格式指示器 -->
        <el-col :span="2">
          <el-tag size="small" :type="currentParagraphHasCustomFont ? 'success' : 'info'">
            {{ currentParagraphHasCustomFont ? '已设置' : '默认' }}
          </el-tag>
        </el-col>

        <!-- 图片插入 -->
        <el-col :span="3">
          <el-button size="small" icon="el-icon-picture" @click="triggerImageUpload">插入图片</el-button>
          <input
            ref="imageInput"
            type="file"
            accept="image/*"
            style="display: none;"
            @change="handleNativeImageUpload">
        </el-col>

        <!-- 页面设置 -->
        <el-col :span="3">
          <el-button size="small" icon="el-icon-setting" @click="showPageSettings = true">页面设置</el-button>
        </el-col>

        <!-- 预览 -->
        <el-col :span="2">
          <el-button size="small" icon="el-icon-view" @click="previewDocument">预览</el-button>
        </el-col>

        <!-- JSON操作 -->
        <el-col :span="6">
          <el-button-group>
            <el-button size="small" icon="el-icon-document-copy" @click="copyJson" title="复制JSON">复制</el-button>
            <el-button size="small" icon="el-icon-upload2" @click="showImportDialog = true" title="导入JSON">导入</el-button>
            <el-button size="small" icon="el-icon-download" @click="exportJsonToFile" title="导出JSON文件">导出</el-button>
          </el-button-group>
        </el-col>

        <!-- 帮助 -->
        <el-col :span="1">
          <el-button size="small" icon="el-icon-question" @click="showHelp = true" title="快捷键帮助"></el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧JSON显示区域 -->
      <div class="json-panel">
        <div class="json-header">
          <h4>JSON数据结构</h4>
          <el-button-group size="mini">
            <el-button @click="formatJson" title="格式化JSON">格式化</el-button>
            <el-button @click="copyJson" title="复制JSON">复制</el-button>
            <el-button @click="toggleJsonPanel" title="隐藏/显示">{{ showJsonPanel ? '隐藏' : '显示' }}</el-button>
          </el-button-group>
        </div>
        <el-input
          v-show="showJsonPanel"
          v-model="jsonDisplay"
          type="textarea"
          :rows="25"
          readonly
          class="json-textarea"
          placeholder="JSON数据将在这里显示...">
        </el-input>

        <!-- 调试信息面板 -->
        <div v-show="showJsonPanel" class="debug-panel">
          <h5>调试信息</h5>
          <div class="debug-info">
            <p><strong>图片数量:</strong> {{ debugInfo.imageCount }}</p>
            <p><strong>最后操作:</strong> {{ debugInfo.lastAction }}</p>
            <p><strong>编辑器状态:</strong> {{ debugInfo.editorStatus }}</p>
          </div>
        </div>
      </div>

      <!-- 右侧编辑区域 -->
      <div class="editor-container">
        <!-- 标尺 -->
        <div class="ruler">
          <div class="ruler-numbers">
            <span v-for="i in 20" :key="i" :style="{left: (i * 40) + 'px'}">{{ i }}</span>
          </div>
        </div>

        <!-- 页面容器 -->
        <div class="page-container" :style="pageContainerStyle">
        <!-- 页眉 -->
        <div v-if="pageSettings.showHeader" class="page-header"
             :style="headerFooterStyle"
             contenteditable="true"
             @input="updateHeader">
          {{ pageSettings.headerText }}
        </div>

        <!-- 主要内容区域 -->
        <div
          ref="editor"
          class="editor-content"
          :style="editorStyle"
          contenteditable="true"
          @input="handleContentChange"
          @keyup="updateFormatState"
          @mouseup="updateFormatState"
          @paste="handlePaste"
          @keydown="handleKeyDown"
          @click="handleEditorClick">
          <p>在此输入您的文档内容...</p>
        </div>

        <!-- 页脚 -->
        <div v-if="pageSettings.showFooter" class="page-footer"
             :style="headerFooterStyle"
             contenteditable="true"
             @input="updateFooter">
          <span v-if="pageSettings.showPageNumber">第 {{ currentPage }} 页</span>
          {{ pageSettings.footerText }}
        </div>
      </div>
      </div> <!-- 关闭editor-container -->
    </div> <!-- 关闭main-content -->

    <!-- 页面设置对话框 -->
    <el-dialog title="页面设置" :visible.sync="showPageSettings" width="600px">
      <el-form :model="pageSettings" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="纸张大小">
              <el-select v-model="pageSettings.paperSize" @change="applyPaperSize">
                <el-option label="A4" value="A4"></el-option>
                <el-option label="A3" value="A3"></el-option>
                <el-option label="Letter" value="Letter"></el-option>
                <el-option label="自定义" value="Custom"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="页面方向">
              <el-radio-group v-model="pageSettings.orientation" @change="applyOrientation">
                <el-radio label="portrait">纵向</el-radio>
                <el-radio label="landscape">横向</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="上边距">
              <el-input-number v-model="pageSettings.marginTop" :min="0" :max="100" size="small"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下边距">
              <el-input-number v-model="pageSettings.marginBottom" :min="0" :max="100" size="small"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="左边距">
              <el-input-number v-model="pageSettings.marginLeft" :min="0" :max="100" size="small"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="右边距">
              <el-input-number v-model="pageSettings.marginRight" :min="0" :max="100" size="small"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="显示页眉">
              <el-switch v-model="pageSettings.showHeader"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="显示页脚">
              <el-switch v-model="pageSettings.showFooter"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="显示页码">
              <el-switch v-model="pageSettings.showPageNumber"></el-switch>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item v-if="pageSettings.showHeader" label="页眉内容">
          <el-input v-model="pageSettings.headerText" placeholder="请输入页眉内容"></el-input>
        </el-form-item>

        <el-form-item v-if="pageSettings.showFooter" label="页脚内容">
          <el-input v-model="pageSettings.footerText" placeholder="请输入页脚内容"></el-input>
        </el-form-item>

        <el-form-item label="页眉页脚字体">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-select v-model="pageSettings.headerFooterFont" size="small">
                <el-option label="宋体" value="SimSun"></el-option>
                <el-option label="黑体" value="SimHei"></el-option>
                <el-option label="微软雅黑" value="Microsoft YaHei"></el-option>
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-input-number v-model="pageSettings.headerFooterFontSize" size="small"></el-input-number>
            </el-col>
            <el-col :span="4">
              <el-color-picker v-model="pageSettings.headerFooterColor" size="small"></el-color-picker>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="showPageSettings = false">取消</el-button>
        <el-button type="primary" @click="applyPageSettings">确定</el-button>
      </div>
    </el-dialog>

    <!-- 帮助对话框 -->
    <el-dialog title="快捷键帮助" :visible.sync="showHelp" width="500px">
      <div class="help-content">
        <h4>文本格式化</h4>
        <ul>
          <li><kbd>Ctrl + B</kbd> - 加粗</li>
          <li><kbd>Ctrl + I</kbd> - 斜体</li>
          <li><kbd>Ctrl + U</kbd> - 下划线</li>
          <li><kbd>Ctrl + +</kbd> - 增大字体</li>
          <li><kbd>Ctrl + -</kbd> - 减小字体</li>
        </ul>

        <h4>文档操作</h4>
        <ul>
          <li><kbd>Ctrl + S</kbd> - 导出文档</li>
          <li><kbd>Ctrl + P</kbd> - 预览文档</li>
          <li><kbd>Ctrl + Z</kbd> - 撤销</li>
          <li><kbd>Ctrl + Y</kbd> - 重做</li>
        </ul>

        <h4>段落操作</h4>
        <ul>
          <li><kbd>Tab</kbd> - 增加缩进</li>
          <li><kbd>Shift + Tab</kbd> - 减少缩进</li>
        </ul>

        <h4>其他功能</h4>
        <ul>
          <li>支持拖拽图片调整位置</li>
          <li>自动保存功能（每2秒保存一次）</li>
          <li>支持富文本粘贴</li>
        </ul>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showHelp = false">知道了</el-button>
      </div>
    </el-dialog>

    <!-- JSON导入对话框 -->
    <el-dialog title="导入JSON数据" :visible.sync="showImportDialog" width="600px">
      <el-form>
        <el-form-item label="JSON数据">
          <el-input
            v-model="importJsonText"
            type="textarea"
            :rows="15"
            placeholder="请粘贴JSON数据..."
            class="json-import-textarea">
          </el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="showImportDialog = false">取消</el-button>
        <el-button @click="validateImportJson">验证JSON</el-button>
        <el-button type="primary" @click="importJsonData">导入</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'SimpleWordDesigner',
  data() {
    return {
      // 格式化状态
      fontFamily: 'Microsoft YaHei',
      fontSize: '14px',
      fontSizeNumber: 14,
      fontColor: '#000000',
      isBold: false,
      isItalic: false,
      isUnderline: false,
      isStrikethrough: false,
      lineHeight: 1.5,
      paragraphSpacing: 0,
      currentParagraphHasCustomFont: false,

      // 页面设置
      showPageSettings: false,
      showHelp: false,
      showImportDialog: false,
      importJsonText: '',
      currentPage: 1,
      pageSettings: {
        paperSize: 'A4',
        orientation: 'portrait',
        marginTop: 25,
        marginBottom: 25,
        marginLeft: 30,
        marginRight: 30,
        showHeader: false,
        headerText: '',
        showFooter: false,
        footerText: '',
        showPageNumber: true,
        headerFooterFont: 'Microsoft YaHei',
        headerFooterFontSize: 12,
        headerFooterColor: '#666666'
      },

      // 文档内容
      documentContent: '',

      // 自动保存定时器
      autoSaveTimer: null,

      // 图片缩放相关
      selectedImage: null,
      isResizing: false,
      resizeData: null,

      // 文本选择状态保存
      savedSelection: null,

      // JSON数据显示
      showJsonPanel: false,
      jsonDisplay: '',
      documentData: {
        title: '',
        content: [],
        pageSettings: {},
        metadata: {
          created: new Date().toISOString(),
          modified: new Date().toISOString(),
          version: '1.0'
        }
      },

      // 调试信息
      debugInfo: {
        imageCount: 0,
        lastAction: '无',
        editorStatus: '未初始化'
      },

      // 纸张尺寸配置
      paperSizes: {
        A4: { width: '210mm', height: '297mm' },
        A3: { width: '297mm', height: '420mm' },
        Letter: { width: '8.5in', height: '11in' }
      }
    }
  },
  computed: {
    pageContainerStyle() {
      const size = this.paperSizes[this.pageSettings.paperSize] || this.paperSizes.A4;
      const isLandscape = this.pageSettings.orientation === 'landscape';

      return {
        width: isLandscape ? size.height : size.width,
        height: isLandscape ? size.width : size.height,
        margin: '20px auto',
        padding: `${this.pageSettings.marginTop}mm ${this.pageSettings.marginRight}mm ${this.pageSettings.marginBottom}mm ${this.pageSettings.marginLeft}mm`,
        backgroundColor: '#ffffff',
        boxShadow: '0 0 10px rgba(0,0,0,0.1)',
        minHeight: '800px'
      }
    },
    editorStyle() {
      return {
        fontFamily: this.fontFamily,
        fontSize: this.fontSize,
        color: this.fontColor,
        lineHeight: this.lineHeight,
        marginBottom: this.paragraphSpacing + 'px',
        minHeight: '600px',
        outline: 'none',
        border: 'none'
      }
    },
    headerFooterStyle() {
      return {
        fontFamily: this.pageSettings.headerFooterFont,
        fontSize: this.pageSettings.headerFooterFontSize + 'px',
        color: this.pageSettings.headerFooterColor,
        textAlign: 'center',
        padding: '10px 0',
        borderBottom: this.pageSettings.showHeader ? '1px solid #eee' : 'none',
        borderTop: this.pageSettings.showFooter ? '1px solid #eee' : 'none'
      }
    }
  },
  mounted() {
    this.initEditor();

    // 初始化JSON数据
    this.initJsonData();

    // 恢复自动保存的内容
    this.$nextTick(() => {
      this.restoreAutoSave();
    });
  },

  beforeDestroy() {
    // 清理定时器
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer);
    }
  },
  methods: {
    initEditor() {
      // 初始化编辑器
      this.$nextTick(() => {
        if (this.$refs.editor) {
          this.$refs.editor.focus();
          // 初始化所有段落的默认字体大小
          this.initializeParagraphFontSizes();
          // 更新调试信息
          this.updateDebugInfo('编辑器初始化完成');
        }
      });
    },

    // 初始化所有段落的默认字体大小
    initializeParagraphFontSizes() {
      const paragraphs = this.$refs.editor.querySelectorAll('p, div, h1, h2, h3, h4, h5, h6');
      paragraphs.forEach(paragraph => {
        if (!paragraph.style.fontSize) {
          paragraph.style.fontSize = '14px'; // 设置默认字体大小
        }
      });
    },

    initJsonData() {
      // 初始化JSON数据结构
      this.documentData = {
        title: '简单Word文档',
        content: [],
        pageSettings: { ...this.pageSettings },
        metadata: {
          created: new Date().toISOString(),
          modified: new Date().toISOString(),
          version: '1.0',
          author: 'Word Designer',
          description: '使用简单Word设计器创建的文档'
        }
      };

      // 初始解析内容
      this.$nextTick(() => {
        this.parseContentToJson();
      });
    },

    // 文档操作
    newDocument() {
      if (confirm('确定要新建文档吗？当前内容将丢失。')) {
        this.$refs.editor.innerHTML = '<p style="font-size: 14px;">在此输入您的文档内容...</p>';
        this.documentContent = '';

        // 重新初始化JSON数据
        this.$nextTick(() => {
          this.parseContentToJson();
        });
      }
    },

    exportDocument() {
      console.log('🚀 [导出] 开始导出文档');

      // 确保JSON数据是最新的
      this.parseContentToJson();

      // 只使用JSON结构化数据，不再发送HTML内容
      const jsonContent = this.documentData.content;

      console.log('📊 [导出] JSON内容节点数:', jsonContent.length);
      console.log('🖼️ [导出] 检查图片数据...');

      // 检查并记录图片信息
      const images = this.getImagesFromJsonContent(jsonContent);
      console.log('🖼️ [导出] 发现图片数量:', images.length);

      images.forEach((img, index) => {
        console.log(`🖼️ [导出] 图片${index + 1}:`, {
          src: img.attributes?.src ? img.attributes.src.substring(0, 50) + '...' : '无src',
          width: img.styles?.width || '未设置',
          height: img.styles?.height || '未设置',
          base64Size: img.attributes?.src ? this.getBase64Size(img.attributes.src) : '未知'
        });
      });

      const documentData = {
        // 只发送JSON结构化数据
        jsonContent: jsonContent,
        // 页面设置
        pageSettings: this.pageSettings,
        // 文档元数据
        title: this.documentData.title || '简单Word文档',
        metadata: this.documentData.metadata,
        // 导出配置
        exportConfig: {
          includeImages: true,
          imageFormat: 'base64',
          preserveLayout: true,
          maxImageWidth: 600,
          maxImageHeight: 800
        }
      };

      console.log('📦 [导出] 准备发送的数据:', {
        jsonNodes: documentData.jsonContent.length,
        imageCount: images.length,
        pageSettings: Object.keys(documentData.pageSettings).length,
        totalDataSize: this.calculateDataSize(documentData)
      });

      // 调用导出接口
      this.callExportAPI(documentData);
    },

    // 从JSON内容中递归获取所有图片节点
    getImagesFromJsonContent(jsonContent) {
      const images = [];

      const findImages = (nodes) => {
        if (!Array.isArray(nodes)) return;

        nodes.forEach(node => {
          if (node.type === 'img') {
            images.push(node);
          }

          // 递归查找子节点中的图片
          if (node.children && Array.isArray(node.children)) {
            findImages(node.children);
          }
        });
      };

      findImages(jsonContent);
      return images;
    },

    // 计算Base64数据大小
    getBase64Size(base64String) {
      if (!base64String || !base64String.includes(',')) {
        return '0KB';
      }

      const base64Data = base64String.split(',')[1];
      const sizeInBytes = (base64Data.length * 3) / 4;

      if (sizeInBytes < 1024) {
        return Math.round(sizeInBytes) + 'B';
      } else if (sizeInBytes < 1024 * 1024) {
        return Math.round(sizeInBytes / 1024) + 'KB';
      } else {
        return Math.round(sizeInBytes / (1024 * 1024)) + 'MB';
      }
    },

    // 计算整个文档数据的大小
    calculateDataSize(documentData) {
      try {
        const jsonString = JSON.stringify(documentData);
        const sizeInBytes = new Blob([jsonString]).size;

        if (sizeInBytes < 1024) {
          return Math.round(sizeInBytes) + 'B';
        } else if (sizeInBytes < 1024 * 1024) {
          return Math.round(sizeInBytes / 1024) + 'KB';
        } else {
          return Math.round(sizeInBytes / (1024 * 1024)) + 'MB';
        }
      } catch (error) {
        console.warn('计算数据大小失败:', error);
        return '未知';
      }
    },

    // 验证JSON内容结构
    validateJsonContent(jsonContent) {
      try {
        console.log('🔍 [验证] 开始验证JSON内容结构');

        if (!Array.isArray(jsonContent)) {
          return { success: false, message: 'JSON内容必须是数组格式' };
        }

        let nodeCount = 0;
        let imageCount = 0;
        let textNodeCount = 0;

        const validateNode = (node, path = '') => {
          nodeCount++;

          // 验证必需字段
          if (!node.type) {
            throw new Error(`节点缺少type字段: ${path}`);
          }

          // 统计不同类型的节点
          if (node.type === 'img') {
            imageCount++;

            // 验证图片节点
            if (!node.attributes || !node.attributes.src) {
              throw new Error(`图片节点缺少src属性: ${path}`);
            }

            if (!node.attributes.src.startsWith('data:image/')) {
              throw new Error(`图片节点src格式不正确: ${path}`);
            }
          } else if (node.content && node.content.trim()) {
            textNodeCount++;
          }

          // 递归验证子节点
          if (node.children && Array.isArray(node.children)) {
            node.children.forEach((child, index) => {
              validateNode(child, `${path}.children[${index}]`);
            });
          }
        };

        // 验证所有节点
        jsonContent.forEach((node, index) => {
          validateNode(node, `root[${index}]`);
        });

        console.log('🔍 [验证] JSON内容验证通过:', {
          totalNodes: nodeCount,
          imageNodes: imageCount,
          textNodes: textNodeCount
        });

        return {
          success: true,
          stats: {
            totalNodes: nodeCount,
            imageNodes: imageCount,
            textNodes: textNodeCount
          }
        };

      } catch (error) {
        console.error('🔍 [验证] JSON内容验证失败:', error);
        return { success: false, message: `JSON内容格式错误: ${error.message}` };
      }
    },

    async callExportAPI(documentData) {
      try {
        this.$message.info('正在导出文档...');

        // 验证导出请求
        const validation = await this.validateExportRequest(documentData);
        if (!validation.success) {
          this.$message.error(validation.message);
          return;
        }

        // 调用导出API
        const { exportAndDownloadSimpleWord } = await import('@/api/word/simpleWordDesigner');
        const result = await exportAndDownloadSimpleWord(documentData);

        this.$message.success(`文档导出成功！文件大小: ${this.formatFileSize(result.size)}`);
        console.log('导出成功:', result);

      } catch (error) {
        console.error('导出失败:', error);
        let errorMessage = '文档导出失败';

        if (error.response) {
          // 服务器返回错误
          if (error.response.status === 400) {
            errorMessage = '请求参数错误';
          } else if (error.response.status === 500) {
            errorMessage = '服务器内部错误';
          } else {
            errorMessage = `导出失败 (${error.response.status})`;
          }
        } else if (error.message) {
          errorMessage += ': ' + error.message;
        }

        this.$message.error(errorMessage);
      }
    },

    async validateExportRequest(documentData) {
      try {
        console.log('🔍 [验证] 开始验证导出请求');

        // 基本验证
        if (!documentData.title || documentData.title.trim() === '') {
          return { success: false, message: '文档标题不能为空' };
        }

        // 验证JSON内容
        const hasJsonContent = documentData.jsonContent && documentData.jsonContent.length > 0;

        if (!hasJsonContent) {
          return { success: false, message: '文档内容不能为空' };
        }

        console.log('🔍 [验证] 内容检查:', {
          hasJsonContent,
          jsonNodes: documentData.jsonContent.length,
          hasPageSettings: !!documentData.pageSettings,
          hasExportConfig: !!documentData.exportConfig
        });

        // 验证JSON内容结构
        const validationResult = this.validateJsonContent(documentData.jsonContent);
        if (!validationResult.success) {
          return validationResult;
        }

        // 调用后端验证API
        const { validateExportRequest } = await import('@/api/word/simpleWordDesigner');
        const response = await validateExportRequest(documentData);

        if (response.code === 200) {
          return { success: true };
        } else {
          return { success: false, message: response.msg || '验证失败' };
        }

      } catch (error) {
        console.warn('验证请求失败，跳过验证:', error);
        return { success: true }; // 验证失败时允许继续导出
      }
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    previewDocument() {
      // 打开预览窗口
      const content = this.$refs.editor.innerHTML;
      const previewWindow = window.open('', '_blank');
      previewWindow.document.write(`
        <html>
          <head>
            <title>文档预览</title>
            <style>
              body {
                font-family: ${this.fontFamily};
                font-size: ${this.fontSize};
                color: ${this.fontColor};
                line-height: ${this.lineHeight};
                margin: ${this.pageSettings.marginTop}mm ${this.pageSettings.marginRight}mm ${this.pageSettings.marginBottom}mm ${this.pageSettings.marginLeft}mm;
              }
              @media print {
                body { margin: 0; }
              }
            </style>
          </head>
          <body>
            ${this.pageSettings.showHeader ? '<div style="text-align: center; border-bottom: 1px solid #eee; padding-bottom: 10px; margin-bottom: 20px;">' + this.pageSettings.headerText + '</div>' : ''}
            ${content}
            ${this.pageSettings.showFooter ? '<div style="text-align: center; border-top: 1px solid #eee; padding-top: 10px; margin-top: 20px;">' + this.pageSettings.footerText + (this.pageSettings.showPageNumber ? ' 第 ' + this.currentPage + ' 页' : '') + '</div>' : ''}
          </body>
        </html>
      `);
    },

    // 字体格式化
    applyFontFamily() {
      // 尝试恢复保存的选择状态
      this.restoreSelection();

      document.execCommand('fontName', false, this.fontFamily);

      // 清除保存的选择状态
      this.clearSavedSelection();
      this.focusEditor();

      // 触发JSON更新
      this.$nextTick(() => {
        this.handleContentChange({ target: this.$refs.editor });
      });
    },

    onFontSizeNumberChange(value) {
      if (value && value >= 8 && value <= 72) {
        this.fontSize = value + 'px';
        this.applyFontSize();
      }
    },

    onFontSizeSelectVisibleChange(visible) {
      if (visible) {
        // 下拉框打开时保存选择状态
        this.saveSelection();
      }
    },

    onFontFamilySelectVisibleChange(visible) {
      if (visible) {
        // 下拉框打开时保存选择状态
        this.saveSelection();
      }
    },

    applyFontSize() {
      // 首先尝试恢复保存的选择状态
      const hasRestoredSelection = this.restoreSelection();

      const selection = window.getSelection();

      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);

        if (!range.collapsed || hasRestoredSelection) {
          // 有选中文本（当前或恢复的），只对选中文本应用字体大小
          this.applyFontSizeToSelection(range);
        } else {
          // 没有选中文本，对当前行应用字体大小
          this.applyFontSizeToCurrentLine();
        }
      }

      // 清除保存的选择状态
      this.clearSavedSelection();
      this.focusEditor();
    },

    applyFontSizeToSelection(range) {
      // 处理选中文本的字体大小
      const contents = range.extractContents();
      const span = document.createElement('span');
      span.style.fontSize = this.fontSize;

      // 处理提取的内容
      if (contents.childNodes.length === 1 && contents.firstChild.nodeType === Node.TEXT_NODE) {
        // 纯文本节点，直接包装
        span.appendChild(contents);
      } else {
        // 复杂内容，递归处理每个节点
        this.processNodesForFontSize(contents, this.fontSize);
        span.appendChild(contents);
      }

      range.insertNode(span);

      // 重新选中应用了样式的内容
      range.selectNode(span);
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);

      // 强制触发内容变化事件，确保JSON更新
      this.$nextTick(() => {
        this.handleContentChange({ target: this.$refs.editor });
      });
    },

    applyFontSizeToCurrentLine() {
      // 对当前光标所在段落应用字体大小
      const selection = window.getSelection();
      if (selection.rangeCount === 0) return;

      const range = selection.getRangeAt(0);
      let element = range.startContainer;

      // 如果是文本节点，获取其父元素
      if (element.nodeType === Node.TEXT_NODE) {
        element = element.parentElement;
      }

      // 找到段落元素
      while (element && element !== this.$refs.editor &&
             !['P', 'DIV', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6'].includes(element.tagName)) {
        element = element.parentElement;
      }

      if (element && element !== this.$refs.editor) {
        // 直接在段落元素上设置字体大小
        element.style.fontSize = this.fontSize;

        // 强制触发内容变化事件，确保JSON更新
        this.$nextTick(() => {
          this.handleContentChange({ target: this.$refs.editor });
        });

      } else {
        // 如果没找到段落元素，创建一个新段落
        const paragraph = document.createElement('p');
        paragraph.style.fontSize = this.fontSize;
        paragraph.innerHTML = '&nbsp;';

        range.insertNode(paragraph);

        // 将光标移到段落内部
        range.selectNodeContents(paragraph);
        range.collapse(false);
        selection.removeAllRanges();
        selection.addRange(range);

        // 强制触发内容变化事件
        this.$nextTick(() => {
          this.handleContentChange({ target: this.$refs.editor });
        });
      }
    },

    processNodesForFontSize(container, fontSize) {
      // 递归处理容器中的所有节点
      const childNodes = Array.from(container.childNodes);

      childNodes.forEach(node => {
        if (node.nodeType === Node.TEXT_NODE) {
          // 文本节点，用span包装
          if (node.textContent.trim()) {
            const span = document.createElement('span');
            span.style.fontSize = fontSize;
            span.textContent = node.textContent;
            container.replaceChild(span, node);
          }
        } else if (node.nodeType === Node.ELEMENT_NODE) {
          // 元素节点，设置字体大小并递归处理子节点
          node.style.fontSize = fontSize;
          if (node.childNodes.length > 0) {
            this.processNodesForFontSize(node, fontSize);
          }
        }
      });
    },

    applyFontColor() {
      // 尝试恢复保存的选择状态
      this.restoreSelection();

      document.execCommand('foreColor', false, this.fontColor);

      // 清除保存的选择状态
      this.clearSavedSelection();
      this.focusEditor();

      // 触发JSON更新
      this.$nextTick(() => {
        this.handleContentChange({ target: this.$refs.editor });
      });
    },

    applyLineHeight() {
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        let element = range.commonAncestorContainer;
        if (element.nodeType === Node.TEXT_NODE) {
          element = element.parentElement;
        }

        // 找到段落元素
        while (element && element.tagName !== 'P' && element.tagName !== 'DIV') {
          element = element.parentElement;
        }

        if (element) {
          element.style.lineHeight = this.lineHeight;
        }
      }
      this.focusEditor();

      // 触发JSON更新
      this.$nextTick(() => {
        this.handleContentChange({ target: this.$refs.editor });
      });
    },

    applyParagraphSpacing() {
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        let element = range.commonAncestorContainer;
        if (element.nodeType === Node.TEXT_NODE) {
          element = element.parentElement;
        }

        // 找到段落元素
        while (element && element.tagName !== 'P' && element.tagName !== 'DIV') {
          element = element.parentElement;
        }

        if (element) {
          element.style.marginBottom = this.paragraphSpacing + 'px';
        }
      }
      this.focusEditor();

      // 触发JSON更新
      this.$nextTick(() => {
        this.handleContentChange({ target: this.$refs.editor });
      });
    },

    // 格式化按钮
    toggleBold() {
      this.restoreSelection();
      document.execCommand('bold');
      this.clearSavedSelection();
      this.updateFormatState();
      this.focusEditor();

      // 触发JSON更新
      this.$nextTick(() => {
        this.handleContentChange({ target: this.$refs.editor });
      });
    },

    toggleItalic() {
      this.restoreSelection();
      document.execCommand('italic');
      this.clearSavedSelection();
      this.updateFormatState();
      this.focusEditor();

      // 触发JSON更新
      this.$nextTick(() => {
        this.handleContentChange({ target: this.$refs.editor });
      });
    },

    toggleUnderline() {
      this.restoreSelection();
      document.execCommand('underline');
      this.clearSavedSelection();
      this.updateFormatState();
      this.focusEditor();

      // 触发JSON更新
      this.$nextTick(() => {
        this.handleContentChange({ target: this.$refs.editor });
      });
    },

    toggleStrikethrough() {
      this.restoreSelection();
      document.execCommand('strikeThrough');
      this.clearSavedSelection();
      this.updateFormatState();
      this.focusEditor();

      // 触发JSON更新
      this.$nextTick(() => {
        this.handleContentChange({ target: this.$refs.editor });
      });
    },

    // 对齐方式
    setAlignment(align) {
      this.restoreSelection();
      const commands = {
        left: 'justifyLeft',
        center: 'justifyCenter',
        right: 'justifyRight'
      };
      document.execCommand(commands[align]);
      this.clearSavedSelection();
      this.focusEditor();

      // 触发JSON更新
      this.$nextTick(() => {
        this.handleContentChange({ target: this.$refs.editor });
      });
    },

    // 缩进
    increaseIndent() {
      document.execCommand('indent');
      this.focusEditor();

      // 触发JSON更新
      this.$nextTick(() => {
        this.handleContentChange({ target: this.$refs.editor });
      });
    },

    decreaseIndent() {
      document.execCommand('outdent');
      this.focusEditor();

      // 触发JSON更新
      this.$nextTick(() => {
        this.handleContentChange({ target: this.$refs.editor });
      });
    },

    // 更新格式状态
    updateFormatState() {
      this.isBold = document.queryCommandState('bold');
      this.isItalic = document.queryCommandState('italic');
      this.isUnderline = document.queryCommandState('underline');
      this.isStrikethrough = document.queryCommandState('strikeThrough');

      // 更新字体大小显示
      this.updateFontSizeDisplay();

      // 更新段落格式状态
      this.updateParagraphFormatState();
    },

    updateFontSizeDisplay() {
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        let element = range.commonAncestorContainer;

        // 如果是文本节点，获取其父元素
        if (element.nodeType === Node.TEXT_NODE) {
          element = element.parentElement;
        }

        // 获取计算后的字体大小
        if (element && element.nodeType === Node.ELEMENT_NODE) {
          const computedStyle = window.getComputedStyle(element);
          const currentFontSize = computedStyle.fontSize;

          if (currentFontSize) {
            const sizeValue = parseInt(currentFontSize);
            if (sizeValue && sizeValue !== this.fontSizeNumber) {
              this.fontSizeNumber = sizeValue;
              this.fontSize = sizeValue + 'px';
            }
          }
        }
      }
    },

    updateParagraphFormatState() {
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        let element = range.commonAncestorContainer;

        // 如果是文本节点，获取其父元素
        if (element.nodeType === Node.TEXT_NODE) {
          element = element.parentElement;
        }

        // 找到段落元素
        while (element && element !== this.$refs.editor &&
               !['P', 'DIV', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6'].includes(element.tagName)) {
          element = element.parentElement;
        }

        // 检查段落是否有自定义字体大小
        if (element && element !== this.$refs.editor) {
          const hasCustomFontSize = element.style.fontSize ||
                                   element.querySelector('[style*="font-size"]') ||
                                   element.querySelector('span[style*="font-size"]');
          this.currentParagraphHasCustomFont = !!hasCustomFontSize;
        } else {
          this.currentParagraphHasCustomFont = false;
        }
      }
    },

    focusEditor() {
      this.$nextTick(() => {
        if (this.$refs.editor) {
          this.$refs.editor.focus();
        }
      });
    },

    // 保存当前选择状态
    saveSelection() {
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        this.savedSelection = {
          range: selection.getRangeAt(0).cloneRange(),
          hasSelection: !selection.getRangeAt(0).collapsed
        };
      } else {
        this.savedSelection = null;
      }
    },

    // 恢复选择状态
    restoreSelection() {
      if (this.savedSelection && this.savedSelection.range) {
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(this.savedSelection.range);
        return this.savedSelection.hasSelection;
      }
      return false;
    },

    // 清除保存的选择状态
    clearSavedSelection() {
      this.savedSelection = null;
    },

    // 图片处理

    processImageFile(file) {
      console.log('🖼️ [图片处理] 开始处理:', file.name);

      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        console.error('❌ [图片处理] 文件类型错误:', file.type);
        this.$message.error('请选择图片文件');
        return false;
      }

      // 验证文件大小（限制为5MB）
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        console.error('❌ [图片处理] 文件过大:', (file.size / 1024 / 1024).toFixed(1) + 'MB');
        this.$message.error('图片文件不能超过5MB');
        return false;
      }

      console.log('✅ [图片处理] 文件验证通过，开始读取');

      const reader = new FileReader();

      reader.onload = (e) => {
        console.log('📖 [图片处理] 读取完成，Base64长度:', e.target.result.length);
        this.insertImage(e.target.result);
      };

      reader.onerror = (e) => {
        console.error('❌ [图片处理] 读取失败:', e);
        this.$message.error('图片读取失败');
      };

      reader.readAsDataURL(file);
    },

    // 图片上传处理
    triggerImageUpload() {
      console.log('🖼️ [图片上传] 触发文件选择');
      this.$refs.imageInput.click();
    },

    handleNativeImageUpload(event) {
      console.log('🖼️ [图片上传] 文件选择事件触发');
      const files = event.target.files;

      if (files && files.length > 0) {
        const file = files[0];
        console.log('📁 [图片上传] 选择文件:', {
          name: file.name,
          size: (file.size / 1024).toFixed(1) + 'KB',
          type: file.type
        });

        this.processImageFile(file);

        // 清空input，允许重复选择同一文件
        event.target.value = '';
      } else {
        console.warn('⚠️ [图片上传] 没有选择文件');
      }
    },

    insertImage(src) {
      console.log('🖼️ [图片插入] 开始插入图片');

      try {
        // 创建图片元素
        const img = document.createElement('img');

        // 设置图片属性
        img.src = src;
        img.style.maxWidth = '100%';
        img.style.height = 'auto';
        img.style.cursor = 'move';
        img.style.display = 'block';
        img.style.margin = '10px 0';
        img.draggable = true;
        img.alt = '插入的图片';

        // 添加图片加载事件监听
        img.onload = () => {
          console.log('✅ [图片插入] 图片加载成功，原始尺寸:', img.naturalWidth + 'x' + img.naturalHeight);

          // 设置图片的默认显示尺寸（基于原始尺寸和最大宽度限制）
          const maxWidth = 600; // 最大宽度限制
          let displayWidth = img.naturalWidth;
          let displayHeight = img.naturalHeight;

          // 如果图片宽度超过最大限制，按比例缩放
          if (displayWidth > maxWidth) {
            const ratio = maxWidth / displayWidth;
            displayWidth = maxWidth;
            displayHeight = Math.round(displayHeight * ratio);
          }

          // 设置图片的实际显示尺寸到style中
          img.style.width = displayWidth + 'px';
          img.style.height = displayHeight + 'px';

          console.log('📏 [图片插入] 设置显示尺寸:', displayWidth + 'x' + displayHeight);

          // 触发JSON更新
          this.$nextTick(() => {
            this.handleContentChange({ target: this.$refs.editor });
          });
        };

        img.onerror = (e) => {
          console.error('❌ [图片插入] 图片加载失败:', e);
          this.$message.error('图片加载失败');
        };

        // 添加图片事件监听
        img.addEventListener('click', this.selectImage);
        img.addEventListener('dragstart', this.handleImageDragStart);
        img.addEventListener('dragend', this.handleImageDragEnd);

        // 检查编辑器是否存在
        if (!this.$refs.editor) {
          console.error('❌ [图片插入] 编辑器引用不存在');
          this.$message.error('编辑器未初始化');
          return;
        }

        // 智能插入图片，支持内联和块级两种模式
        this.insertImageIntelligently(img);

        this.focusEditor();
        console.log('✅ [图片插入] 插入完成，当前图片数量:', this.$refs.editor.querySelectorAll('img').length);

        // 更新调试信息
        this.updateDebugInfo('图片插入成功');

        // 显示成功消息
        this.$message.success('图片插入成功');

      } catch (error) {
        console.error('❌ [图片插入] 插入失败:', error);
        this.updateDebugInfo('图片插入失败: ' + error.message);
        this.$message.error('图片插入失败: ' + error.message);
      }
    },

    selectImage(event) {
      // 清除其他选中的图片
      const images = this.$refs.editor.querySelectorAll('img');
      images.forEach(img => img.classList.remove('selected'));

      // 选中当前图片
      event.target.classList.add('selected');

      // 添加调整大小的控制点
      this.addResizeHandles(event.target);
    },

    addResizeHandles(img) {
      // 移除现有的控制点
      this.removeResizeHandles();

      // 存储当前选中的图片
      this.selectedImage = img;

      // 创建四角的等比例缩放控制点
      const cornerHandles = ['nw', 'ne', 'sw', 'se'];
      cornerHandles.forEach(position => {
        const handle = document.createElement('div');
        handle.className = `resize-handle resize-${position} corner-handle`;
        handle.style.cssText = `
          position: absolute;
          width: 10px;
          height: 10px;
          background: #409EFF;
          border: 2px solid #fff;
          border-radius: 50%;
          cursor: ${position}-resize;
          z-index: 1000;
        `;

        // 设置位置
        this.updateHandlePosition(handle, img, position);

        // 添加鼠标事件
        handle.addEventListener('mousedown', (e) => this.startResize(e, position, img));

        this.$refs.editor.appendChild(handle);
      });

      // 创建四边的不等比缩放控制点
      const edgeHandles = ['n', 'e', 's', 'w'];
      edgeHandles.forEach(position => {
        const handle = document.createElement('div');
        handle.className = `resize-handle resize-${position} edge-handle`;
        handle.style.cssText = `
          position: absolute;
          width: 8px;
          height: 8px;
          background: #67C23A;
          border: 2px solid #fff;
          border-radius: 50%;
          cursor: ${this.getEdgeCursor(position)};
          z-index: 1000;
        `;

        // 设置位置
        this.updateHandlePosition(handle, img, position);

        // 添加鼠标事件
        handle.addEventListener('mousedown', (e) => this.startResize(e, position, img));

        this.$refs.editor.appendChild(handle);
      });

      // 添加图片周围的边框
      img.style.border = '2px dashed #409EFF';
    },

    // 获取边缘控制点的光标样式
    getEdgeCursor(position) {
      const cursors = {
        'n': 'n-resize',    // 上边：垂直调整
        's': 's-resize',    // 下边：垂直调整
        'e': 'e-resize',    // 右边：水平调整
        'w': 'w-resize'     // 左边：水平调整
      };
      return cursors[position] || 'default';
    },

    updateHandlePosition(handle, img, position) {
      const rect = img.getBoundingClientRect();
      const containerRect = this.$refs.editor.getBoundingClientRect();
      const scrollTop = this.$refs.editor.scrollTop || 0;
      const scrollLeft = this.$refs.editor.scrollLeft || 0;

      // 四角控制点（等比例缩放）
      switch(position) {
        case 'nw':
          handle.style.top = (rect.top - containerRect.top + scrollTop - 5) + 'px';
          handle.style.left = (rect.left - containerRect.left + scrollLeft - 5) + 'px';
          break;
        case 'ne':
          handle.style.top = (rect.top - containerRect.top + scrollTop - 5) + 'px';
          handle.style.left = (rect.right - containerRect.left + scrollLeft - 5) + 'px';
          break;
        case 'sw':
          handle.style.top = (rect.bottom - containerRect.top + scrollTop - 5) + 'px';
          handle.style.left = (rect.left - containerRect.left + scrollLeft - 5) + 'px';
          break;
        case 'se':
          handle.style.top = (rect.bottom - containerRect.top + scrollTop - 5) + 'px';
          handle.style.left = (rect.right - containerRect.left + scrollLeft - 5) + 'px';
          break;

        // 四边控制点（不等比缩放）
        case 'n': // 上边中点
          handle.style.top = (rect.top - containerRect.top + scrollTop - 4) + 'px';
          handle.style.left = (rect.left + rect.width/2 - containerRect.left + scrollLeft - 4) + 'px';
          break;
        case 's': // 下边中点
          handle.style.top = (rect.bottom - containerRect.top + scrollTop - 4) + 'px';
          handle.style.left = (rect.left + rect.width/2 - containerRect.left + scrollLeft - 4) + 'px';
          break;
        case 'e': // 右边中点
          handle.style.top = (rect.top + rect.height/2 - containerRect.top + scrollTop - 4) + 'px';
          handle.style.left = (rect.right - containerRect.left + scrollLeft - 4) + 'px';
          break;
        case 'w': // 左边中点
          handle.style.top = (rect.top + rect.height/2 - containerRect.top + scrollTop - 4) + 'px';
          handle.style.left = (rect.left - containerRect.left + scrollLeft - 4) + 'px';
          break;
      }
    },

    startResize(event, position, img) {
      event.preventDefault();
      event.stopPropagation();

      this.isResizing = true;
      this.resizeData = {
        startX: event.clientX,
        startY: event.clientY,
        startWidth: img.offsetWidth,
        startHeight: img.offsetHeight,
        position: position,
        img: img
      };

      // 添加缩放样式
      img.classList.add('resizing');

      // 添加全局鼠标事件
      document.addEventListener('mousemove', this.handleResize);
      document.addEventListener('mouseup', this.stopResize);

      // 防止文本选择
      document.body.style.userSelect = 'none';
    },

    handleResize(event) {
      if (!this.isResizing || !this.resizeData) return;

      const { startX, startY, startWidth, startHeight, position, img } = this.resizeData;
      const deltaX = event.clientX - startX;
      const deltaY = event.clientY - startY;

      // 计算原始宽高比
      const aspectRatio = startWidth / startHeight;

      let newWidth = startWidth;
      let newHeight = startHeight;

      // 判断是四角缩放（等比例）还是四边缩放（不等比例）
      const isCornerResize = ['nw', 'ne', 'sw', 'se'].includes(position);
      const isEdgeResize = ['n', 's', 'e', 'w'].includes(position);

      if (isCornerResize) {
        // 四角缩放：保持等比例
        switch(position) {
          case 'se': // 右下角
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
              newWidth = startWidth + deltaX;
              newHeight = newWidth / aspectRatio;
            } else {
              newHeight = startHeight + deltaY;
              newWidth = newHeight * aspectRatio;
            }
            break;
          case 'sw': // 左下角
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
              newWidth = startWidth - deltaX;
              newHeight = newWidth / aspectRatio;
            } else {
              newHeight = startHeight + deltaY;
              newWidth = newHeight * aspectRatio;
            }
            break;
          case 'ne': // 右上角
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
              newWidth = startWidth + deltaX;
              newHeight = newWidth / aspectRatio;
            } else {
              newHeight = startHeight - deltaY;
              newWidth = newHeight * aspectRatio;
            }
            break;
          case 'nw': // 左上角
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
              newWidth = startWidth - deltaX;
              newHeight = newWidth / aspectRatio;
            } else {
              newHeight = startHeight - deltaY;
              newWidth = newHeight * aspectRatio;
            }
            break;
        }
      } else if (isEdgeResize) {
        // 四边缩放：不等比例，只改变一个维度
        switch(position) {
          case 'n': // 上边：只改变高度
            newHeight = startHeight - deltaY;
            newWidth = startWidth; // 宽度不变
            break;
          case 's': // 下边：只改变高度
            newHeight = startHeight + deltaY;
            newWidth = startWidth; // 宽度不变
            break;
          case 'e': // 右边：只改变宽度
            newWidth = startWidth + deltaX;
            newHeight = startHeight; // 高度不变
            break;
          case 'w': // 左边：只改变宽度
            newWidth = startWidth - deltaX;
            newHeight = startHeight; // 高度不变
            break;
        }
      }

      // 限制最小和最大尺寸
      const minSize = 20;
      const editorWidth = this.$refs.editor.offsetWidth - 40;
      const editorHeight = this.$refs.editor.offsetHeight - 40;

      if (isCornerResize) {
        // 四角缩放：限制尺寸时保持宽高比
        if (newWidth < minSize) {
          newWidth = minSize;
          newHeight = newWidth / aspectRatio;
        }
        if (newHeight < minSize) {
          newHeight = minSize;
          newWidth = newHeight * aspectRatio;
        }
        if (newWidth > editorWidth) {
          newWidth = editorWidth;
          newHeight = newWidth / aspectRatio;
        }
        if (newHeight > editorHeight) {
          newHeight = editorHeight;
          newWidth = newHeight * aspectRatio;
        }
      } else if (isEdgeResize) {
        // 四边缩放：独立限制宽度和高度
        newWidth = Math.max(minSize, Math.min(newWidth, editorWidth));
        newHeight = Math.max(minSize, Math.min(newHeight, editorHeight));
      }

      // 应用新尺寸
      img.style.width = newWidth + 'px';
      img.style.height = newHeight + 'px';

      // 更新控制点位置
      this.updateAllHandlePositions(img);
    },

    stopResize() {
      if (this.resizeData && this.resizeData.img) {
        // 移除缩放样式
        this.resizeData.img.classList.remove('resizing');
      }

      this.isResizing = false;
      this.resizeData = null;

      // 移除全局事件监听
      document.removeEventListener('mousemove', this.handleResize);
      document.removeEventListener('mouseup', this.stopResize);

      // 恢复文本选择
      document.body.style.userSelect = '';

      // 触发内容变化事件
      this.handleContentChange({ target: this.$refs.editor });
    },

    updateAllHandlePositions(img) {
      const handles = this.$refs.editor.querySelectorAll('.resize-handle');
      handles.forEach(handle => {
        const position = handle.className.split(' ')[1].replace('resize-', '');
        this.updateHandlePosition(handle, img, position);
      });
    },

    removeResizeHandles() {
      const handles = this.$refs.editor.querySelectorAll('.resize-handle');
      handles.forEach(handle => handle.remove());

      // 移除图片边框
      if (this.selectedImage) {
        this.selectedImage.style.border = '';
        this.selectedImage = null;
      }

      // 移除所有图片的选中状态
      const images = this.$refs.editor.querySelectorAll('img');
      images.forEach(img => {
        img.classList.remove('selected');
        img.style.border = '';
      });
    },

    handleImageDragStart(event) {
      event.dataTransfer.effectAllowed = 'move';
      event.dataTransfer.setData('text/html', event.target.outerHTML);
    },

    handleImageDragEnd(event) {
      // 拖拽结束后的处理
    },

    // 编辑器点击处理
    handleEditorClick(event) {
      // 如果点击的不是图片，则取消图片选择
      if (event.target.tagName !== 'IMG' && !event.target.classList.contains('resize-handle')) {
        this.removeResizeHandles();
      }
      this.updateFormatState();
    },

    // 内容处理
    handleContentChange(event) {
      this.documentContent = event.target.innerHTML;

      // 确保新添加的段落有默认字体大小
      this.ensureParagraphFontSizes();

      // 解析内容并更新JSON数据
      this.parseContentToJson();

      // 自动保存到本地存储
      this.autoSave();
    },

    // 确保所有段落都有字体大小设置
    ensureParagraphFontSizes() {
      const paragraphs = this.$refs.editor.querySelectorAll('p, div, h1, h2, h3, h4, h5, h6');
      paragraphs.forEach(paragraph => {
        if (!paragraph.style.fontSize) {
          paragraph.style.fontSize = '14px'; // 设置默认字体大小
        }
      });
    },

    handlePaste(event) {
      // 处理粘贴事件，支持富文本和纯文本
      event.preventDefault();

      // 尝试获取HTML内容
      let htmlData = event.clipboardData.getData('text/html');
      let textData = event.clipboardData.getData('text/plain');

      if (htmlData) {
        // 清理HTML内容，移除危险标签
        htmlData = this.sanitizeHtml(htmlData);
        document.execCommand('insertHTML', false, htmlData);
      } else if (textData) {
        // 插入纯文本
        document.execCommand('insertText', false, textData);
      }

      this.updateFormatState();
    },

    sanitizeHtml(html) {
      // 创建临时DOM元素来清理HTML
      const temp = document.createElement('div');
      temp.innerHTML = html;

      // 移除script标签
      const scripts = temp.querySelectorAll('script');
      scripts.forEach(script => script.remove());

      // 移除危险的事件属性
      const dangerousAttrs = ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur'];
      const allElements = temp.querySelectorAll('*');
      allElements.forEach(element => {
        dangerousAttrs.forEach(attr => {
          if (element.hasAttribute(attr)) {
            element.removeAttribute(attr);
          }
        });
      });

      return temp.innerHTML;
    },

    // 自动保存功能
    autoSave() {
      if (this.autoSaveTimer) {
        clearTimeout(this.autoSaveTimer);
      }

      this.autoSaveTimer = setTimeout(() => {
        try {
          const saveData = {
            content: this.documentContent,
            pageSettings: this.pageSettings,
            timestamp: new Date().toISOString()
          };

          localStorage.setItem('simpleWordDesigner_autoSave', JSON.stringify(saveData));
          console.log('文档已自动保存');
        } catch (error) {
          console.warn('自动保存失败:', error);
        }
      }, 2000); // 2秒后保存
    },

    // 恢复自动保存的内容
    restoreAutoSave() {
      try {
        const savedData = localStorage.getItem('simpleWordDesigner_autoSave');
        if (savedData) {
          const data = JSON.parse(savedData);

          // 询问用户是否恢复
          this.$confirm('检测到未保存的文档内容，是否恢复？', '恢复文档', {
            confirmButtonText: '恢复',
            cancelButtonText: '忽略',
            type: 'info'
          }).then(() => {
            if (data.content) {
              this.$refs.editor.innerHTML = data.content;
              this.documentContent = data.content;
            }
            if (data.pageSettings) {
              this.pageSettings = { ...this.pageSettings, ...data.pageSettings };
            }
            this.$message.success('文档内容已恢复');
          }).catch(() => {
            // 用户选择忽略，清除自动保存数据
            localStorage.removeItem('simpleWordDesigner_autoSave');
          });
        }
      } catch (error) {
        console.warn('恢复自动保存失败:', error);
      }
    },

    // 清除自动保存
    clearAutoSave() {
      localStorage.removeItem('simpleWordDesigner_autoSave');
    },

    // 键盘快捷键处理
    handleKeyDown(event) {
      // Ctrl/Cmd + 组合键
      if (event.ctrlKey || event.metaKey) {
        switch (event.key.toLowerCase()) {
          case 'b':
            // Ctrl+B 加粗
            event.preventDefault();
            this.toggleBold();
            break;
          case 'i':
            // Ctrl+I 斜体
            event.preventDefault();
            this.toggleItalic();
            break;
          case 'u':
            // Ctrl+U 下划线
            event.preventDefault();
            this.toggleUnderline();
            break;
          case 's':
            // Ctrl+S 保存（导出）
            event.preventDefault();
            this.exportDocument();
            break;
          case 'p':
            // Ctrl+P 预览
            event.preventDefault();
            this.previewDocument();
            break;
          case 'z':
            // Ctrl+Z 撤销（浏览器默认行为）
            // 不阻止默认行为，让浏览器处理
            break;
          case 'y':
            // Ctrl+Y 重做（浏览器默认行为）
            // 不阻止默认行为，让浏览器处理
            break;
          case '=':
          case '+':
            // Ctrl+= 或 Ctrl++ 增大字体
            event.preventDefault();
            this.increaseFontSize();
            break;
          case '-':
            // Ctrl+- 减小字体
            event.preventDefault();
            this.decreaseFontSize();
            break;
        }
      }

      // 其他快捷键
      switch (event.key) {
        case 'Tab':
          // Tab键增加缩进
          if (!event.shiftKey) {
            event.preventDefault();
            this.increaseIndent();
          } else {
            // Shift+Tab减少缩进
            event.preventDefault();
            this.decreaseIndent();
          }
          break;
        case 'Enter':
          // 回车键创建新段落时，确保有默认字体大小
          setTimeout(() => {
            this.ensureParagraphFontSizes();
          }, 10);
          break;
      }
    },

    // 字体大小调整方法
    increaseFontSize() {
      // 保存选择状态已经在mousedown事件中处理
      const currentSize = parseInt(this.fontSize);
      const fontSizes = [8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 36, 42, 48, 54, 60, 72];
      const currentIndex = fontSizes.indexOf(currentSize);

      if (currentIndex < fontSizes.length - 1) {
        const newSize = fontSizes[currentIndex + 1];
        this.fontSize = newSize + 'px';
        this.fontSizeNumber = newSize;
        this.applyFontSize();
      }
    },

    selectFontSize(value) {
      console.log('font size -> ',value)
      const newSize = parseInt(value);
      this.fontSize = newSize + 'px';
      this.fontSizeNumber = newSize;
      this.applyFontSize();
    },

    decreaseFontSize() {
      // 保存选择状态已经在mousedown事件中处理
      const currentSize = parseInt(this.fontSize);
      const fontSizes = [8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 36, 42, 48, 54, 60, 72];
      const currentIndex = fontSizes.indexOf(currentSize);

      if (currentIndex > 0) {
        const newSize = fontSizes[currentIndex - 1];
        this.fontSize = newSize + 'px';
        this.fontSizeNumber = newSize;
        this.applyFontSize();
      }
    },

    // 页面设置
    applyPageSettings() {
      this.showPageSettings = false;
      this.$message.success('页面设置已应用');
    },

    applyPaperSize() {
      // 纸张大小改变时的处理
    },

    applyOrientation() {
      // 页面方向改变时的处理
    },

    updateHeader(event) {
      this.pageSettings.headerText = event.target.textContent;
    },

    updateFooter(event) {
      this.pageSettings.footerText = event.target.textContent;
    },

    // JSON数据处理方法
    parseContentToJson() {
      try {
        // 更新文档数据
        this.documentData.modified = new Date().toISOString();
        this.documentData.pageSettings = { ...this.pageSettings };
        this.documentData.content = this.parseHtmlToStructuredData();

        // 更新JSON显示
        this.updateJsonDisplay();
      } catch (error) {
        console.error('解析内容到JSON时出错:', error);
      }
    },

    parseHtmlToStructuredData() {
      if (!this.$refs.editor) return [];

      const content = [];
      const children = Array.from(this.$refs.editor.children);

      children.forEach((element, index) => {
        const nodeData = this.parseElementToJson(element, index);
        if (nodeData) {
          content.push(nodeData);
        }
      });

      return content;
    },

    // 检查段落是否包含内联图片
    hasInlineImages(element) {
      return Array.from(element.children).some(child =>
        child.tagName === 'IMG' && child.style.display === 'inline-block'
      );
    },

    // 分解包含内联图片的段落为独立节点序列
    decomposeParagraphWithInlineImages(element, baseIndex) {
      console.log('🔄 [段落分解] 开始分解包含内联图片的段落');

      const nodes = [];
      let currentTextContent = '';
      let nodeIndex = 0;

      // 遍历段落的所有子节点
      for (let i = 0; i < element.childNodes.length; i++) {
        const node = element.childNodes[i];

        if (node.nodeType === Node.TEXT_NODE) {
          // 文本节点，累积文本内容
          currentTextContent += node.textContent;
        } else if (node.nodeType === Node.ELEMENT_NODE) {
          if (node.tagName === 'IMG' && node.style.display === 'inline-block') {
            // 遇到内联图片，先处理之前累积的文本
            if (currentTextContent.trim()) {
              const textNode = this.createTextNode(currentTextContent.trim(), element, baseIndex, nodeIndex++);
              nodes.push(textNode);
              console.log('🔄 [段落分解] 添加文本节点:', currentTextContent.trim().substring(0, 30));
              currentTextContent = '';
            }

            // 处理内联图片，转换为块级图片
            const imgNode = this.createImageNodeFromInline(node, baseIndex, nodeIndex++);
            nodes.push(imgNode);
            console.log('🔄 [段落分解] 添加图片节点');
          } else {
            // 其他内联元素，添加到文本内容中
            currentTextContent += node.textContent || '';
          }
        }
      }

      // 处理剩余的文本内容
      if (currentTextContent.trim()) {
        const textNode = this.createTextNode(currentTextContent.trim(), element, baseIndex, nodeIndex++);
        nodes.push(textNode);
        console.log('🔄 [段落分解] 添加最后的文本节点:', currentTextContent.trim().substring(0, 30));
      }

      console.log('🔄 [段落分解] 分解完成，生成节点数:', nodes.length);
      return nodes;
    },

    // 创建文本节点
    createTextNode(textContent, originalElement, baseIndex, nodeIndex) {
      return {
        id: `text_${baseIndex}_${nodeIndex}_${Date.now()}`,
        type: 'p',
        content: textContent,
        styles: this.extractElementStyles(originalElement),
        attributes: {},
        children: []
      };
    },

    // 从内联图片创建块级图片节点
    createImageNodeFromInline(imgElement, baseIndex, nodeIndex) {
      const nodeData = {
        id: `img_${baseIndex}_${nodeIndex}_${Date.now()}`,
        type: 'img',
        content: '',
        styles: {},
        attributes: {},
        children: []
      };

      // 复制图片的样式，但转换为块级显示
      if (imgElement.style.cssText) {
        const styles = {};
        imgElement.style.cssText.split(';').forEach(style => {
          if (style.trim()) {
            const [property, value] = style.split(':').map(s => s.trim());
            if (property && value) {
              styles[this.camelCase(property)] = value;
            }
          }
        });

        // 转换内联样式为块级样式
        if (styles.maxHeight) {
          // 将maxHeight转换为实际的height和width
          const maxHeightValue = styles.maxHeight;
          if (maxHeightValue.endsWith('em')) {
            const emValue = parseFloat(maxHeightValue.replace('em', ''));
            const pxValue = Math.round(emValue * 16); // 1em ≈ 16px
            styles.height = pxValue + 'px';
            styles.width = pxValue + 'px'; // 保持正方形
          } else {
            styles.height = maxHeightValue;
            styles.width = maxHeightValue;
          }
          delete styles.maxHeight;
        }
        styles.display = 'block';
        styles.margin = '10px 0';
        delete styles.verticalAlign;

        nodeData.styles = styles;
      }

      // 复制属性
      Array.from(imgElement.attributes).forEach(attr => {
        if (attr.name !== 'style') {
          nodeData.attributes[attr.name] = attr.value;
        }
      });

      // 确保图片尺寸信息
      this.ensureImageDimensions(imgElement, nodeData);

      return nodeData;
    },

    // 提取元素样式
    extractElementStyles(element) {
      const styles = {};
      if (element.style.cssText) {
        element.style.cssText.split(';').forEach(style => {
          if (style.trim()) {
            const [property, value] = style.split(':').map(s => s.trim());
            if (property && value) {
              styles[this.camelCase(property)] = value;
            }
          }
        });
      }
      return styles;
    },

    parseElementToJson(element, index) {
      console.log('🔍 [JSON解析] 解析元素:', element.tagName, '索引:', index);

      const nodeData = {
        id: `node_${index}_${Date.now()}`,
        type: element.tagName.toLowerCase(),
        content: '',
        styles: {},
        attributes: {},
        children: []
      };

      // 解析样式
      if (element.style.cssText) {
        const styles = {};
        element.style.cssText.split(';').forEach(style => {
          if (style.trim()) {
            const [property, value] = style.split(':').map(s => s.trim());
            if (property && value) {
              styles[this.camelCase(property)] = value;
            }
          }
        });
        nodeData.styles = styles;
        console.log('🎨 [JSON解析] 解析样式:', styles);
      }

      // 解析属性
      Array.from(element.attributes).forEach(attr => {
        if (attr.name !== 'style') {
          nodeData.attributes[attr.name] = attr.value;
        }
      });

      if (Object.keys(nodeData.attributes).length > 0) {
        console.log('📋 [JSON解析] 解析属性:', nodeData.attributes);
      }

      // 特殊处理图片元素
      if (element.tagName.toLowerCase() === 'img') {
        console.log('🖼️ [JSON解析] 处理图片元素');

        // 确保图片的宽高信息被正确记录
        this.ensureImageDimensions(element, nodeData);

        // 图片元素不需要解析子元素，直接返回
        return nodeData;
      }

      // 解析内容 - 区分纯文本节点和混合内容节点
      const hasChildElements = element.children.length > 0;
      const hasTextContent = element.textContent && element.textContent.trim();

      console.log('📝 [JSON解析] 内容分析:', {
        hasChildElements,
        hasTextContent,
        childrenCount: element.children.length,
        textContent: element.textContent ? element.textContent.substring(0, 50) + '...' : '无'
      });

      if (!hasChildElements && hasTextContent) {
        // 纯文本节点
        nodeData.content = element.textContent;
        console.log('📝 [JSON解析] 纯文本节点:', nodeData.content);
      } else if (hasChildElements) {
        // 有子元素的节点，正常处理
        Array.from(element.children).forEach((child, childIndex) => {
          const childData = this.parseElementToJson(child, childIndex);
          if (childData) {
            nodeData.children.push(childData);
            console.log('👶 [JSON解析] 添加子节点:', childData.type, childData.id);
          }
        });

        // 检查是否有直接的文本内容（混合内容）
        const directText = this.getDirectTextContent(element);
        if (directText.trim()) {
          nodeData.content = directText;
          console.log('📝 [JSON解析] 混合内容的直接文本:', directText.trim());
        }
      }

      console.log('✅ [JSON解析] 节点解析完成:', {
        type: nodeData.type,
        id: nodeData.id,
        hasContent: !!nodeData.content,
        childrenCount: nodeData.children.length,
        stylesCount: Object.keys(nodeData.styles).length
      });

      return nodeData;
    },

    // 确保图片的宽高信息被正确记录到JSON中
    ensureImageDimensions(imgElement, nodeData) {
      console.log('📏 [图片尺寸] 开始处理图片尺寸信息');

      // 获取图片的当前显示尺寸
      const computedStyle = window.getComputedStyle(imgElement);
      const currentWidth = imgElement.offsetWidth;
      const currentHeight = imgElement.offsetHeight;
      const styleWidth = imgElement.style.width;
      const styleHeight = imgElement.style.height;

      console.log('📏 [图片尺寸] 当前尺寸信息:', {
        offsetWidth: currentWidth,
        offsetHeight: currentHeight,
        styleWidth: styleWidth,
        styleHeight: styleHeight,
        naturalWidth: imgElement.naturalWidth,
        naturalHeight: imgElement.naturalHeight
      });

      // 确保样式对象存在
      if (!nodeData.styles) {
        nodeData.styles = {};
      }

      // 如果图片有明确的style尺寸设置，优先使用
      if (styleWidth && styleWidth !== 'auto') {
        nodeData.styles.width = styleWidth;
        console.log('📏 [图片尺寸] 使用style宽度:', styleWidth);
      } else if (currentWidth > 0) {
        // 否则使用当前显示宽度
        nodeData.styles.width = currentWidth + 'px';
        console.log('📏 [图片尺寸] 使用显示宽度:', currentWidth + 'px');
      }

      if (styleHeight && styleHeight !== 'auto') {
        nodeData.styles.height = styleHeight;
        console.log('📏 [图片尺寸] 使用style高度:', styleHeight);
      } else if (currentHeight > 0) {
        // 否则使用当前显示高度
        nodeData.styles.height = currentHeight + 'px';
        console.log('📏 [图片尺寸] 使用显示高度:', currentHeight + 'px');
      }

      // 记录原始尺寸信息（用于后端参考）
      if (imgElement.naturalWidth > 0 && imgElement.naturalHeight > 0) {
        nodeData.attributes.naturalWidth = imgElement.naturalWidth.toString();
        nodeData.attributes.naturalHeight = imgElement.naturalHeight.toString();
        console.log('📏 [图片尺寸] 记录原始尺寸:', imgElement.naturalWidth + 'x' + imgElement.naturalHeight);
      }

      // 记录宽高比（用于后端等比例缩放）
      if (imgElement.naturalWidth > 0 && imgElement.naturalHeight > 0) {
        const aspectRatio = (imgElement.naturalWidth / imgElement.naturalHeight).toFixed(4);
        nodeData.attributes.aspectRatio = aspectRatio;
        console.log('📏 [图片尺寸] 计算宽高比:', aspectRatio);
      }

      // 记录文本位置信息（用于内联图片的正确定位）
      const textPosition = imgElement.getAttribute('data-text-position');
      if (textPosition) {
        nodeData.attributes.textPosition = textPosition;
        console.log('📍 [图片位置] 记录文本位置:', textPosition);
      }

      // 记录显示模式（内联或块级）
      const display = imgElement.style.display;
      if (display === 'inline-block') {
        nodeData.attributes.displayMode = 'inline';
        nodeData.attributes.isInlineImage = 'true'; // 明确标识为内联图片
        console.log('📍 [图片位置] 记录显示模式: 内联');
      } else {
        nodeData.attributes.displayMode = 'block';
        console.log('📍 [图片位置] 记录显示模式: 块级');
      }

      console.log('📏 [图片尺寸] 最终尺寸信息:', {
        width: nodeData.styles.width,
        height: nodeData.styles.height,
        naturalWidth: nodeData.attributes.naturalWidth,
        naturalHeight: nodeData.attributes.naturalHeight,
        aspectRatio: nodeData.attributes.aspectRatio,
        textPosition: nodeData.attributes.textPosition,
        displayMode: nodeData.attributes.displayMode
      });
    },

    // 解析包含内联图片的段落内容序列
    parseInlineContentSequence(element, nodeData) {
      console.log('🔄 [内联解析] 开始解析内联内容序列');

      // 创建一个内容序列数组，按DOM顺序记录文本和图片
      const contentSequence = [];

      // 遍历段落的所有子节点（包括文本节点和元素节点）
      for (let i = 0; i < element.childNodes.length; i++) {
        const node = element.childNodes[i];

        if (node.nodeType === Node.TEXT_NODE) {
          // 文本节点
          const text = node.textContent;
          if (text && text.trim()) {
            contentSequence.push({
              type: 'text',
              content: text,
              index: i
            });
            console.log('🔄 [内联解析] 添加文本片段:', text.substring(0, 30));
          }
        } else if (node.nodeType === Node.ELEMENT_NODE) {
          // 元素节点
          if (node.tagName === 'IMG' && node.style.display === 'inline-block') {
            // 内联图片
            const imgData = this.parseElementToJson(node, i);
            contentSequence.push({
              type: 'img',
              data: imgData,
              index: i
            });
            console.log('🔄 [内联解析] 添加内联图片');
          } else {
            // 其他内联元素
            const elementData = this.parseElementToJson(node, i);
            contentSequence.push({
              type: 'element',
              data: elementData,
              index: i
            });
            console.log('🔄 [内联解析] 添加内联元素:', node.tagName);
          }
        }
      }

      console.log('🔄 [内联解析] 内容序列长度:', contentSequence.length);

      // 将序列信息存储到节点数据中
      nodeData.inlineSequence = contentSequence;

      // 为了兼容性，仍然保留原有的content和children结构
      // 但添加序列信息供后端使用
      const textParts = contentSequence.filter(item => item.type === 'text').map(item => item.content);
      const childElements = contentSequence.filter(item => item.type !== 'text').map(item => item.data);

      if (textParts.length > 0) {
        nodeData.content = textParts.join(''); // 合并所有文本
      }

      if (childElements.length > 0) {
        nodeData.children = childElements;
      }

      console.log('🔄 [内联解析] 解析完成 - 文本片段:', textParts.length, '子元素:', childElements.length);
    },

    // 智能插入图片，支持内联和块级两种模式
    insertImageIntelligently(img) {
      console.log('🎯 [图片插入] 开始智能插入图片');

      const selection = window.getSelection();

      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const container = range.commonAncestorContainer;

        // 判断插入模式
        const insertMode = this.determineInsertMode(range, container);
        console.log('🎯 [图片插入] 插入模式:', insertMode);

        if (insertMode === 'inline') {
          this.insertImageInline(img, range);
        } else {
          this.insertImageAsBlockElement(img);
        }
      } else {
        // 没有选择范围，默认作为块级元素插入
        this.$refs.editor.appendChild(img);
        console.log('✅ [图片插入] 图片追加到编辑器末尾');
      }
    },

    // 判断图片插入模式
    determineInsertMode(range, container) {
      // 获取光标所在的块级父元素
      let blockParent = container.nodeType === Node.TEXT_NODE ? container.parentNode : container;
      while (blockParent && blockParent !== this.$refs.editor) {
        if (this.isBlockElement(blockParent)) {
          break;
        }
        blockParent = blockParent.parentNode;
      }

      if (!blockParent || blockParent === this.$refs.editor) {
        return 'block'; // 没有找到块级父元素，作为块级插入
      }

      // 检查光标位置
      const position = this.getCursorPositionInBlock(range, blockParent);
      const blockText = blockParent.textContent || '';

      console.log('🎯 [插入模式判断] 块级元素:', blockParent.nodeName, '位置:', position, '文本长度:', blockText.length);

      // 判断规则：
      // 1. 如果段落很短（< 20字符），且光标在中间，倾向于内联插入
      // 2. 如果光标在段落开头或结尾，倾向于块级插入
      // 3. 如果段落较长，且光标在中间，倾向于内联插入

      if (position === 'start' || position === 'end') {
        return 'block'; // 开头或结尾，块级插入
      } else if (position === 'middle') {
        if (blockText.length < 50) {
          return 'inline'; // 短段落中间，内联插入
        } else {
          return 'inline'; // 长段落中间，也倾向于内联插入
        }
      }

      return 'block'; // 默认块级插入
    },

    // 内联插入图片
    insertImageInline(img, range) {
      console.log('🎯 [图片插入] 内联模式插入图片');

      // 记录插入位置信息，用于后端正确处理
      const insertPosition = this.calculateInsertPosition(range);
      console.log('📍 [图片插入] 计算插入位置:', insertPosition);

      // 设置内联图片的样式
      img.style.display = 'inline-block';
      img.style.verticalAlign = 'middle';
      img.style.margin = '0 4px';
      img.style.maxHeight = '1.5em'; // 限制高度，与文字行高匹配
      img.style.width = 'auto';

      // 添加位置信息到图片的data属性中
      img.setAttribute('data-text-position', insertPosition.toString());

      // 直接在光标位置插入
      try {
        range.insertNode(img);
        range.collapse(false);
        console.log('✅ [图片插入] 内联图片插入成功，位置:', insertPosition);

        // 设置光标到图片后面
        const newRange = document.createRange();
        newRange.setStartAfter(img);
        newRange.collapse(true);
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(newRange);

      } catch (error) {
        console.error('❌ [图片插入] 内联插入失败:', error);
        // 降级到块级插入
        this.insertImageAsBlockElement(img);
      }
    },

    // 计算图片在文本中的插入位置
    calculateInsertPosition(range) {
      try {
        const container = range.startContainer;
        const offset = range.startOffset;

        console.log('📍 [位置计算] 开始计算位置 - 容器类型:', container.nodeType, '偏移量:', offset);

        // 找到段落元素
        let paragraph = container;
        while (paragraph && paragraph.nodeType !== Node.ELEMENT_NODE || paragraph.tagName !== 'P') {
          paragraph = paragraph.parentNode;
        }

        if (!paragraph) {
          console.log('📍 [位置计算] 未找到段落元素，使用默认位置');
          return 0;
        }

        // 计算光标在段落中的文本位置
        let textPosition = 0;
        const walker = document.createTreeWalker(
          paragraph,
          NodeFilter.SHOW_TEXT,
          null,
          false
        );

        let currentNode;
        while (currentNode = walker.nextNode()) {
          if (currentNode === container) {
            // 找到光标所在的文本节点
            textPosition += offset;
            break;
          } else {
            // 累加之前文本节点的长度
            textPosition += currentNode.textContent.length;
          }
        }

        console.log('📍 [位置计算] 计算完成 - 段落总文本:', paragraph.textContent.length, '光标位置:', textPosition);
        return textPosition;

      } catch (error) {
        console.error('❌ [位置计算] 计算插入位置失败:', error);
        return 0; // 默认位置
      }
    },

    // 智能插入图片作为独立的块级元素
    insertImageAsBlockElement(img) {
      console.log('🎯 [图片插入] 开始智能插入图片');

      const selection = window.getSelection();

      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const container = range.commonAncestorContainer;

        console.log('🎯 [图片插入] 光标位置信息:', {
          containerType: container.nodeType,
          containerName: container.nodeName,
          parentName: container.parentNode ? container.parentNode.nodeName : 'none'
        });

        // 找到最近的块级父元素
        let blockParent = container.nodeType === Node.TEXT_NODE ? container.parentNode : container;
        while (blockParent && blockParent !== this.$refs.editor) {
          if (this.isBlockElement(blockParent)) {
            break;
          }
          blockParent = blockParent.parentNode;
        }

        if (blockParent && blockParent !== this.$refs.editor) {
          console.log('🎯 [图片插入] 找到块级父元素:', blockParent.nodeName);

          // 检查光标是否在块级元素的开头、中间或结尾
          const position = this.getCursorPositionInBlock(range, blockParent);
          console.log('🎯 [图片插入] 光标在块级元素中的位置:', position);

          if (position === 'start') {
            // 在块级元素前插入图片
            blockParent.parentNode.insertBefore(img, blockParent);
            console.log('✅ [图片插入] 图片插入到块级元素前');
          } else if (position === 'end') {
            // 在块级元素后插入图片
            blockParent.parentNode.insertBefore(img, blockParent.nextSibling);
            console.log('✅ [图片插入] 图片插入到块级元素后');
          } else {
            // 在中间位置，需要分割块级元素
            this.splitBlockAndInsertImage(range, blockParent, img);
          }
        } else {
          // 直接在编辑器中插入
          this.$refs.editor.appendChild(img);
          console.log('✅ [图片插入] 图片直接插入到编辑器');
        }
      } else {
        // 没有选择范围，追加到编辑器末尾
        this.$refs.editor.appendChild(img);
        console.log('✅ [图片插入] 图片追加到编辑器末尾');
      }
    },

    // 判断是否为块级元素
    isBlockElement(element) {
      const blockElements = ['P', 'DIV', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'BLOCKQUOTE', 'UL', 'OL', 'LI'];
      return blockElements.includes(element.nodeName);
    },

    // 获取光标在块级元素中的位置
    getCursorPositionInBlock(range, blockElement) {
      const blockText = blockElement.textContent || '';
      const beforeRange = document.createRange();
      beforeRange.setStart(blockElement, 0);
      beforeRange.setEnd(range.startContainer, range.startOffset);
      const beforeText = beforeRange.toString();

      if (beforeText.length === 0) {
        return 'start';
      } else if (beforeText.length === blockText.length) {
        return 'end';
      } else {
        return 'middle';
      }
    },

    // 分割块级元素并插入图片
    splitBlockAndInsertImage(range, blockElement, img) {
      console.log('🎯 [图片插入] 分割块级元素并插入图片');

      try {
        // 创建新的块级元素来容纳分割后的内容
        const newBlock = blockElement.cloneNode(false);

        // 提取光标后的内容
        const afterRange = document.createRange();
        afterRange.setStart(range.startContainer, range.startOffset);
        afterRange.setEnd(blockElement, blockElement.childNodes.length);
        const afterContent = afterRange.extractContents();

        // 如果后面有内容，放到新块中
        if (afterContent.textContent.trim() || afterContent.children.length > 0) {
          newBlock.appendChild(afterContent);
        }

        // 插入图片和新块
        blockElement.parentNode.insertBefore(img, blockElement.nextSibling);

        if (newBlock.textContent.trim() || newBlock.children.length > 0) {
          blockElement.parentNode.insertBefore(newBlock, img.nextSibling);
          console.log('✅ [图片插入] 分割完成，创建了新的块级元素');
        } else {
          console.log('✅ [图片插入] 分割完成，后续内容为空');
        }

      } catch (error) {
        console.error('❌ [图片插入] 分割块级元素时出错:', error);
        // 降级处理：直接在块级元素后插入
        blockElement.parentNode.insertBefore(img, blockElement.nextSibling);
        console.log('✅ [图片插入] 降级处理：插入到块级元素后');
      }
    },

    getDirectTextContent(element) {
      let text = '';
      for (let node of element.childNodes) {
        if (node.nodeType === Node.TEXT_NODE) {
          text += node.textContent;
        }
      }
      return text;
    },

    camelCase(str) {
      return str.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
    },

    updateJsonDisplay() {
      try {
        const displayData = {
          // 文档元数据
          title: this.documentData.title || '简单Word文档',
          jsonContent: this.documentData.content,
          // 页面设置
          pageSettings: this.pageSettings,

          metadata: this.documentData.metadata,
          // 导出配置
          exportConfig: {
            includeImages: true,
            imageFormat: 'base64',
            preserveLayout: true,
            maxImageWidth: 600,
            maxImageHeight: 800
          }
        };
        this.jsonDisplay = JSON.stringify(displayData, null, 2);
      } catch (error) {
        console.error('更新JSON显示时出错:', error);
        this.jsonDisplay = '{ "error": "JSON序列化失败" }';
      }
    },

    // JSON面板控制方法
    toggleJsonPanel() {
      this.showJsonPanel = !this.showJsonPanel;
    },

    formatJson() {
      try {
        const parsed = JSON.parse(this.jsonDisplay);
        this.jsonDisplay = JSON.stringify(parsed, null, 2);
        this.$message.success('JSON格式化完成');
      } catch (error) {
        this.$message.error('JSON格式错误，无法格式化');
      }
    },

    copyJson() {
      try {
        navigator.clipboard.writeText(this.jsonDisplay).then(() => {
          this.$message.success('JSON数据已复制到剪贴板');
        }).catch(() => {
          // 降级方案
          const textArea = document.createElement('textarea');
          textArea.value = this.jsonDisplay;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
          this.$message.success('JSON数据已复制到剪贴板');
        });
      } catch (error) {
        this.$message.error('复制失败');
      }
    },

    // 从JSON数据恢复内容
    loadFromJson(jsonData) {
      try {
        if (typeof jsonData === 'string') {
          jsonData = JSON.parse(jsonData);
        }
        this.documentData.content = jsonData.jsonContent;
        this.documentData.metadata = jsonData.metadata;
        this.documentData.title = jsonData.title;

        this.pageSettings = { ...jsonData.pageSettings };

        // 重建HTML内容
        const htmlContent = this.buildHtmlFromJson(jsonData.jsonContent);
        this.$refs.editor.innerHTML = htmlContent;

        this.updateJsonDisplay();
        this.$message.success('从JSON数据加载成功');
      } catch (error) {
        console.error('从JSON加载失败:', error);
        this.$message.error('JSON数据格式错误，加载失败');
      }
    },

    buildHtmlFromJson(contentArray) {
      if (!Array.isArray(contentArray)) return '';

      return contentArray.map(node => {
        let element = `<${node.type}`;

        // 添加属性
        if (node.attributes) {
          Object.entries(node.attributes).forEach(([key, value]) => {
            element += ` ${key}="${value}"`;
          });
        }

        // 添加样式
        if (node.styles && Object.keys(node.styles).length > 0) {
          const styleStr = Object.entries(node.styles)
            .map(([key, value]) => `${this.kebabCase(key)}: ${value}`)
            .join('; ');
          element += ` style="${styleStr}"`;
        }

        element += '>';

        // 添加内容
        if (node.children && node.children.length > 0) {
          element += this.buildHtmlFromJson(node.children);
        }
        if (node.content) {
          element += node.content;
        }

        element += `</${node.type}>`;
        return element;
      }).join('');
    },

    kebabCase(str) {
      return str.replace(/([A-Z])/g, '-$1').toLowerCase();
    },

    // 调试信息更新
    updateDebugInfo(action) {
      if (this.$refs.editor) {
        this.debugInfo.imageCount = this.$refs.editor.querySelectorAll('img').length;
        this.debugInfo.editorStatus = '正常';
      } else {
        this.debugInfo.editorStatus = '编辑器未找到';
      }
      this.debugInfo.lastAction = action + ' - ' + new Date().toLocaleTimeString();

      console.log('🔍 [调试信息] 更新:', this.debugInfo);
    },

    // JSON导入相关方法
    validateImportJson() {
      try {
        JSON.parse(this.importJsonText);
        this.$message.success('JSON格式验证通过');
      } catch (error) {
        this.$message.error('JSON格式错误: ' + error.message);
      }
    },

    importJsonData() {
      try {
        const jsonData = JSON.parse(this.importJsonText);
        this.loadFromJson(jsonData);
        this.showImportDialog = false;
        this.importJsonText = '';
      } catch (error) {
        this.$message.error('导入失败: ' + error.message);
      }
    },

    // 导出JSON数据到文件
    exportJsonToFile() {
      try {
        const jsonData = {
          // 文档元数据
          title: this.documentData.title || '简单Word文档',

          jsonContent: this.documentData.content,
          // 页面设置
          pageSettings: this.pageSettings,

          metadata: this.documentData.metadata,
          // 导出配置
          exportConfig: {
            includeImages: true,
            imageFormat: 'base64',
            preserveLayout: true,
            maxImageWidth: 600,
            maxImageHeight: 800
          }
        }

        const jsonStr = JSON.stringify(jsonData, null, 2);
        const blob = new Blob([jsonStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `word-document-${new Date().toISOString().slice(0, 10)}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        URL.revokeObjectURL(url);
        this.$message.success('JSON文件导出成功');
      } catch (error) {
        this.$message.error('导出失败: ' + error.message);
      }
    }

  }
}




</script>

<style scoped>
.word-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.json-panel {
  width: 400px;
  background: #ffffff;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
}

.json-header {
  padding: 10px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.json-header h4 {
  margin: 0;
  font-size: 14px;
  color: #333;
}

.json-textarea {
  flex: 1;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.json-textarea .el-textarea__inner {
  border: none;
  border-radius: 0;
  resize: none;
  background: #f8f9fa;
  color: #333;
  padding: 10px;
}

/* 调试面板样式 */
.debug-panel {
  padding: 10px;
  background: #fff3cd;
  border-top: 1px solid #ffeaa7;
  font-size: 12px;
}

.debug-panel h5 {
  margin: 0 0 10px 0;
  color: #856404;
  font-size: 13px;
}

.debug-info p {
  margin: 3px 0;
  color: #856404;
}

.debug-info strong {
  color: #6c5700;
}

.toolbar {
  background: linear-gradient(180deg, #124B9A, #0D438D);
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 8px 8px 0 0;
}

/* 工具栏按钮组样式 */
.toolbar .el-button-group .el-button {
  background: linear-gradient(0deg, #0096FF, #043475);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  margin: 0 2px;
  border-radius: 6px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar .el-button-group .el-button:hover {
  background: linear-gradient(0deg, #1aa7ff, #0d4f8a);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.toolbar .el-button-group .el-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 工具栏按钮激活状态 */
.toolbar .el-button-group .el-button.is-active {
  background: linear-gradient(0deg, #17d2ef, #0096FF);
  box-shadow: 0 0 12px rgba(23, 210, 239, 0.4);
}

/* 工具栏独立按钮样式 */
.toolbar .el-button {
  background: linear-gradient(0deg, #0096FF, #043475);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 6px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar .el-button:hover {
  background: linear-gradient(0deg, #1aa7ff, #0d4f8a);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.toolbar .el-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.editor-container {
  flex: 1;
  overflow: auto;
  position: relative;
  background: #f5f5f5;
}

.ruler {
  height: 20px;
  background: #f0f0f0;
  border-bottom: 1px solid #ddd;
  position: relative;
}

.ruler-numbers {
  position: relative;
  height: 100%;
}

.ruler-numbers span {
  position: absolute;
  top: 2px;
  font-size: 10px;
  color: #666;
}

.page-container {
  position: relative;
  background: white;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.page-header,
.page-footer {
  min-height: 30px;
  outline: none;
}

.editor-content {
  min-height: 600px;
  outline: none;
  word-wrap: break-word;
  font-size: initial; /* 重置字体大小，避免继承 */
}

.editor-content p {
  margin: 0 0 10px 0;
  font-size: 14px; /* 默认字体大小 */
}

.editor-content div {
  font-size: 14px; /* 默认字体大小 */
}

.editor-content h1, .editor-content h2, .editor-content h3,
.editor-content h4, .editor-content h5, .editor-content h6 {
  font-size: 14px; /* 默认字体大小，会被内联样式覆盖 */
}

.is-active {
  background-color: #409EFF !important;
  color: white !important;
}

/* 图片样式 */
.editor-content img {
  max-width: 100%;
  height: auto;
  cursor: move;
  border: 2px dashed transparent;
}

.editor-content img:hover {
  border-color: #409EFF;
}

.editor-content img.selected {
  border-color: #409EFF;
}

/* 调整大小控制点 */
.resize-handle {
  position: absolute;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  transition: all 0.2s ease;
}

.resize-handle:hover {
  transform: scale(1.2);
}

/* 四角控制点（等比例缩放） - 蓝色 */
.corner-handle {
  background: #409EFF !important;
  border: 2px solid #fff !important;
}

.corner-handle:hover {
  background: #66b1ff !important;
}

/* 四边控制点（不等比缩放） - 绿色 */
.edge-handle {
  background: #67C23A !important;
  border: 2px solid #fff !important;
}

.edge-handle:hover {
  background: #85ce61 !important;
}

/* 光标样式 */
.resize-nw { cursor: nw-resize; }
.resize-ne { cursor: ne-resize; }
.resize-sw { cursor: sw-resize; }
.resize-se { cursor: se-resize; }
.resize-n { cursor: n-resize; }
.resize-s { cursor: s-resize; }
.resize-e { cursor: e-resize; }
.resize-w { cursor: w-resize; }

/* 图片选中状态 */
.editor-content img.selected {
  border: 2px dashed #409EFF !important;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.3);
}

/* 图片缩放时的样式 */
.editor-content img.resizing {
  opacity: 0.8;
  transition: none;
}

/* 拖拽样式 */
.editor-content {
  position: relative;
}

.editor-content [draggable="true"] {
  user-select: none;
}

/* 工具栏选择框样式 */
.toolbar .el-select .el-input__inner {
  background: linear-gradient(0deg, #0D3A8D, #03256B);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 6px;
  backdrop-filter: blur(10px);
}

.toolbar .el-select .el-input__inner::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.toolbar .el-select .el-input__inner:focus {
  border-color: #17d2ef;
  box-shadow: 0 0 8px rgba(23, 210, 239, 0.3);
}

/* 工具栏数字输入框样式 */
.toolbar .el-input-number .el-input__inner {
  background: linear-gradient(0deg, #0D3A8D, #03256B);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 6px;
  backdrop-filter: blur(10px);
}

.toolbar .el-input-number .el-input__inner::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.toolbar .el-input-number .el-input__inner:focus {
  border-color: #17d2ef;
  box-shadow: 0 0 8px rgba(23, 210, 239, 0.3);
}

/* 工具栏颜色选择器样式 */
.toolbar .el-color-picker {
  background: linear-gradient(0deg, #0D3A8D, #03256B);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  backdrop-filter: blur(10px);
}

.toolbar .el-color-picker .el-color-picker__trigger {
  border: none;
  background: transparent;
}

/* 工具栏标签样式 */
.toolbar .el-tag {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
}

.toolbar .el-tag.el-tag--success {
  background: rgba(103, 194, 58, 0.2);
  border-color: rgba(103, 194, 58, 0.4);
}

.toolbar .el-tag.el-tag--info {
  background: rgba(144, 147, 153, 0.2);
  border-color: rgba(144, 147, 153, 0.4);
}

/* 工具栏增强毛玻璃效果 */
.toolbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
  border-radius: 8px 8px 0 0;
}

/* 工具栏下拉菜单样式 */
.toolbar .el-select-dropdown {
  background: linear-gradient(180deg, #124B9A, #0D438D);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.toolbar .el-select-dropdown .el-select-dropdown__item {
  background: transparent;
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.toolbar .el-select-dropdown .el-select-dropdown__item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #17d2ef;
}

.toolbar .el-select-dropdown .el-select-dropdown__item.selected {
  background: rgba(23, 210, 239, 0.2);
  color: #17d2ef;
}

/* 工具栏数字输入框增强样式 */
::v-deep .el-input-number .el-input-number__decrease,
::v-deep .el-input-number .el-input-number__increase {
  background: linear-gradient(0deg, #0096FF, #043475);
  border: 1px solid #043475FF;
  color: white;
  backdrop-filter: blur(10px);
}

.toolbar .el-input-number .el-input-number__decrease:hover,
.toolbar .el-input-number .el-input-number__increase:hover {
  background: linear-gradient(0deg, #1aa7ff, #0d4f8a);
  transform: scale(1.05);
}

/* 工具栏第二行增强样式 */
.toolbar .el-row:nth-child(2) {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

/* 工具栏分隔线效果 */
.toolbar .el-col:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 60%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
}

/* 页面容器响应式 */
@media (max-width: 768px) {
  .page-container {
    width: 100% !important;
    margin: 10px !important;
    padding: 10px !important;
  }

  .toolbar {
    padding: 8px 12px;
    border-radius: 6px 6px 0 0;
  }

  .toolbar .el-col {
    margin-bottom: 8px;
  }

  .toolbar .el-button {
    padding: 6px 12px;
    font-size: 12px;
  }

  .toolbar .el-button-group .el-button {
    margin: 0 1px;
    padding: 6px 10px;
  }

  .toolbar .el-select .el-input__inner {
    font-size: 12px;
    padding: 6px 8px;
  }

  .toolbar .el-input-number .el-input__inner {
    font-size: 12px;
    padding: 6px 8px;
  }

  .toolbar .el-color-picker {
    width: 32px !important;
    height: 32px !important;
  }

  .toolbar .el-tag {
    font-size: 11px;
    padding: 2px 6px;
  }

  /* 工具栏第二行在移动设备上的样式 */
  .toolbar .el-row:nth-child(2) {
    margin-top: 8px;
    padding-top: 8px;
  }

  /* 移动设备上隐藏分隔线 */
  .toolbar .el-col:not(:last-child)::after {
    display: none;
  }
}

/* 超小屏幕响应式 */
@media (max-width: 480px) {
  .toolbar {
    padding: 6px 8px;
  }

  .toolbar .el-col {
    margin-bottom: 6px;
    width: 100% !important;
  }

  .toolbar .el-button {
    padding: 4px 8px;
    font-size: 11px;
  }

  .toolbar .el-button-group .el-button {
    margin: 0 1px;
    padding: 4px 6px;
  }

  .toolbar .el-row {
    margin-bottom: 6px;
  }

  .toolbar .el-row:nth-child(2) {
    margin-top: 6px;
    padding-top: 6px;
  }

  /* 超小屏幕上简化工具栏布局 */
  .toolbar .el-col span {
    font-size: 10px;
  }
}

/* 横屏响应式 */
@media (max-width: 768px) and (orientation: landscape) {
  .toolbar {
    padding: 6px 12px;
  }

  .toolbar .el-col {
    margin-bottom: 4px;
  }

  .toolbar .el-button {
    padding: 4px 8px;
    font-size: 11px;
  }
}

/* 打印样式 */
@media print {
  .toolbar,
  .ruler {
    display: none;
  }

  .page-container {
    box-shadow: none;
    margin: 0;
  }

  .resize-handle {
    display: none;
  }
}

/* 帮助对话框样式 */
.help-content {
  line-height: 1.6;
}

.help-content h4 {
  color: #409EFF;
  margin: 15px 0 10px 0;
  font-size: 14px;
}

.help-content ul {
  margin: 0 0 15px 0;
  padding-left: 20px;
}

.help-content li {
  margin: 5px 0;
  font-size: 13px;
}

.help-content kbd {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 2px 6px;
  font-family: monospace;
  font-size: 12px;
  color: #333;
}

/* JSON导入对话框样式 */
.json-import-textarea .el-textarea__inner {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}
</style>
