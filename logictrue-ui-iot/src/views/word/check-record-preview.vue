<template>
  <div class="check-record-config">
    <div class="config-container">

      <!-- 表格渲染区域 -->
      <div class="show-panel">
        <div class="table-section">

          <!-- 页面管理区域 -->
          <div class="page-management">
            <div class="page-tabs">
              <div
                  v-for="page in pageList"
                  :key="`page-${page.id}`"
                  :class="['page-tab', { active: currentPageId === page.id }]"
                  @click="switchPage(page.id)"
              >
                <span class="page-name">{{ page.pageName }}</span>
                <button
                    v-if="pageList.length > 1"
                    @click.stop="deletePage(page.id)"
                    class="delete-page-btn"
                    title="删除页面"
                >
                  ×
                </button>
              </div>
              <button @click="addNewPage" class="add-page-btn" title="添加新页面">+</button>
            </div>

            <div class="page-actions">
              <div class="page-info">
                <span>当前页面：{{ currentPageId }}/{{ pageList.length }}</span>
              </div>
              <div class="page-controls">
                <button
                    @click="renamePage"
                    class="rename-btn"
                    title="重命名页面"
                >
                  重命名
                </button>
                <button
                    @click="sortPages"
                    class="sort-btn"
                    title="页面排序"
                >
                  排序
                </button>
              </div>
            </div>
          </div>

          <!-- 表格容器 -->
          <div class="table-container">
            <div class="table-actions">
              <button @click="saveTableData" class="save-btn">保存表格</button>
              <button @click="clearTableData" class="clear-btn">清空表格</button>
            </div>
            <TableContainer
                ref="tableContainer"
                :table-width="'100%'"
                :table-height="'600px'"
                :data-rows="tableData"
                @data-inserted="handleDataInserted"
                @table-updated="handleTableUpdated"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 保存结果提示 -->
    <div v-if="saveResult" class="save-result" :class="saveResult.success ? 'success' : 'error'">
      {{ saveResult.message }}
    </div>

    <!-- 页面重命名弹窗 -->
    <div v-if="showRenameDialog" class="dialog-overlay" @click="closeRenameDialog">
      <div class="dialog-content" @click.stop>
        <h3>重命名页面</h3>
        <div class="form-item">
          <label>页面名称：</label>
          <input
              v-model="newPageName"
              type="text"
              class="rename-input"
              @keyup.enter="confirmRename"
              ref="renameInput"
          >
        </div>
        <div class="dialog-actions">
          <button @click="confirmRename" class="confirm-btn">确定</button>
          <button @click="closeRenameDialog" class="cancel-btn">取消</button>
        </div>
      </div>
    </div>

    <!-- 页面排序弹窗 -->
    <div v-if="showSortDialog" class="dialog-overlay" @click="closeSortDialog">
      <div class="dialog-content sort-dialog" @click.stop>
        <h3>页面排序</h3>
        <div class="sort-list" v-if="sortablePages.length > 0">
          <draggable
              v-model="sortablePages"
              @start="dragStart"
              @end="dragEnd"
              handle=".drag-handle"
              ghost-class="sortable-ghost"
              drag-class="sortable-drag"
              chosen-class="sortable-chosen"
          >
            <div
              v-for="(element, index) in sortablePages"
              :key="getItemKey(element)"
              class="sort-item"
              :class="{ 'dragging': draggedIndex === index }"
            >
              <span class="drag-handle">⋮⋮</span>
              <span class="page-name">{{ element.pageName }}</span>
              <span class="page-order">顺序: {{ index + 1 }}</span>
            </div>
          </draggable>
        </div>
        <div v-else class="no-pages-message">
          暂无页面数据
        </div>
        <div class="dialog-actions">
          <button @click="confirmSort" class="confirm-btn">确定</button>
          <button @click="closeSortDialog" class="cancel-btn">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 引入draggable组件
import draggable from 'vuedraggable'
import TableContainer from '@/components/TableContainer.vue'
import { getCheckRecord } from '@/api/word/checkRecord'
import {
  saveOrUpdateDesignWord,
  getDesignWordByCarId,
  getDesignWordPagesByCarId,
  getDesignWordByCarIdAndPage,
  setActivePage,
  updateTotalPages,
  batchUpdatePageOrder,
  swapPageOrder
} from '@/api/word/designWord'
import mathFormulaUtils from '@/utils/math-formula-utils'

export default {
  name: 'CheckRecordConfig',
  components: {
    TableContainer,
    draggable
  },
  data() {
    return {
      // 搜索表单
      searchForm: {
        carId: '0822'
      },

      // 记录列表
      recordList: [],

      // 多选相关
      selectedRecords: [], // 选中的记录
      selectAll: false, // 全选状态

      // 分页信息
      pagination: {
        current: 1,
        size: 10,
        total: 0,
        pages: 0
      },

      // 表格数据
      tableData: [
        Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false
        }))
      ],

      // 表格配置
      tableConfig: null,

      // 保存结果
      saveResult: null,

      // 当前车辆的设计ID
      currentDesignId: null,

      // 多页相关数据
      pageList: [], // 页面列表
      currentPageId: 1, // 当前页面顺序

      // 页面重命名弹窗
      showRenameDialog: false,
      newPageName: '',

      // 页面排序弹窗
      showSortDialog: false,
      sortablePages: [],
      draggedIndex: -1,
      isSorting: false,

      // 可用列索引选项
      availableFields: [
        { value: 0, label: '第1列 - 检查工序名称' },
        { value: 1, label: '第2列 - 检查项目及技术条件' },
        { value: 2, label: '第3列 - 实际检查结果' },
        { value: 3, label: '第4列 - 完工-月' },
        { value: 4, label: '第5列 - 完工-日' },
        { value: 5, label: '第6列 - 操作员' },
        { value: 6, label: '第7列 - 班组长' },
        { value: 7, label: '第8列 - 检验员' },
        { value: 8, label: '第9列' },
        { value: 9, label: '第10列' }
      ],

      // 行与模板ID的绑定关系 {rowIndex: template_id}
      rowTemplateBindings: {},

      // 绑定的模板ID列表（按插入顺序，逗号分隔的字符串）
      boundTemplateIds: '',

    }
  },
  mounted() {
    this.initializeTable()
    this.loadPageList("0822")
  },
  methods: {
    /**
     * 初始化表格配置
     */
    initializeTable() {
      // 设置默认表格配置
      this.tableConfig = {
        title: "检验记录表",
        headers: [
          [
            "检查工序\n名称",
            "检 查 项 目 及 技 术 条 件",
            "实 际 检 查 结 果",
            "完工",
            "",
            "操作员",
            "班组长",
            "检验员"
          ],
          [
            "",
            "",
            "",
            "月",
            "日",
            "",
            "",
            ""
          ]
        ],
        headerMerges: [
          {
            startRow: 0,
            startCol: 0,
            endRow: 1,
            endCol: 0,
            content: "检查工序\n名称"
          },
          {
            startRow: 0,
            startCol: 1,
            endRow: 1,
            endCol: 1,
            content: "检 查 项 目 及 技 术 条 件"
          },
          {
            startRow: 0,
            startCol: 2,
            endRow: 1,
            endCol: 2,
            content: "实 际 检 查 结 果"
          },
          {
            startRow: 0,
            startCol: 3,
            endRow: 0,
            endCol: 4,
            content: "完工"
          },
          {
            startRow: 0,
            startCol: 5,
            endRow: 1,
            endCol: 5,
            content: "操作员"
          },
          {
            startRow: 0,
            startCol: 6,
            endRow: 1,
            endCol: 6,
            content: "班组长"
          },
          {
            startRow: 0,
            startCol: 7,
            endRow: 1,
            endCol: 7,
            content: "检验员"
          }
        ],
        headerWidthConfig: {
          columnWidths: [100, 460, 160, 32, 32, 32, 32, 32],
          headerHeights: [35, 35]
        },
        verticalHeadersConfig: [false, false, false, false, false, true, true, true]
      }

      // 应用表格配置
      this.$nextTick(() => {
        this.applyTableConfig()
      })
    },

    /**
     * 应用表格配置
     */
    applyTableConfig() {
      const tableContainer = this.$refs.tableContainer
      if (tableContainer && this.tableConfig) {
        tableContainer.setDynamicHeaderConfig(
            true,
            {
              headers: this.tableConfig.headers,
              merges: this.tableConfig.headerMerges
            },
            this.tableConfig.headerWidthConfig,
            this.tableConfig.verticalHeadersConfig
        )
      }
    },

    /**
     * 加载页面列表
     */
    async loadPageList(carId) {
      try {
        const response = await getDesignWordPagesByCarId(carId)
        if (response.code === 200 && response.data) {
          this.pageList = response.data.sort((a, b) => a.pageOrder - b.pageOrder)

          // 如果没有页面，创建默认页面
          if (this.pageList.length === 0) {
            this.pageList = [{
              id: null,
              carId: carId,
              pageName: '第1页',
              pageOrder: 1,
              totalPages: 1,
              isActive: 1
            }]
            this.currentPageId = 1
          } else {
            // 找到当前活动页面
            const activePage = this.pageList.find(page => page.isActive === 1)
            this.currentPageId = activePage ? activePage.id : this.pageList[0].id
          }
          console.log('页面列表加载完成:', this.pageList)
        } else {
          console.warn('获取页面列表失败，使用默认页面')
          // 创建默认页面
          this.pageList = [{
            id: null,
            carId: carId,
            pageName: '第1页',
            pageOrder: 1,
            totalPages: 1,
            isActive: 1
          }]
          this.currentPageId = 1
        }
      } catch (error) {
        console.error('加载页面列表失败:', error)
        // 创建默认页面
        this.pageList = [{
          id: null,
          carId: carId,
          pageName: '第1页',
          pageOrder: 1,
          totalPages: 1,
          isActive: 1
        }]
        this.currentPageId = 1
      }
    },

    /**
     * 加载当前页面数据
     */
    async loadCurrentPageData() {

      try {
        const response = await getDesignWordByCarIdAndPage(this.searchForm.carId, this.currentPageId)
        if (response.code === 200 && response.data) {
          const designData = response.data
          this.currentDesignId = designData.id

          // 解析表格配置
          if (designData.tableConfig) {
            this.tableConfig = JSON.parse(designData.tableConfig)
            this.applyTableConfig()
          }

          // 解析表格数据
          if (designData.tableData) {
            const tableData = JSON.parse(designData.tableData)

            // 检查是否是新的完整格式（包含合并信息）
            let cellRowsData = null
            let mergeData = []

            if (tableData && typeof tableData === 'object' && tableData.cellRows) {
              // 新格式：包含完整信息的对象
              cellRowsData = tableData.cellRows
              mergeData = tableData.merges || []
              console.log('加载新格式数据，包含合并信息:', mergeData.length, '个合并单元格')
            } else if (tableData && Array.isArray(tableData)) {
              // 旧格式：直接是cellRows数组
              cellRowsData = tableData
              console.log('加载旧格式数据，无合并信息')
            }

            if (cellRowsData && Array.isArray(cellRowsData)) {
              // 检查数据格式，支持新旧格式
              let processedData = cellRowsData

              // 如果是cellRows格式（包含行高信息），转换为dataRows格式
              if (cellRowsData.length > 0 && cellRowsData[0].length > 0 &&
                  typeof cellRowsData[0][0] === 'object' && cellRowsData[0][0].hasOwnProperty('content')) {
                console.log('加载cellRows格式数据，包含行高信息')
                processedData = cellRowsData.map(row =>
                    row.map(cell => {
                      // 回显时优先展示originContent
                      const displayContent = cell.originContent || cell.content || ''
                      console.log('单元格数据回显:', {
                        原始content: cell.content,
                        originContent: cell.originContent,
                        最终显示: displayContent,
                        hasNestedTable: !!(cell.nestedTable)
                      })
                      const cellData = {
                        content: displayContent,
                        originContent: cell.originContent || cell.content || '',
                        isEditing: false,
                        originalContent: displayContent,
                        hasMath: cell.hasMath || false,
                        mathML: cell.mathML || null,
                        width: cell.width,
                        height: cell.height,
                        hasMultipleContent: cell.hasMultipleContent || false,
                        mathMLMap: cell.mathMLMap || null
                      }

                      // 重要：保留嵌套表格配置
                      if (cell.nestedTable) {
                        cellData.nestedTable = cell.nestedTable
                        console.log('回显时保留嵌套表格配置:', cell.nestedTable)
                      }

                      return cellData
                    })
                )
              } else {
                console.log('加载简单格式数据，转换为标准格式')
                // 旧格式，转换为标准格式
                processedData = cellRowsData.map(row =>
                    row.map(cellContent => ({
                      content: cellContent || '',
                      originContent: cellContent || '',
                      isEditing: false,
                      originalContent: cellContent || '',
                      hasMath: false
                    }))
                )
              }

              this.tableData = processedData

              // 使用insertDataFromJSON方法正确应用高度配置和合并信息
              const tableContainer = this.$refs.tableContainer
              if (tableContainer) {
                // 检查是否有高度信息需要应用
                const hasHeightInfo = cellRowsData.length > 0 && cellRowsData[0].length > 0 &&
                    typeof cellRowsData[0][0] === 'object' && cellRowsData[0][0].hasOwnProperty('height')

                if (hasHeightInfo || mergeData.length > 0) {
                  console.log('使用insertDataFromJSON方法应用高度配置和合并信息')
                  // 准备cellRows格式的数据，回显时优先使用originContent
                  const insertData = {
                    cellRows: cellRowsData.map(row =>
                        row.map(cell => {
                          // 回显时优先展示originContent
                          const displayContent = cell.originContent || cell.content || ''
                          const cellData = {
                            content: displayContent,
                            originContent: cell.originContent || cell.content || '',
                            hasMath: cell.hasMath || false,
                            mathML: cell.mathML || null,
                            hasMultipleContent: cell.hasMultipleContent || false,
                            mathMLMap: cell.mathMLMap || null,
                            width: cell.width,
                            height: cell.height
                          }

                          // 重要：保留嵌套表格配置
                          if (cell.nestedTable) {
                            cellData.nestedTable = cell.nestedTable
                            console.log('回显时保留嵌套表格配置:', cell.nestedTable)
                          }

                          return cellData
                        })
                    )
                  }

                  // 使用insertDataFromJSON方法插入数据，应用高度配置和合并信息
                  const result = tableContainer.insertDataFromJSON(insertData, {
                    clearExisting: true,
                    validateData: false,
                    mergeCells: mergeData // 应用合并单元格信息
                  })

                  if (result.success) {
                    console.log('高度配置和合并信息应用成功，合并单元格数量:', mergeData.length)
                  } else {
                    console.error('高度配置和合并信息应用失败:', result.message)
                    // 回退到直接设置dataRows
                    tableContainer.dataRows = processedData
                  }
                } else {
                  // 没有高度信息和合并信息，直接设置dataRows
                  tableContainer.dataRows = processedData
                }
              }
            }
          } else {
            // 没有数据，初始化空表格
            this.initializeEmptyTable()
          }
        } else {
          // 没有找到页面数据，初始化空表格
          this.initializeEmptyTable()
          this.rowTemplateBindings = {}
          this.boundTemplateIds = ''
        }
      } catch (error) {
        console.error('加载当前页面数据失败:', error)
        this.initializeEmptyTable()
      }
    },

    /**
     * 初始化空表格
     */
    initializeEmptyTable() {
      // 创建完全空的表格（0行数据）
      this.tableData = []

      const tableContainer = this.$refs.tableContainer
      if (tableContainer) {
        // 直接设置为空数组，避免clearAllData自动添加空行
        tableContainer.dataRows = []

        // 清除所有合并单元格
        if (typeof tableContainer.clearAllMerges === 'function') {
          tableContainer.clearAllMerges()
        }

        // 重置内部状态
        if (tableContainer.internalRowHeights) {
          tableContainer.internalRowHeights = {}
        }

        console.log('空表格初始化完成，数据行数:', tableContainer.dataRows.length)
      }

      // 重置相关数据
      this.currentDesignId = null
      this.rowTemplateBindings = {}
      this.boundTemplateIds = ''
    },


    /**
     * 保存表格数据
     */
    async saveTableData() {
      try {
        const tableContainer = this.$refs.tableContainer
        if (!tableContainer) {
          this.showSaveResult(false, '表格组件未找到')
          return
        }

        // 获取完整的表格数据，包含行高信息和合并信息
        const fullTableData = tableContainer.getDataAsJSON({
          includeEmpty: true,
          includeMergeInfo: true
        })

        // 对表格数据进行转换处理（参考LaTeX转换方法）
        const processedCellRows = await this.processTableDataForSave(fullTableData.cellRows)

        // 构建完整的保存数据结构，包含合并信息
        const completeTableData = {
          cellRows: processedCellRows,
          merges: fullTableData.merges || [], // 保存合并单元格信息
          headerConfig: fullTableData.headerConfig,
          headerWidthConfig: fullTableData.headerWidthConfig,
          metadata: fullTableData.metadata
        }

        // 获取当前页面信息
        const currentPage = this.pageList.find(page => page.id === this.currentPageId)

        // 构建保存数据
        const saveData = {
          id: this.currentDesignId,
          carId: this.searchForm.carId,
          title: this.tableConfig.title || '检验记录表',
          tableConfig: JSON.stringify(this.tableConfig),
          tableData: JSON.stringify(completeTableData), // 保存完整的表格数据，包含合并信息
          fieldBindingConfig: JSON.stringify(this.fieldBindingConfig), // 保存字段绑定配置
          rowTemplateBindings: JSON.stringify(this.rowTemplateBindings || {}), // 保存行与模板ID的绑定关系
          boundTemplateIds: this.boundTemplateIds || null, // 直接使用已拼接的模板ID列表
          status: 1,
          pageName: currentPage ? currentPage.pageName : `第${this.currentPageId}页`,
          pageOrder: this.currentPageId,
          totalPages: this.pageList.length,
          isActive: 1
        }

        console.log('保存的表格数据:', {
          tableConfig: this.tableConfig,
          originalCellRows: fullTableData.cellRows,
          processedCellRows: processedCellRows,
          merges: fullTableData.merges,
          rowCount: processedCellRows.length,
          pageInfo: {
            pageName: saveData.pageName,
            pageOrder: saveData.pageOrder,
            totalPages: saveData.totalPages
          }
        })

        const response = await saveOrUpdateDesignWord(saveData)
        if (response.code === 200) {
          this.showSaveResult(true, '表格保存成功')
          // 更新当前设计ID
          if (!this.currentDesignId && response.data) {
            this.currentDesignId = response.data
          }

          // 设置为活动页面
          await setActivePage(this.searchForm.carId, this.currentPageId)

          // 重新加载页面列表
          await this.loadPageList(this.searchForm.carId)

        } else {
          this.showSaveResult(false, response.msg || '表格保存失败')
        }

      } catch (error) {
        console.error('保存表格数据失败:', error)
        this.showSaveResult(false, '保存表格数据失败')
      }
    },

    /**
     * 切换页面
     */
    async switchPage(pageId) {
      if (pageId === this.currentPageId) {
        return
      }

      this.currentPageId = pageId
      await this.loadCurrentPageData()

      // 设置为活动页面
      try {
        await setActivePage(this.searchForm.carId, pageId)
      } catch (error) {
        console.error('设置活动页面失败:', error)
      }
    },

    /**
     * 添加新页面
     */
    async addNewPage() {
      if (!this.searchForm.carId) {
        this.showSaveResult(false, '请先选择车辆ID')
        return
      }

      const newPageOrder = this.pageList.length + 1
      const newPageName = `第${newPageOrder}页`

      // 添加到页面列表
      this.pageList.push({
        id: null,
        carId: this.searchForm.carId,
        pageName: newPageName,
        pageOrder: newPageOrder,
        totalPages: newPageOrder,
        isActive: 0
      })

      // 更新所有页面的总页数
      try {
        await updateTotalPages(this.searchForm.carId, newPageOrder)
      } catch (error) {
        console.error('更新总页数失败:', error)
      }

      // 切换到新页面
      this.currentPageId = newPageOrder
      this.currentDesignId = null
      this.initializeEmptyTable()

      this.showSaveResult(true, `已添加${newPageName}`)
    },

    /**
     * 删除页面
     */
    async deletePage(pageId) {
      if (this.pageList.length <= 1) {
        this.showSaveResult(false, '至少需要保留一个页面')
        return
      }

      if (!confirm(`确定要删除该页吗？`)) {
        return
      }

      try {
        // 找到要删除的页面
        const pageToDelete = this.pageList.find(page => page.id === pageId)
        if (pageToDelete && pageToDelete.id) {
          // 如果页面已保存，从数据库删除
          // 这里需要调用删除接口，暂时跳过
          console.log('需要从数据库删除页面:', pageToDelete.id)
        }

        // 从页面列表中移除
        this.pageList = this.pageList.filter(page => page.id !== pageId)

        // 重新排序页面
        this.pageList.forEach((page, index) => {
          page.pageOrder = index + 1
          page.pageName = `第${index + 1}页`
        })

        // 如果删除的是当前页面，切换到第一页
        if (this.currentPageId === pageId) {
          this.currentPageId = 1
          await this.loadCurrentPageData()
        } else if (this.currentPageId > pageId) {
          // 如果删除的页面在当前页面之前，调整当前页面序号
          this.currentPageId -= 1
        }

        // 更新总页数
        await updateTotalPages(this.searchForm.carId, this.pageList.length)

        this.showSaveResult(true, '页面删除成功')

      } catch (error) {
        console.error('删除页面失败:', error)
        this.showSaveResult(false, '删除页面失败')
      }
    },

    /**
     * 处理表格数据用于保存（参考LaTeX转换方法）
     * @param {Array} cellRows - 原始单元格数据
     * @returns {Promise<Array>} 处理后的单元格数据
     */
    async processTableDataForSave(cellRows) {
      if (!Array.isArray(cellRows)) {
        return cellRows
      }

      console.log('开始处理表格数据进行转换...')
      const processedRows = []

      for (let rowIndex = 0; rowIndex < cellRows.length; rowIndex++) {
        const row = cellRows[rowIndex]
        const processedRow = []

        for (let cellIndex = 0; cellIndex < row.length; cellIndex++) {
          const cell = row[cellIndex]
          const processedCell = { ...cell }

          // 如果单元格有内容，进行转换处理
          if (cell.content && typeof cell.content === 'string') {
            try {
              // 检测是否包含数学公式
              if (mathFormulaUtils.containsMath(cell.content)) {
                console.log(`检测到数学公式内容 [${rowIndex},${cellIndex}]:`, cell.content)

                // 分离公式和普通文本
                const separationResult = mathFormulaUtils.separateFormulaAndText(cell.content)

                if (separationResult.hasFormula) {
                  console.log('分离结果:', separationResult)

                  // 处理每个公式，转换为MathML
                  const mathMLMap = new Map()

                  for (const formula of separationResult.formulas) {
                    try {
                      const mathML = await mathFormulaUtils.latexToMathML(formula.content)
                      if (mathML) {
                        mathMLMap.set(formula.placeholder, mathML)
                        console.log(`公式 ${formula.placeholder} 转换成功`)
                      } else {
                        console.warn(`公式 ${formula.placeholder} 转换失败`)
                      }
                    } catch (error) {
                      console.error(`处理公式 ${formula.placeholder} 失败:`, error)
                    }
                  }

                  // 设置处理结果
                  processedCell.hasMath = true
                  processedCell.hasMultipleContent = separationResult.formulas.length > 1 ||
                      separationResult.processedContent.replace(/__MATH_FORMULA_\d+__/g, '').trim().length > 0
                  processedCell.content = separationResult.processedContent // 包含占位符的内容
                  processedCell.originContent = cell.content // 保存原始内容
                  processedCell.mathMLMap = Object.fromEntries(mathMLMap) // 占位符到MathML的映射
                  processedCell.formulas = separationResult.formulas // 公式信息

                  console.log(`单元格 [${rowIndex},${cellIndex}] 转换完成:`, {
                    content: processedCell.content,
                    mathMLMap: processedCell.mathMLMap,
                    hasMultipleContent: processedCell.hasMultipleContent
                  })
                } else if (mathFormulaUtils.containsMath(cell.content)) {
                  // 纯公式内容的处理（保持原有逻辑兼容性）
                  console.log(`检测到纯公式内容 [${rowIndex},${cellIndex}]:`, cell.content)

                  const mathML = await mathFormulaUtils.latexToMathML(cell.content)

                  if (mathML) {
                    processedCell.hasMath = true
                    processedCell.mathML = mathML
                    processedCell.originContent = cell.content // 保存原始内容
                    console.log(`纯公式 [${rowIndex},${cellIndex}] 转换成功`)
                  } else {
                    console.warn(`纯公式 [${rowIndex},${cellIndex}] 转换失败，保持原有标记`)
                    processedCell.hasMath = true
                    processedCell.originContent = cell.content
                  }
                }
              } else {
                // 普通文本内容，保存原始内容
                processedCell.originContent = cell.content
              }

            } catch (error) {
              console.error(`处理单元格 [${rowIndex},${cellIndex}] 失败:`, error)
              // 出错时保持原始数据
              processedCell.originContent = cell.content
            }
          }

          processedRow.push(processedCell)
        }

        processedRows.push(processedRow)
      }

      console.log('表格数据转换处理完成，处理行数:', processedRows.length)
      return processedRows
    },

    /**
     * 清空表格数据
     */
    clearTableData() {
      if (!confirm('确定要清空表格数据吗？此操作不可撤销。')) {
        return
      }

      // 创建完全空的表格（0行数据）
      this.tableData = []

      const tableContainer = this.$refs.tableContainer
      if (tableContainer) {
        // 直接设置为空数组，避免clearAllData自动添加空行
        tableContainer.dataRows = []

        // 清除所有合并单元格
        if (typeof tableContainer.clearAllMerges === 'function') {
          tableContainer.clearAllMerges()
        }

        // 重置内部状态
        if (tableContainer.internalRowHeights) {
          tableContainer.internalRowHeights = {}
        }

        console.log('表格已清空，数据行数:', tableContainer.dataRows.length)
      }

      this.showSaveResult(true, '表格已清空')
    },

    /**
     * 显示保存结果
     */
    showSaveResult(success, message) {
      this.saveResult = { success, message }
      setTimeout(() => {
        this.saveResult = null
      }, 3000)
    },

    /**
     * 处理数据插入事件
     */
    handleDataInserted(event) {
      console.log('数据插入事件:', event)
    },

    /**
     * 处理表格更新事件
     */
    handleTableUpdated() {
      console.log('表格更新事件')
    },

    /**
     * 重命名页面
     */
    renamePage() {
      const currentPage = this.pageList.find(page => page.id === this.currentPageId)
      this.newPageName = currentPage ? currentPage.pageName : `第${this.currentPageId}页`
      this.showRenameDialog = true

      this.$nextTick(() => {
        if (this.$refs.renameInput) {
          this.$refs.renameInput.focus()
          this.$refs.renameInput.select()
        }
      })
    },

    /**
     * 确认重命名
     */
    async confirmRename() {
      if (!this.newPageName.trim()) {
        this.showSaveResult(false, '页面名称不能为空')
        return
      }

      // 更新页面列表中的名称
      const currentPage = this.pageList.find(page => page.id === this.currentPageId)
      if (currentPage) {
        currentPage.pageName = this.newPageName.trim()
        this.showSaveResult(true, '页面重命名成功')
      }

      this.closeRenameDialog()
    },

    /**
     * 关闭重命名弹窗
     */
    closeRenameDialog() {
      this.showRenameDialog = false
      this.newPageName = ''
    },

    /**
     * 页面排序
     */
    sortPages() {
      // 确保页面列表有数据
      if (!this.pageList || this.pageList.length === 0) {
        console.warn('页面列表为空，无法排序')
        this.showSaveResult(false, '页面列表为空，无法排序')
        return
      }

      console.log('原始页面列表:', this.pageList)

      // 深拷贝页面列表并为每个页面添加唯一标识
      this.sortablePages = JSON.parse(JSON.stringify(this.pageList)).map(page => ({
        ...page,
        uniqueKey: page.id ? `page-${page.id}` : `page-temp-${page.pageOrder}-${Math.random().toString(36).substr(2, 9)}`
      }))

      console.log('初始化排序对话框，页面数据:', this.sortablePages)
      console.log('sortablePages 长度:', this.sortablePages.length)

      // 显示弹窗
      this.showSortDialog = true
    },

    /**
     * 获取拖拽项的唯一键
     */
    getItemKey(item) {
      return item.uniqueKey || item.id || item.pageOrder
    },

    /**
     * 拖拽开始
     */
    dragStart(event) {
      this.draggedIndex = event.oldIndex
    },

    /**
     * 拖拽结束
     */
    dragEnd() {
      this.draggedIndex = -1
      console.log('拖拽结束')
    },

    /**
     * 确认排序
     */
    async confirmSort() {
      try {
        console.log('确认排序，sortablePages:', this.sortablePages)

        // 更新页面顺序
        const updatedPages = this.sortablePages.map((page, index) => ({
          ...page,
          pageOrder: index + 1
        }))

        // 调用API批量更新页面顺序
        const response = await batchUpdatePageOrder(updatedPages)

        if (response.code === 200) {
          // 重新加载页面列表
          await this.loadPageList(this.searchForm.carId)
          this.showSaveResult(true, '页面排序成功')
        } else {
          throw new Error(response.msg || '页面排序失败')
        }

        this.closeSortDialog()
      } catch (error) {
        console.error('确认排序失败:', error)
        this.showSaveResult(false, `确认排序失败: ${error.message || '未知错误'}`)
      }
    },

    /**
     * 关闭排序弹窗
     */
    closeSortDialog() {
      this.showSortDialog = false
      this.sortablePages = []
      this.draggedIndex = -1
    },


  }
}
</script>

<style scoped>
.check-record-config {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.config-container {
  display: flex;
  gap: 20px;
  height: calc(100vh - 40px);
}


.form-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-item label {
  min-width: 80px;
  font-weight: 500;
  color: #333;
}
.data-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.data-table th,
.data-table td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.data-table th {
  background: #f8f9fa;
  font-weight: 500;
  color: #333;
  position: sticky;
  top: 0;
  z-index: 1;
}

.data-table tr:hover {
  background: #f8f9fa;
}

.data-table tr.selected-row {
  background: #e3f2fd;
}

.data-table tr.selected-row:hover {
  background: #bbdefb;
}


.page-info {
  color: #666;
  font-size: 14px;
}

/* 右侧面板 */
.show-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.table-section {
  flex: 1;
  display: flex;
  flex-direction: row;
  gap: 15px;
}



.table-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin: 10px;
}

.save-btn, .clear-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.save-btn {
  background: #28a745;
  color: white;
}

.save-btn:hover {
  background: #218838;
}

.clear-btn {
  background: #dc3545;
  color: white;
}

.clear-btn:hover {
  background: #c82333;
}

.table-container {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  min-width: 0;
  padding: 0 30px;
}

/* 页面管理样式 */
.page-management {
  width: 300px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 80px);
}

.page-tabs::-webkit-scrollbar {
  width: 6px;
}

.page-tabs::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.page-tabs::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.page-tabs::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.page-tabs {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 10px;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
}

.page-tab {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s ease;
  user-select: none;
  min-height: 40px;
}

.page-tab:hover {
  background: #f0f0f0;
  border-color: #007bff;
}

.page-tab.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.page-tab .page-name {
  font-weight: 500;
}

.delete-page-btn {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.delete-page-btn:hover {
  background: #dc3545;
  color: white;
}

.page-tab.active .delete-page-btn {
  color: rgba(255, 255, 255, 0.8);
}

.page-tab.active .delete-page-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.add-page-btn {
  padding: 8px 12px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.2s ease;
  width: 100%;
  justify-content: center;
}

.add-page-btn:hover {
  background: #218838;
}

.page-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
  position: sticky;
  bottom: 0;
  background: #f8f9fa;
}

.page-info {
  color: #666;
  font-size: 13px;
}

.page-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rename-btn, .sort-btn, .field-binding-btn {
  padding: 8px 12px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  width: 100%;
  text-align: center;
}

.rename-btn:hover, .sort-btn:hover {
  background: #545b62;
}

/* 保存结果提示 */
.save-result {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

.save-result.success {
  background: #28a745;
}

.save-result.error {
  background: #dc3545;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 弹窗样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.dialog-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  min-width: 400px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.dialog-content h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
}

.dialog-content .form-item {
  margin-bottom: 15px;
}

.dialog-content .form-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.rename-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.rename-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.confirm-btn, .cancel-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.confirm-btn {
  background: #007bff;
  color: white;
}

.confirm-btn:hover {
  background: #0056b3;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #545b62;
}

/* 排序列表样式 */
.sort-list {
  max-height: 300px;
  min-height: 100px;
  overflow-y: auto;
  margin-bottom: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px;
}

.sort-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin-bottom: 5px;
  cursor: move;
  transition: all 0.2s ease;
  color: #333;
}

.sort-item:hover {
  background: #e9ecef;
  border-color: #007bff;
}

.sort-item:last-child {
  margin-bottom: 0;
}

.drag-handle {
  color: #6c757d;
  font-size: 16px;
  cursor: grab;
}

.drag-handle:active {
  cursor: grabbing;
}

.sort-item .page-name {
  flex: 1;
  font-weight: 500;
  color: #333;
}

.sort-item .page-order {
  color: #666;
  font-size: 13px;
}

.sort-item.sorting {
  opacity: 0.7;
  pointer-events: none;
}

.sort-item.dragging {
  opacity: 0.5;
  background: #e3f2fd;
  border-color: #2196f3;
}

/* 无页面数据提示 */
.no-pages-message {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
  border: 1px dashed #ddd;
  border-radius: 4px;
  margin-bottom: 15px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .config-container {
    flex-direction: column;
    height: auto;
  }

  .table-section {
    flex-direction: column;
  }

  .page-tabs {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .page-actions {
    flex-direction: row;
    justify-content: space-between;
  }

  .page-controls {
    flex-direction: row;
  }

  .add-page-btn,
  .rename-btn,
  .sort-btn,
  .field-binding-btn {
    width: auto;
  }

  .data-table {
    max-height: 400px;
  }

  .table-container {
    height: 500px;
  }
}

@media (max-width: 768px) {
  .page-tabs {
    flex-direction: column;
  }

  .page-actions {
    flex-direction: column;
  }

  .page-controls {
    flex-direction: column;
  }

  .add-page-btn,
  .rename-btn,
  .sort-btn,
  .field-binding-btn {
    width: 100%;
  }
}

.binding-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.binding-description p {
  margin: 0;
  color: #1976d2;
  font-size: 14px;
}
.field-info .field-label {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.field-desc {
  font-size: 12px;
  color: #666;
}

.binding-config-item .field-mapping {
  flex: 1;
  min-width: 250px;
}

.binding-config-item .binding-select {
  width: 100%;
  padding: 6px 10px;
  font-size: 13px;
}

.binding-config-item .current-mapping {
  white-space: nowrap;
  font-size: 11px;
}
/* 添加排序对话框样式 */
.sort-dialog {
  width: 500px;
  max-width: 90%;
}

.sort-list {
  max-height: 300px;
  overflow-y: auto;
  margin: 15px 0;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.sort-item {
  display: flex;
  align-items: center;
  padding: 10px;
  background: #f9f9f9;
  border-bottom: 1px solid #eee;
  cursor: move;
}

.sort-item:last-child {
  border-bottom: none;
}

.sort-item.dragging {
  background: #e3f2fd;
}

.drag-handle {
  margin-right: 10px;
  cursor: grab;
  color: #666;
}

.drag-handle:active {
  cursor: grabbing;
}

.page-name {
  flex: 1;
  font-weight: bold;
}

.page-order {
  color: #666;
  font-size: 0.9em;
}

/* vuedraggable 拖拽效果样式 */
.sortable-ghost {
  opacity: 0.4;
  background: #f0f0f0;
  border: 2px dashed #2196f3;
}

.sortable-drag {
  opacity: 0.8;
  background: #e3f2fd;
  border: 1px solid #2196f3;
  transform: rotate(5deg);
}

.sortable-chosen {
  background: #fff3e0;
  border: 1px solid #ff9800;
}

</style>
